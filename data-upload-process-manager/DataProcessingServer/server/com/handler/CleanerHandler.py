from server import thisfolder, os, config, pd, re, venv, platform, subprocess, glob, csv, logger, pd
import numpy as np

from server.com.handler.ValidationHandler import ValidationHandler
from server.com.handler.LoggerHandler import <PERSON><PERSON><PERSON>and<PERSON>
from server.com.handler.DBHandler import create_connection_with_ssms

log = LoggerHandler()


class CleanerHandler(ValidationHandler):

    def process_product_sheet(self, df):
        try:
            log.request_logger.info("Process the product-related columns.")
            self.info(logs="Process the product-related columns.")

            # ✅ Check for mandatory column: SupplierCatalogID
            if 'SupplierCatalogId' not in df.columns:
                error_message = "The provided sheet does not contain the 'SupplierCatalogId' column."
                self.error_log(logs=error_message)
                log.request_logger.error(error_message)
                return {"success": False, "error": error_message}

            # Define validation functions
            def cas_validation(cas):
                try:
                    cas_match = re.fullmatch(r'(\d+)-(\d\d)-(\d)', cas)
                    if not cas_match:
                        return np.nan
                    main_part = cas_match.group(1) + cas_match.group(2)
                    check_digit = int(cas_match.group(3))
                    sum_cas = sum(int(num) * (idx + 1) for idx, num in enumerate(reversed(main_part)))
                    return cas if sum_cas % 10 == check_digit else np.nan
                except Exception:
                    return np.nan

            def mdl_validation(mdl):
                try:
                    pattern = r'^MFCD[A-Za-z0-9]{8}$'
                    return mdl if re.match(pattern, mdl) else np.nan
                except Exception:
                    return np.nan

            def purity_validation(purity):
                try:
                    pattern = r'^[+-]?\d{1,3}(\.\d+)?%$|^[+-]?\d{1,3}(\.\d+)?\+?%(\s*-\s*[+-]?\d{1,3}(\.\d+)?%)*$'
                    match = re.match(pattern, purity)
                    return purity.split(" - ")[1].strip() if match and " - " in purity else purity if match else np.nan
                except Exception:
                    return np.nan

            # ✅ Create a DataFrame for tracking old and new values
            validation_df = pd.DataFrame()
            df = df.drop_duplicates(subset='SupplierCatalogId', keep='first')
            validation_df['SupplierCatalogId'] = df['SupplierCatalogId']

            # ✅ Apply validations if columns exist
            if 'MDLNumber' in df.columns:
                validation_df['old_mdl'] = df['MDLNumber']
                df['MDLNumber'] = df['MDLNumber'].astype(str).apply(mdl_validation)
                validation_df['new_mdl'] = df['MDLNumber']

            if 'CASNumber' in df.columns:
                validation_df['old_cas'] = df['CASNumber']
                df['CASNumber'] = df['CASNumber'].astype(str).apply(cas_validation)
                validation_df['new_cas'] = df['CASNumber']

            if 'Purity' in df.columns:
                validation_df['old_purity'] = df['Purity']
                df['Purity'] = df['Purity'].astype(str).apply(purity_validation)
                validation_df['new_purity'] = df['Purity']

            # ✅ Filter the changed rows
            changed_mask = (
                    (validation_df.get('old_mdl') != validation_df.get('new_mdl')) |
                    (validation_df.get('old_cas') != validation_df.get('new_cas')) |
                    (validation_df.get('old_purity') != validation_df.get('new_purity'))
            )

            # ✅ Create a DataFrame for rows where data has changed
            changed_df = validation_df[changed_mask]
            change_list = []

            for index, row in changed_df.iterrows():
                supplier_id = row['SupplierCatalogId']
                if row.get('old_mdl') != row.get('new_mdl'):
                    change_list.append([supplier_id, 'MDLNumber', row['old_mdl'], row['new_mdl']])
                if row.get('old_cas') != row.get('new_cas'):
                    change_list.append([supplier_id, 'CASNumber', row['old_cas'], row['new_cas']])
                if row.get('old_purity') != row.get('new_purity'):
                    change_list.append([supplier_id, 'Purity', row['old_purity'], row['new_purity']])

            # ✅ Construct result DataFrame
            change_df = pd.DataFrame(change_list, columns=['SupplierCatalogId', 'error_in', 'old_value', 'new_value'])

            return {"success": True, "data": df, "changes": change_df}

        except Exception as e:
            error_message = f"Error while processing product sheet: {str(e)}"
            log.request_logger.error(error_message)
            self.error_log(logs=error_message)
            return {"success": False, "error": error_message}

    def process_price_sheet(self, df):
        try:
            log.request_logger.info("Process the price-related columns.")
            self.info(logs="Process the price-related columns.")

            # Dynamically fetch columns starting with the required names
            price_columns = [col for col in df.columns if col.startswith("Price")]
            size_columns = [col for col in df.columns if col.startswith("Size")]
            unit_columns = [col for col in df.columns if col.startswith("Unit")]

            # Identify missing or inconsistent columns
            missing_columns = []
            for i in range(1, max(len(price_columns), len(size_columns), len(unit_columns)) + 1):
                price_column = f"Price{i}"
                size_column = f"Size{i}"
                unit_column = f"Unit{i}"

                # Check if PriceX, SizeX, and UnitX columns are all present
                if price_column in price_columns and (
                        size_column not in size_columns or unit_column not in unit_columns):
                    missing_columns.append(f"Size{i} and/or Unit{i} for {price_column}")
                if size_column in size_columns and (
                        price_column not in price_columns or unit_column not in unit_columns):
                    missing_columns.append(f"Price{i} and/or Unit{i} for {size_column}")
                if unit_column in unit_columns and (
                        price_column not in price_columns or size_column not in size_columns):
                    missing_columns.append(f"Price{i} and/or Size{i} for {unit_column}")

            # If any mismatch or missing columns are found
            if missing_columns:
                error_message = f"The following price-related columns are missing or inconsistent: {', '.join(missing_columns)}"
                self.error_log(logs=error_message)
                log.request_logger.error(error_message)
                return {"success": False, "error": error_message}

            # Reshape the price-related data
            reshaped_prices = []
            for i in range(len(price_columns)):
                reshaped_prices.extend(
                    df[['SupplierCatalogId', 'SupplierId']].assign(
                        Price=df[price_columns[i]],
                        Size=df[size_columns[i]],
                        Unit=df[unit_columns[i]]
                    ).to_dict("records")
                )

            result_df = pd.DataFrame(reshaped_prices)
            return {"success": True, "data": result_df}

        except Exception as e:
            log.request_logger.error(f"Error in process_price_sheet: {e}")
            self.error_log(logs=f"Error in process_price_sheet: {e}")
            return {"success": False, "error": str(e)}

    def process_stock_sheet(self, df):
        try:
            log.request_logger.info("Process the stock-related columns.")
            self.info(logs="Process the stock-related columns.")

            # Extract columns starting with required names
            stockqty_columns = [col for col in df.columns if col.startswith("StockQty")]
            stockunit_columns = [col for col in df.columns if col.startswith("StockUnit")]
            warehouse_columns = [col for col in df.columns if col.startswith("Warehouse")]

            # Identify missing or inconsistent columns
            missing_columns = []
            for i in range(1, max(len(stockqty_columns), len(stockunit_columns), len(warehouse_columns)) + 1):
                stockqty_column = f"StockQty{i}"
                stockunit_column = f"StockUnit{i}"
                warehouse_column = f"Warehouse{i}"

                # Check if StockQtyX, StockUnitX, and WarehouseX columns are all present
                if stockqty_column in stockqty_columns and (
                        stockunit_column not in stockunit_columns or warehouse_column not in warehouse_columns):
                    missing_columns.append(f"StockUnit{i} and/or Warehouse{i} for {stockqty_column}")
                if stockunit_column in stockunit_columns and (
                        stockqty_column not in stockqty_columns or warehouse_column not in warehouse_columns):
                    missing_columns.append(f"StockQty{i} and/or Warehouse{i} for {stockunit_column}")
                if warehouse_column in warehouse_columns and (
                        stockqty_column not in stockqty_columns or stockunit_column not in stockunit_columns):
                    missing_columns.append(f"StockQty{i} and/or StockUnit{i} for {warehouse_column}")

            # If any mismatch or missing columns are found
            if missing_columns:
                error_message = f"The following stock-related columns are missing or inconsistent: {', '.join(missing_columns)}"
                self.error_log(logs=error_message)
                log.request_logger.error(error_message)
                return {"success": False, "error": error_message}

            # Reshape the stock-related data
            reshaped_stock = []
            for i in range(len(stockqty_columns)):
                reshaped_stock.extend(
                    df[['SupplierCatalogId', 'SupplierId']].assign(
                        StockQty=df[stockqty_columns[i]],
                        StockUnit=df[stockunit_columns[i]],
                        Warehouse=df[warehouse_columns[i]]
                    ).to_dict("records")
                )

            stock_df = pd.DataFrame(reshaped_stock)
            return {"success": True, "data": stock_df}

        except Exception as e:
            error_message = f"Error while processing stock sheet: {str(e)}"
            log.request_logger.error(error_message)
            self.error_log(logs=error_message)
            return {"success": False, "error": error_message}

    def process_excel(self, df, request_type):
        # Initialize DataFrames
        products = prices = stock = validate_df = pd.DataFrame()

        try:
            # Process based on request_type
            if request_type == 1:
                product_result = self.process_product_sheet(df)
                if not product_result["success"]:
                    return product_result
                products, validate_df = product_result["data"], product_result["changes"]
                log.request_logger.info("Product sheet processed successfully.")
                self.info(logs="Product sheet processed successfully.")

                price_result = self.process_price_sheet(df)
                if not price_result["success"]:
                    return price_result
                prices = price_result["data"]
                log.request_logger.info("Price sheet processed successfully.")
                self.info(logs="Price sheet processed successfully.")

                stock_result = self.process_stock_sheet(df)
                if not stock_result["success"]:
                    return stock_result
                stock = stock_result["data"]
                log.request_logger.info("Stock sheet processed successfully.")
                self.info(logs="Stock sheet processed successfully.")

            elif request_type == 2:
                product_result = self.process_product_sheet(df)
                if not product_result["success"]:
                    return product_result
                products, validate_df = product_result["data"], product_result["changes"]
                log.request_logger.info("Product sheet processed successfully.")
                self.info(logs="Product sheet processed successfully.")

            elif request_type == 3:
                price_result = self.process_price_sheet(df)
                if not price_result["success"]:
                    return price_result
                prices = price_result["data"]
                log.request_logger.info("Price sheet processed successfully.")
                self.info(logs="Price sheet processed successfully.")

            elif request_type == 4:
                stock_result = self.process_stock_sheet(df)
                if not stock_result["success"]:
                    return stock_result
                stock = stock_result["data"]
                log.request_logger.info("Stock sheet processed successfully.")
                self.info(logs="Stock sheet processed successfully.")

            return {
                "success": True,
                "products": products,
                "prices": prices,
                "stock": stock,
                "validate_df": validate_df
            }

        except Exception as e:
            error_message = f"Error in process_excel: {str(e)}"
            log.request_logger.error(error_message)
            self.error_log(logs=error_message)
            return {"success": False, "error": error_message}


class ProductDataFilter(CleanerHandler):
    def __init__(self):
        super().__init__()

    def get_size_master_from_db(self):
        conn = create_connection_with_ssms()
        if conn:
            try:
                cursor = conn.cursor()
                query = "SELECT DISTINCT SizeUnit FROM SizeUnits;"
                cursor.execute(query)
                rows = cursor.fetchall()
                return [row[0].lower() for row in rows if row[0] is not None]
            finally:
                conn.close()
        return []

    def validate_price_and_size(self, df):
        size_master = self.get_size_master_from_db()
        error_list = []
        cleaned_data = []

        for _, row in df.iterrows():
            supplier_catalog_id = row["SupplierCatalogId"]
            supplier_id = row["SupplierId"]
            price = row["Price"]
            size = row["Size"]
            unit = row["Unit"]

            row_has_error = False

            # ✅ Validate Price
            if pd.notna(price):
                try:
                    price = float(price)
                except (ValueError, TypeError):
                    error_list.append([supplier_catalog_id, "Price", price, "Invalid Price. Must be float."])
                    row_has_error = True
            else:
                price = None  # Keep NaN as None

            # ✅ Validate Size (Quantity)
            if pd.notna(size):
                try:
                    size = float(size)
                    if size.is_integer():
                        size = int(size)  # Convert to integer if applicable
                except (ValueError, TypeError):
                    error_list.append([supplier_catalog_id, "Size", size, "Invalid Size. Must be float or integer."])
                    row_has_error = True
            else:
                size = None  # Keep NaN as None

            # ✅ Validate Unit
            if pd.notna(unit) and unit.lower() not in size_master:
                error_list.append([supplier_catalog_id, "Unit", unit, "Unit not found in master table."])
                row_has_error = True

            # ✅ If the row has any error, skip adding it to cleaned_data
            if not row_has_error:
                cleaned_data.append({
                    "SupplierCatalogId": supplier_catalog_id,
                    "SupplierId": supplier_id,
                    "Price": price,
                    "Size": size,
                    "Unit": unit
                })

        cleaned_df = pd.DataFrame(cleaned_data)
        error_df = pd.DataFrame(error_list, columns=["SupplierCatalogId", "Column", "Value", "Reason"])

        return cleaned_df, error_df

    def validate_stock_data(self, df):
        size_master = self.get_size_master_from_db()
        error_list = []
        cleaned_data = []

        for _, row in df.iterrows():
            supplier_catalog_id = row["SupplierCatalogId"]
            supplier_id = row["SupplierId"]
            stock_qty = row["StockQty"]
            stock_unit = row["StockUnit"]
            warehouse = row["Warehouse"]

            row_has_error = False

            # ✅ Validate StockQty
            if pd.notna(stock_qty):
                try:
                    stock_qty = float(stock_qty)
                    if stock_qty.is_integer():
                        stock_qty = int(stock_qty)
                except (ValueError, TypeError):
                    error_list.append([supplier_catalog_id, "StockQty", stock_qty, "Invalid StockQty. Must be float or integer."])
                    row_has_error = True
            else:
                stock_qty = None

            # ✅ Validate StockUnit
            if pd.notna(stock_unit) and stock_unit.lower() not in size_master:
                error_list.append([supplier_catalog_id, "StockUnit", stock_unit, "StockUnit not found in master table."])
                row_has_error = True

            # ✅ If the row has any error, skip adding it to cleaned_data
            if not row_has_error:
                cleaned_data.append({
                    "SupplierCatalogId": supplier_catalog_id,
                    "SupplierId": supplier_id,
                    "StockQty": stock_qty,
                    "StockUnit": stock_unit,
                    "Warehouse": warehouse
                })

        cleaned_df = pd.DataFrame(cleaned_data)
        error_df = pd.DataFrame(error_list, columns=["SupplierCatalogId", "Column", "Value", "Reason"])

        return cleaned_df, error_df

    def filter_product_data(self, df_price, df_stock):
        try:
            log.request_logger.info("Starting Product Data filtering...")
            self.info(logs="Starting Product Data filtering...")

            valid_price_size_df, price_size_errors = self.validate_price_and_size(df_price)
            valid_stock_df, stock_errors = self.validate_stock_data(df_stock)
            combined_errors = pd.concat([price_size_errors, stock_errors], ignore_index=True)

            return {
                "valid_price_data": valid_price_size_df,
                "valid_stock_data": valid_stock_df,
                "combined_errors": combined_errors
            }

        except Exception as e:
            error_message = f"Error during processing: {str(e)}"
            log.request_logger.error(error_message)
            self.error_log(logs=error_message)
            return None
