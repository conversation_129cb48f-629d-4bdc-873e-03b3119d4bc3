from server import thisfolder, os, config, pd, time
from server.com.handler.ApiHandler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from server.com.handler.DBHandler import create_connection_with_ssms
from base64 import b64encode, b64decode
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad
import requests
import json


class ValidationHandler(ApiHandler):

    def __init__(self):
        super().__init__()
        self.initialize_request()  # Initialize variables at the start
        self.process_time = {}
        self.request_logger = None
        self.SupplierID = None
        self.SupplierName = None
        self.ImportRequestId = None
        self.RequestTypeId = None
        self.SendModuleType = "ValidationProcess"
        self.ImportTypeId = None
        self.File_UUID = ""
        self.InputFileName = ""
        self.ProductInputFileName = ""
        self.CreatedDate = ""
        self.InitiateCount = 0
        self.DuplicateCount = 0
        self.ErrorCount = 0
        self.ValidationCount = 0
        self.Script = None
        self.Error = ""
        self.Status = ""
        self.StartTime = None
        self.EndTime = None
        self.TimeTaken = None

    def initialize_request(self):
        """Reset all attributes to their default values before a new request."""
        self.process_time = {}
        self.request_logger = None
        self.SupplierID = None
        self.SupplierName = None
        self.ImportRequestId = None
        self.RequestTypeId = None
        self.SendModuleType = "ValidationProcess"
        self.ImportTypeId = None
        self.File_UUID = ""
        self.InputFileName = ""
        self.ProductInputFileName = ""
        self.CreatedDate = ""
        self.InitiateCount = 0
        self.DuplicateCount = 0
        self.ErrorCount = 0
        self.ValidationCount = 0
        self.Script = None
        self.Error = ""
        self.Status = ""
        self.StartTime = None
        self.EndTime = None
        self.TimeTaken = None

    def time_calculator(self):
        self.EndTime = time.time()
        elapsed_time = self.EndTime - self.StartTime
        hours = int(elapsed_time // 3600)
        elapsed_time %= 3600
        minutes = int(elapsed_time // 60)
        seconds = elapsed_time % 60
        self.TimeTaken = f"{hours} hours, {minutes} minutes, {seconds:.2f} seconds"
        return

    def middleware_upload_file(self, file_name, file_type_id, act_file_name):
        try:
            file_path = config.get("Data", "UPLOAD_FILE_PATH") + str(self.ImportRequestId) + "/" + "Reports" + "/"
            self.request_logger.info(f"The File path {file_path}")

            file_stream = open(file_name, 'rb')

            self.request_logger.warning(
                f"[[ImportRequestId: {self.ImportRequestId}]] => The File: {file_name} and FileType: {file_type_id} is being uploaded on Filemanager Server")
            self.info(
                logs=f"The File {file_name} and FileType: {file_type_id} is being uploaded on Filemanager Server")

            file_upload_res = self.upload_file(filename=act_file_name, file=file_stream, file_path=file_path)
            respone_file_data = {}
            if self.validate_response(file_upload_res):
                file_upload_res_data = file_upload_res.json()
                output_file_name = file_upload_res_data["data"].get("filename", "")
                file_name_uuid = file_upload_res_data["data"].get("uuid", "")

                print("output_file_name", output_file_name)

                self.result(
                    logs=f"The File {file_name} and FileType:{file_type_id} has been uploaded on Filemanager Server in module")
                self.info(logs="Adding the OutPutFileName")

                add_filename_resp = self.add_import_filename(
                    import_id=self.ImportRequestId,
                    output_file=output_file_name,
                    file_uuid=file_name_uuid,
                    import_type_id=self.ImportTypeId or 1,
                    output_file_type_id=file_type_id)

                self.request_logger.info(f"add_import_filename response {add_filename_resp.status_code = }")

                if self.validate_response(add_filename_resp):
                    self.result(logs="Output File Successfully Added")
                    respone_file_data['FileName'] = str(output_file_name)
                    respone_file_data['FileType'] = str(file_type_id)
                    respone_file_data['FileUUID'] = str(file_name_uuid)
                else:
                    self.error_log(logs="There is error in Adding FileName")
                    self.request_logger.error(f"There is error in Adding File {file_name}")
                    # self.Status = "Canceled"
                    # self.Error = f"There is error in Adding FileName {file_name}"
            else:
                self.error_log(
                    f"There is error in uploading middleware file {file_name} ::{self.SendModuleType} :: {file_type_id}")
                self.request_logger.error(f"There is error in uploading middleware file {file_name}")
                # self.Status = "Canceled"
                # self.Error = f"There is error in uploading Price file {file_name}"
            return respone_file_data
        except Exception as e:
            # self.Error = str(e)
            self.error_log(logs=str(e))
            # self.Status = "Canceled"
            self.request_logger.error(e, exc_info=True)
            return

    def send_cas_mdl_purity_report(self, validated_report_df):
        if validated_report_df.empty:
            self.request_logger.info("No validation results to report")
            self.info(logs="No validation results to report")
            return False

        try:
            report_name = f"{self.ImportRequestId}_Validation_report.csv"
            report_path = os.path.join(thisfolder, 'Temp', 'ReportSheet', str(self.ImportRequestId), report_name)

            # Ensure directory exists
            os.makedirs(os.path.dirname(report_path), exist_ok=True)

            validated_report_df.to_csv(report_path, index=False)

            self.request_logger.info(f"CAS, MDL, and Purity validation report saved at: {report_path}")
            self.info(logs=f"CAS, MDL, and Purity validation report saved at: {report_path}")

            upload_response = self.middleware_upload_file(
                file_name=report_path,
                file_type_id=4,  # Adjust file type ID if needed
                act_file_name=report_name,
            )

            if upload_response:
                self.request_logger.info("Validation report successfully uploaded")
                self.info(logs="Validation report successfully uploaded")
                return True
            else:
                self.request_logger.error("Failed to upload validation report")
                self.error_log(logs="Failed to upload validation report")
                return False

        except Exception as e:
            self.request_logger.error(f"Error saving or uploading CAS, MDL, and Purity validation report: {str(e)}")
            self.error_log(logs=f"Error saving or uploading CAS, MDL, and Purity validation report: {str(e)}")
            return False

    def send_price_size_unit_filter_error_report(self, error_report_df):
        if error_report_df.empty:
            self.request_logger.info("No Price, Size, or Unit filter validation errors found. Skipping report generation.")
            self.info(logs="No Price, Size, or Unit filter validation errors found. No report generated.")
            return False

        try:
            # Define report file name and path
            report_name = f"{self.ImportRequestId}_price_size_unit_filter_errors.csv"
            report_path = os.path.join(thisfolder, 'Temp', 'ReportSheet', str(self.ImportRequestId), report_name)

            # Ensure directory exists
            os.makedirs(os.path.dirname(report_path), exist_ok=True)

            # Save DataFrame to CSV
            error_report_df.to_csv(report_path, index=False)

            self.request_logger.info(f"Validation error report for Price, Size, and Unit filters saved at: {report_path}")
            self.info(logs=f"Validation error report for Price, Size, and Unit filters saved at: {report_path}")

            # Upload the report (Replace with actual upload function)
            upload_response = self.middleware_upload_file(
                file_name=report_path,
                file_type_id=4,  # Adjust file type ID if needed
                act_file_name=report_name,
            )

            if upload_response:
                self.request_logger.info("Validation error report successfully uploaded")
                self.info(logs="Validation error report successfully uploaded")
                return True
            else:
                self.request_logger.error("Failed to upload validation error report")
                self.error_log(logs="Failed to upload validation error report")
                return False

        except Exception as e:
            self.request_logger.error(f"Error saving or uploading validation error report: {str(e)}")
            self.error_log(logs=f"Error saving or uploading validation error report: {str(e)}")
            return False

    def validate_response(self, res):
        if res is not None:
            if res.status_code == 200:
                return True
            else:
                self.request_logger.error(res.__dict__)
                return False
        else:
            return False

    def excel_to_df(self, file_name):
        if file_name.endswith(".csv"):
            df = pd.read_csv(file_name, dtype=str)  # Read everything as string to preserve formatting
        elif file_name.endswith((".xls", ".xlsx")):
            df = pd.read_excel(file_name, dtype=str)
        else:
            self.request_logger.error(f"Unsupported file type: {file_name}")
            return None

        df = df.applymap(lambda x: x.strip() if isinstance(x, str) else x)

        return df

    def remove_duplicate(self, df):
        temp_df = df.copy()
        temp_df['SupplierCatalogId'] = temp_df['SupplierCatalogId'].str.upper()
        duplicate_count = temp_df[temp_df['SupplierCatalogId'].duplicated()].shape[0]
        self.DuplicateCount = duplicate_count
        self.request_logger.info(f"Number of duplicates found and removed: {duplicate_count}")
        df = df[df['SupplierCatalogId'].notna()]
        temp_df = temp_df.drop_duplicates(subset=['SupplierCatalogId'], keep='first')
        df = df.loc[temp_df.index]
        df['SupplierCatalogId'] = df['SupplierCatalogId'].str.upper()
        return df

    def get_column_configurations(self):
        conn = create_connection_with_ssms()
        if conn:
            try:
                cursor = conn.cursor()
                query = """
                SELECT ProductColumnName, IsMandatory
                FROM ProductExcelColumnChecker;
                """
                cursor.execute(query)
                rows = cursor.fetchall()

                mandatory_columns = []
                required_columns = []

                for row in rows:
                    column_name, is_mandatory = row  # only need 'is_mandatory' since 'is_required' isn't being used
                    if is_mandatory == 1:
                        mandatory_columns.append(column_name)  # If IsMandatory is 1, it's mandatory
                    else:
                        required_columns.append(column_name)  # If IsMandatory is 0, it's required

                return {
                    "mandatory_columns": mandatory_columns,
                    "required_columns": required_columns
                }
            finally:
                conn.close()
        return {"mandatory_columns": [], "required_columns": []}

    def map_columns(self, df):
        try:
            # Attempt to fetch from DB
            column_config = self.get_column_configurations()
            if column_config and "mandatory_columns" in column_config and "required_columns" in column_config:
                mandatory_columns = column_config["mandatory_columns"]
                required_columns = column_config["required_columns"]
                source = "DB"
            else:
                # Fallback to Config
                print("Database config not found. Using config file...")
                mandatory_columns = config.get("RequiredColumn", "MANDATORY_COLUMNS").split(",")
                required_columns = config.get("RequiredColumn", "REQUIRED_COLUMNS").split(",")
                source = "Config"

            print(f"Mandatory Columns from {source}: {mandatory_columns}")
            print(f"Required Columns from {source}: {required_columns}")

            # Normalize column names (strip spaces)
            df.columns = df.columns.str.strip()
            mandatory_columns = {col.strip() for col in mandatory_columns}
            required_columns = {col.strip() for col in required_columns}

            # Check for missing mandatory columns
            missing_mandatory = mandatory_columns - set(df.columns)
            if missing_mandatory:
                error_message = f"Missing mandatory columns: {', '.join(missing_mandatory)}"
                self.error_log(logs=error_message)
                return {"success": False, "error": error_message}

            # Add missing required columns with NaN
            missing_required = required_columns - set(df.columns)
            for col in missing_required:
                df[col] = pd.NA
                print(f"Added missing required column: {col}")

            return {"success": True, "data": df}

        except Exception as e:
            error_message = f"An error occurred: {str(e)}"
            self.error_log(logs=error_message)
            return {"success": False, "error": error_message}

    def get_count_of_lines_for_txt(self, filename):
        with open(filename, 'r') as f:
            for line_number, line in enumerate(f, start=1):
                if line.strip():
                    yield line_number

    def get_count_of_data(self, data_source: str | pd.DataFrame):
        """Returns the number of rows in a DataFrame or a file (CSV, XLSX, TXT)."""
        try:
            df = None

            if isinstance(data_source, pd.DataFrame):
                df = data_source  # If already a DataFrame, use it directly
            elif isinstance(data_source, str):  # If it's a filename
                if data_source.endswith(".csv"):
                    df = pd.read_csv(data_source, dtype=str)
                elif data_source.endswith(".xlsx"):
                    df = pd.read_excel(data_source, dtype=str)
                elif data_source.endswith(".txt"):
                    return sum(1 for _ in self.get_count_of_lines_for_txt(data_source))

            return len(df) if isinstance(df, pd.DataFrame) else 0

        except Exception as e:
            self.request_logger.error(f"[[ImportRequestID]] => {e}", exc_info=True)

    def get_supplier_catalog(self):
        conn = create_connection_with_ssms()
        if conn:
            try:
                query = f"""SELECT SupplierCatalogId from SupplierProducts where OmsEchemPortalSupplierId = '{self.SupplierID}'"""
                catalog_df = pd.read_sql(query, conn)

                if catalog_df.empty:
                    self.request_logger.error(f"No SupplierCatalogId found for SupplierID: {self.SupplierID}")
                    return None  # Return None if no data is found
                return catalog_df
            except Exception as e:
                self.request_logger.error(f"Error Fetching data from SupplierProducts: {e}")
                return None  # Return None if there is an error during the SQL query
            finally:
                conn.close()
        else:
            self.request_logger.error("Database connection failed")
            return None  # Return None if connection couldn't be established

    def check_supplier_has_data(self):
        """
        Check if the supplier has any existing data in the system.

        Returns:
            dict: Result dictionary with success status and error message if applicable
                success: True if supplier has data, False if not
                error: Error message if supplier has no data
                approval_required: True if approval is required, False otherwise
                reason: Reason for approval requirement
        """
        conn = create_connection_with_ssms()
        if conn:
            try:
                query = f"""SELECT COUNT(*) as count FROM SupplierProducts WHERE OmsEchemPortalSupplierId = '{self.SupplierID}'"""
                cursor = conn.cursor()
                cursor.execute(query)
                row = cursor.fetchone()

                if row and row[0] > 0:
                    self.request_logger.info(f"Supplier {self.SupplierID} has {row[0]} existing catalog entries")
                    return {
                        "success": True,
                        "approval_required": False
                    }
                else:
                    self.request_logger.warning(f"Supplier {self.SupplierID} has no existing data in the system")
                    return {
                        "success": False,
                        "error": f"The supplier {self.SupplierName} (ID: {self.SupplierID}) has no existing data in the system",
                        "approval_required": True,
                        "reason": "New supplier with no existing data"
                    }
            except Exception as e:
                self.request_logger.error(f"Error checking supplier data: {e}")
                return {
                    "success": False,
                    "error": f"Error checking supplier data: {e}",
                    "approval_required": True,
                    "reason": "Error checking supplier data"
                }
            finally:
                conn.close()
        else:
            self.request_logger.error("Database connection failed when checking supplier data")
            return {
                "success": False,
                "error": "Database connection failed when checking supplier data",
                "approval_required": True,
                "reason": "Database connection error"
            }

    def match_supplier_catalog(self, input_df):
        """
        Check if the uploaded catalog IDs match existing catalog IDs in the database.
        Uses smart sampling to determine if the match percentage is above the threshold.

        Args:
            input_df (pd.DataFrame): Input DataFrame with SupplierCatalogID column

        Returns:
            dict: Result dictionary with success status and error message if applicable
                success: True if match percentage is above threshold, False otherwise
                error: Error message if match percentage is below threshold
                data: Original DataFrame with Reason column added if match percentage is below threshold
                approval_required: True if approval is required, False otherwise
                reason: Reason for approval requirement
                match_percentage: Percentage of sampled catalog IDs that match existing catalog IDs
        """
        catalog_df = self.get_supplier_catalog()

        # If catalog_df is None or empty, return with error message
        if catalog_df is None or catalog_df.empty:
            result = {
                "success": False,
                "error": f"The SupplierCatalogId is not found for OmsEchemPortalSupplierId: {self.SupplierID}",
                "data": input_df,
                "approval_required": True,
                "reason": "No existing catalog IDs found for supplier"
            }
            return result

        # Proceed if catalog_df is not empty
        # Sample at least 10% of rows, but minimum 20 rows (or all rows if less than 20)
        sample_size = max(min(int(len(input_df) * 0.1), len(input_df)), min(20, len(input_df)))
        sampled_input_df = input_df.sample(n=sample_size, random_state=42)

        match_counts = sampled_input_df['SupplierCatalogId'].isin(catalog_df['SupplierCatalogId']).sum()
        total_sampled = len(sampled_input_df)
        match_percentage = (match_counts / total_sampled) * 100

        self.request_logger.info(f"Catalog ID match percentage: {match_percentage:.2f}% ({match_counts}/{total_sampled})")

        result = {
            "success": False,
            "error": None,
            "data": None,
            "approval_required": False,
            "match_percentage": match_percentage
        }

        # If match percentage is above threshold (60%), proceed normally
        if match_percentage >= 60:
            result["success"] = True
            result["data"] = input_df
        else:
            # Add a Reason column to the DataFrame for reporting
            input_df['Reason'] = input_df['SupplierCatalogId'].apply(
                lambda x: 'Catalog Match' if x in catalog_df['SupplierCatalogId'].values else 'Catalog Not Match'
            )

            result["success"] = False
            result["error"] = f"The SupplierCatalogID match percentage ({match_percentage:.2f}%) is below the threshold (60%)"
            result["data"] = input_df
            result["approval_required"] = True
            result["reason"] = f"Low catalog ID match percentage: {match_percentage:.2f}%"

        return result

    def get_column_mappings(self):
        conn = create_connection_with_ssms()
        if conn:
            try:
                cursor = conn.cursor()

                # Query 1: Chemical Product Names and Aliases
                query_product_names = """
                SELECT scp.ChemicalProductName, spa.ChemicalProductAliasName
                FROM SupplierChemicalProductNames scp
                JOIN SupplierChemicalProductAliases spa ON scp.ChemicalProductNameId = spa.ChemicalProductNameId
                """

                # Query 2: Property Names and Aliases
                query_property_names = """
                SELECT pn.PropertyName, pa.PropertyAliasName
                FROM ProductProperties pn
                JOIN ProductPropertyAliases pa ON pn.PropertyNameId = pa.PropertyNameId
                """

                # Execute and fetch for product names
                cursor.execute(query_product_names)
                product_rows = cursor.fetchall()
                product_name_mappings = {}
                for row in product_rows:
                    name, alias = row
                    product_name_mappings.setdefault(name, []).append(alias)

                # Execute and fetch for property names
                cursor.execute(query_property_names)
                property_rows = cursor.fetchall()
                property_name_mappings = {}
                for row in property_rows:
                    name, alias = row
                    property_name_mappings.setdefault(name, []).append(alias)

                # Return both separately
                return {
                    'product_name_mappings': product_name_mappings,
                    'property_name_mappings': property_name_mappings
                }

            finally:
                conn.close()
        return {'product_name_mappings': {}, 'property_name_mappings': {}}

    def read_and_map_headers_for_product(self, df):
        try:
            self.request_logger.info(f"Original Columns: {df.columns.tolist()}")

            # Step 1: Get mappings
            all_column_mappings = self.get_column_mappings()
            self.request_logger.info(f"{all_column_mappings = }")

            product_name_mappings = all_column_mappings.get('product_name_mappings', {})
            property_name_mappings = all_column_mappings.get('property_name_mappings', {})

            remapped_columns = {}
            mapped_property_columns = set()

            # Track which original columns were mapped to product columns
            mapped_original_product_columns = set()
            # Track which original columns were mapped to property columns
            mapped_original_property_columns = set()

            # Step 2: Map product columns
            for db_column, alternatives in product_name_mappings.items():
                alt_set = {alt.lower().strip() for alt in alternatives}
                for column in df.columns:
                    if column.lower().strip() in alt_set:
                        remapped_columns[column] = db_column
                        mapped_original_product_columns.add(column)
                        break

            # Step 3: Map property columns and keep track of what's actually mapped
            for db_column, alternatives in property_name_mappings.items():
                alt_set = {alt.lower().strip() for alt in alternatives}
                for column in df.columns:
                    if column.lower().strip() in alt_set:
                        remapped_columns[column] = db_column
                        mapped_property_columns.add(db_column)
                        mapped_original_property_columns.add(column)
                        break

            self.request_logger.info(f"{remapped_columns = }")

            # Step 4: Rename the columns
            df.rename(columns=remapped_columns, inplace=True)

            # Step 5: Add missing product columns with None values
            missing_product_columns = [col for col in product_name_mappings.keys() if col not in df.columns]
            for col in missing_product_columns:
                df[col] = None

            if missing_product_columns:
                self.request_logger.warning(
                    f"Missing product columns added with null values: {missing_product_columns = }")

            # Step 6: Only ensure mapped property columns are present (already renamed)
            for col in mapped_property_columns:
                if col not in df.columns:
                    df[col] = None

            # Step 7: Identify unmapped columns (excluding dynamic patterns like Size1, Price2 etc.)
            known_prefixes = ["Price", "Size", "Unit", "StockUnit", "StockQty", "Warehouse"]
            product_name_keys = set(map(str.lower, product_name_mappings.keys()))

            def is_known_dynamic_column(col):
                col_lower = col.lower()
                return (
                        any(col_lower.startswith(prefix.lower()) for prefix in known_prefixes)
                        or col_lower in remapped_columns.values()
                        or col_lower in product_name_keys
                        or col_lower in map(str.lower, mapped_property_columns)
                )

            # Find unmapped product columns - these should cause an error
            # A column is an unmapped product column if it's not in mapped_original_product_columns
            # and not in mapped_original_property_columns and not a known dynamic column
            unmapped_product_columns = []
            unmapped_property_columns = []

            print(f"{mapped_original_product_columns = }")
            print(f"{mapped_original_property_columns = }")

            # Categorize unmapped columns
            for col in df.columns:
                if col in mapped_original_product_columns or col in mapped_original_property_columns:
                    continue  # Skip columns that were successfully mapped

                if is_known_dynamic_column(col):
                    continue  # Skip known dynamic columns

                # At this point, the column is unmapped
                # Check if it's a product column that should have been mapped
                # For now, we'll consider all unmapped columns as property columns that need approval
                unmapped_property_columns.append(col)

            # Log unmapped columns
            if unmapped_product_columns:
                error_msg = f"Unmapped product columns found in Excel Sheet: {unmapped_product_columns}"
                self.request_logger.error(error_msg)
                self.error_log(logs=error_msg)
                return None, error_msg, unmapped_product_columns, []

            if unmapped_property_columns:
                self.request_logger.warning(
                    f"Unmapped property columns found in Excel Sheet: {unmapped_property_columns}"
                )
                # Return the DataFrame with a flag for approval
                return df, f"Unmapped property columns found in Excel Sheet: {unmapped_property_columns}", [], unmapped_property_columns

            self.request_logger.info(f"Mapped Columns: {df.columns.tolist()}")
            return df, "Successfully mapped headers.", [], []

        except Exception as e:
            error_msg = f"Error processing file {self.ProductInputFileName}: {e}"
            self.error_log(logs=error_msg)
            self.request_logger.error(error_msg)
            return None, error_msg, [], []

# Unit validation has been moved to the filter data process

    def get_supplier_warehouse_details_from_api(self):
        """
        Fetches warehouse details for a supplier from the API.
        Similar to how it's implemented in the stockProcessqueue worker.

        Returns:
            tuple: (list of valid warehouse names, error message if any)
        """
        try:
            # Get OMS Base URL from config
            is_production = config.get("Data", "ISPRODUCTION").lower() == "true"
            oms_base_url = config.get("ProductionApi", "OMSBaseURL") if is_production else config.get("TestingAPI", "OMSBaseURL")

            # Get AES Key & IV from config
            aes_key = b64decode(config.get('OMS_AES', 'KEY'))
            aes_iv = b64decode(config.get('OMS_AES', 'IV'))

            # Encrypt the supplier ID parameter
            try:
                cipher = AES.new(aes_key, AES.MODE_CBC, aes_iv)
                encrypted_data = cipher.encrypt(pad(f"supplierId={self.SupplierID}".encode(), AES.block_size))
                encrypted_param = b64encode(encrypted_data).decode("utf-8")
            except Exception as e:
                error_msg = f"Encryption Error: {e}"
                self.request_logger.error(error_msg, exc_info=True)
                self.error_log(logs=error_msg)
                return [], error_msg

            # Make API call
            url = f"{oms_base_url}api/SupplierWareHouse/GetWarehouseDetailsBySupplierId?{encrypted_param}"

            try:
                response = requests.get(url)
                response.raise_for_status()

                response_json = response.json()
                encrypted_response = response_json.get("responseData")

                if encrypted_response:
                    # Decrypt the response
                    try:
                        encrypted_data = b64decode(encrypted_response)
                        cipher = AES.new(aes_key, AES.MODE_CBC, aes_iv)
                        decrypted_data = unpad(cipher.decrypt(encrypted_data), AES.block_size)
                        decrypted_response = decrypted_data.decode()

                        decrypted_json = json.loads(decrypted_response)

                        # Extract warehouse data
                        warehouse_data = decrypted_json.get('data', [])
                        warehouse_df = pd.DataFrame(warehouse_data)

                        if not warehouse_df.empty and 'warehouseSheetName' in warehouse_df.columns:
                            return warehouse_df['warehouseSheetName'].str.lower().tolist(), None
                        else:
                            # If no warehouses are found, it's not an error - it means the supplier has no warehouses
                            message = "No warehouses found in API response for this supplier"
                            self.request_logger.info(message)
                            self.info(logs=message)
                            return [], ""
                    except Exception as e:
                        error_msg = f"Decryption Error: {e}"
                        self.request_logger.error(error_msg, exc_info=True)
                        self.error_log(logs=error_msg)
                        return [], error_msg
                else:
                    error_msg = "No responseData found in API response"
                    self.request_logger.error(error_msg)
                    self.error_log(logs=error_msg)
                    return [], error_msg
            except Exception as e:
                error_msg = f"API Request Error: {e}"
                self.request_logger.error(error_msg, exc_info=True)
                self.error_log(logs=error_msg)
                return [], error_msg
        except Exception as e:
            error_msg = f"Error fetching warehouse data from API: {e}"
            self.request_logger.error(error_msg, exc_info=True)
            self.error_log(logs=error_msg)
            return [], error_msg

    def validate_warehouses(self, df):
        """
        Validate warehouses in the DataFrame against API data.
        Handles multiple warehouse columns (Warehouse, Warehouse1, Warehouse2, etc.)

        Args:
            df (pd.DataFrame): Input DataFrame with Warehouse or WarehouseX columns

        Returns:
            dict: Result dictionary with validation results
                success: True if all warehouses are valid, False otherwise
                approval_required: True if approval is required, False otherwise
                invalid_warehouses: List of invalid warehouse names
                reason: Reason for approval requirement
                error_message: Error message if any specific error condition is met
        """
        result = {
            "success": True,
            "approval_required": False,
            "invalid_warehouses": [],
            "reason": "",
            "error_message": ""
        }

        # Find all warehouse columns (Warehouse, Warehouse1, Warehouse2, etc.)
        warehouse_columns = [col for col in df.columns if col.startswith("Warehouse")]

        if not warehouse_columns:
            error_msg = "ERROR: No warehouse columns found in the data for stock-related request. Warehouse columns are required."
            self.request_logger.warning(error_msg)
            result["error_message"] = error_msg
            return result
        
        # Get warehouse data from API instead of database
        valid_warehouses, api_error = self.get_supplier_warehouse_details_from_api()

        if not valid_warehouses:
            # Check if this is a "no warehouses" message or an actual error
            if api_error:
                # This is a technical error with the API
                error_msg = f"ERROR: {api_error}. Warehouse validation is required for stock-related request."
                self.request_logger.warning(error_msg)
                result["error_message"] = error_msg
                return result
            else:
                # This is a business rule case - supplier has no warehouses
                # For this case, we'll just log it and continue - it's not an error
                result["success"] = False
                result["approval_required"] = True
                result["reason"] += "Supplier has no warehouses."
                self.request_logger.warning(f"Supplier has no warehouses.")
                return result

        # Collect all unique warehouse values across all warehouse columns
        all_unique_warehouses = set()
        for col in warehouse_columns:
            # Get unique values, excluding NaN/None
            unique_values = df[col].dropna().unique()
            all_unique_warehouses.update(unique_values)

        # Check for invalid warehouses
        invalid_warehouses = [w for w in all_unique_warehouses if str(w).lower() not in valid_warehouses]

        if invalid_warehouses:
            result["success"] = False
            result["approval_required"] = True
            result["invalid_warehouses"] = invalid_warehouses
            result["reason"] += f"Invalid warehouses found: {', '.join(invalid_warehouses)}. "
            self.request_logger.warning(f"Invalid warehouses found: {invalid_warehouses}")

        return result

    def add_request_report(self, import_type_id: int, approval_reasons=None):
        try:
            # Create a standard report
            add_res = self.add_report(import_id=self.ImportRequestId, import_type_id=import_type_id,
                                      input_count=self.InitiateCount, new_count=0,
                                      exists_count=0,
                                      error_count=self.ErrorCount, update_count=0, duplicate_count=self.DuplicateCount)

            if not self.validate_response(add_res):
                self.request_logger.error(f"There is error during AddReportRequest_API {add_res.__dict__}")
                self.Status = "Canceled"
                self.Error = f"There is error during AddReportRequest_API {add_res.__dict__}"
                return

            # If approval reasons are provided, create a special report or log entry
            if approval_reasons:
                # Log each approval reason
                for reason in approval_reasons:
                    self.info(logs=f"Approval reason: {reason}")

                # Create a CSV report with approval reasons if needed
                # self.create_approval_reasons_report(approval_reasons)

        except Exception as e:
            self.request_logger.error(
                f"Exception occurred while adding report for ImportRequestID: {self.ImportRequestId}. Error: {str(e)}",
                exc_info=True
            )

    def create_approval_reasons_report(self, approval_reasons):
        """
        Create a report file with approval reasons.

        Args:
            approval_reasons (list): List of approval reasons
        """
        try:
            # Create a DataFrame with approval reasons
            # pd is already imported at the top of the file

            reasons_df = pd.DataFrame({
                'ApprovalReason': approval_reasons,
                'ImportRequestId': self.ImportRequestId,
                'SupplierID': self.SupplierID,
                'SupplierName': self.SupplierName,
                'Timestamp': pd.Timestamp.now()
            })

            # Define report file name and path
            report_name = f"{self.ImportRequestId}_approval_reasons.csv"
            report_path = os.path.join(thisfolder, 'Temp', 'ReportSheet', str(self.ImportRequestId), report_name)

            # Ensure directory exists
            os.makedirs(os.path.dirname(report_path), exist_ok=True)

            # Save DataFrame to CSV
            reasons_df.to_csv(report_path, index=False)

            self.request_logger.info(f"Approval reasons report saved at: {report_path}")
            self.info(logs=f"Approval reasons report saved at: {report_path}")

            # Upload the report
            upload_response = self.middleware_upload_file(
                file_name=report_path,
                file_type_id=5,  # Adjust file type ID if needed
                act_file_name=report_name,
            )

            if upload_response:
                self.request_logger.info("Approval reasons report successfully uploaded")
                self.info(logs="Approval reasons report successfully uploaded")
            else:
                self.request_logger.error("Failed to upload approval reasons report")
                self.error_log(logs="Failed to upload approval reasons report")

        except Exception as e:
            self.request_logger.error(f"Error creating approval reasons report: {str(e)}")
            self.error_log(logs=f"Error creating approval reasons report: {str(e)}")

    def update_request_status(self, status_id: int, stage_id: int, sub_stage: int = None):
        try:
            update_res = self.update_request(import_id=self.ImportRequestId, status_id=status_id, stage_id=stage_id, request_time=self.TimeTaken if self.TimeTaken else None,
                                             sub_stage_id=sub_stage, process_request_time=self.process_time)

            if not self.validate_response(update_res):
                self.request_logger.error(f"There is error during UpdateRequest_API {update_res.__dict__}")
                self.Status = "Canceled"
                self.Error = f"There is error during UpdateRequest_API {update_res.__dict__}"
        except Exception as e:
            self.request_logger.error(e, exc_info=True)

    def error_log(self, logs: str):
        try:
            self.add_log(import_id=self.ImportRequestId, com_name="DataValidationProcess",
                         log_type="ERROR", logs=logs)
        except Exception as e:
            self.request_logger.error(e, exc_info=True)

    def info(self, logs: str):
        try:
            self.add_log(import_id=self.ImportRequestId, com_name="DataValidationProcess",
                         log_type="INFO", logs=logs)
        except Exception as e:
            self.request_logger.error(e, exc_info=True)

    def result(self, logs: str):
        try:
            self.add_log(import_id=self.ImportRequestId, com_name="DataValidationProcess",
                         log_type="RESULT", logs=logs)
        except Exception as e:
            self.request_logger.error(e, exc_info=True)
