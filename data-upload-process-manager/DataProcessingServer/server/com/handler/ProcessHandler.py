from server.com.handler.FileProcessHandler import FileProcessHandler
from server.com.handler.ApiHandler import <PERSON><PERSON><PERSON>and<PERSON>
from server.com.handler.LoggerHandler import get_logger
from server import logger, AllowedScript, time, MongoDB_Name
api_handler = ApiHandler()


class ProcessHandler(FileProcessHandler):

    def __init__(self):
        super().__init__()

    def start_process(self):
        try:
            self.StartTime = time.time()
            self.Script = next((script['ScriptName'] for script in AllowedScript if script['TypeId'] == self.RequestTypeId), None)
            logger.info(f"[[Starting: {self.Script}]]")
            self.TimeTaken = "00 hours, 00 minutes, 00 seconds"
            self.update_request_status(status_id=2, stage_id=2)
            self.request_logger = get_logger(self.ImportRequestId)
            self.request_logger.info(f"Update Get Request Status")
            self.info(logs="Update Request Status and StageID")
            # Use validation_step=1 for primary validation (normal requests from request_queue)
            # Return the processing result directly instead of task_list
            processing_result = self.start_import(validation_step=1)
            print("\n\n")
            print("*"*40)
            print(f"{processing_result = }")
            print("*"*40)
            print("\n\n")
            return processing_result
        except Exception as e:
            self.request_logger.error(e, exc_info=True)
            return {"success": False}

    def start_validation_process(self):
        try:
            self.StartTime = time.time()
            self.Script = next((script['ScriptName'] for script in AllowedScript if script['TypeId'] == self.RequestTypeId), None)
            logger.info(f"[[Starting: {self.Script}]]")
            self.TimeTaken = "00 hours, 00 minutes, 00 seconds"
            self.update_request_status(status_id=2, stage_id=2)
            self.request_logger = get_logger(self.ImportRequestId)
            self.request_logger.info(f"Update Get Request Status")
            self.info(logs="Update Request Status and StageID")
            # Use validation_step=2 for secondary validation (revalidation after approval)
            # Return the processing result directly instead of task_list
            processing_result = self.start_import(validation_step=2)
            print("\n\n")
            print("*"*40)
            print(f"{processing_result = }")
            print("*"*40)
            print("\n\n")
            return processing_result
        except Exception as e:
            self.request_logger.error(e, exc_info=True)
            return {"success": False}

    @staticmethod
    def process_request_type(import_request_id, request_type_id, supplier_id, supplier_name, created_date):
        """Process request based on RequestTypeId."""
        def create_request_dict(import_type):
            collection_type = import_type.split('_')[0].capitalize()
            return {
                "ImportRequestId": import_request_id,
                "RequestTypeId": request_type_id,
                "ImportType": import_type,
                "SupplierID": supplier_id,
                "SupplierName": supplier_name,
                "CreatedDate": created_date,
                "Data": {
                    "DatabaseName": MongoDB_Name,
                    "CollectionName": f"{import_request_id}_{collection_type}Excel"
                }
            }

        req_list = []
        request_types = {
            1: ["product_queue", "price_queue", "stock_queue"],
            2: ["product_queue"],
            3: ["price_queue"],
            4: ["stock_queue"]
        }

        if request_type_id in request_types:
            for import_type in request_types[request_type_id]:
                req_list.append(create_request_dict(import_type))

        return req_list
