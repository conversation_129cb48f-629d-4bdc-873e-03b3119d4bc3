import time
import pika
import json
import multiprocessing
import logging
import datetime
from multiprocessing import Process, Queue, Lock
from pymongo import MongoClient

from server import logger, config, MongoDB_Name
from server.com.handler.ProcessHandler import ProcessHandler

logging.getLogger("pika").setLevel(logging.WARNING)

rabbit_mq = config.get('Data', 'RABBITMQ')

class Middleware(ProcessHandler):
    def __init__(self):
        super().__init__()

        # Initialize response queue for inter-process communication
        self.response_queue = Queue()

        # Initialize task queue to process tasks sequentially
        self.task_queue = Queue()

        # Locks to ensure sequential task processing within each consumer
        self.request_task_lock = Lock()
        self.approved_task_lock = Lock()

        # Event to control consumer processes
        self._running = multiprocessing.Event()
        self._running.set()  # Set to True initially

        # Start consumer processes for request, response, and approved queues
        self.consumer_process = Process(target=self.start_request_consumer)
        self.consumer_process.daemon = True
        self.consumer_process.start()

        self.approved_process = Process(target=self.start_approved_queue_consumer)
        self.approved_process.daemon = True
        self.approved_process.start()

        self.response_consumer_process = Process(target=self.start_response_consumer)
        self.response_consumer_process.daemon = True
        self.response_consumer_process.start()

    def establish_connection(self, queue_name=None, max_retries=None):
        """Establish a new connection and declare only the specified queue, if provided."""
        retry_count = 0
        while True:
            try:
                logger.info(f"Connecting to RabbitMQ server at {rabbit_mq}...")

                # Create connection parameters from AMQP URL
                connection_params = pika.URLParameters(rabbit_mq)
                connection_params.heartbeat = 30000
                connection_params.blocked_connection_timeout = 30000
                connection_params.retry_delay = 5
                connection_params.connection_attempts = 5

                # Establish the connection
                connection = pika.BlockingConnection(connection_params)
                channel = connection.channel()

                # Declare only the specified queue, if provided
                if queue_name:
                    channel.queue_declare(queue=queue_name, durable=True)

                logger.info(f"Connection established successfully for queue: {queue_name or 'no queue declared'}.")
                return connection, channel
            except pika.exceptions.AMQPConnectionError as e:
                retry_count += 1
                if max_retries is not None and retry_count > max_retries:
                    raise Exception("Maximum retry limit reached. Unable to connect to RabbitMQ.") from e
                logger.info(f"Connection error: {e}. Retrying in 5 seconds... (Attempt {retry_count})")
                time.sleep(5)

    def send_task(self, queue, task, connection, channel):
        """Send task to a specific queue using the provided connection and channel."""
        try:
            # Ensure the target queue is declared
            channel.queue_declare(queue=queue, durable=True)
            channel.basic_publish(
                exchange='',
                routing_key=queue,
                body=json.dumps(task)
            )
            logger.info(f"Task sent to {queue}: {task}")
            self.info(logs=f"Task sent to {queue}: {task}")
        except (pika.exceptions.AMQPConnectionError, pika.exceptions.ChannelClosed) as e:
            logger.error(f"Connection/channel error during publish: {e}. Reconnecting...")
            # Re-establish connection and retry
            new_connection, new_channel = self.establish_connection(queue_name=queue)
            new_channel.basic_publish(
                exchange='',
                routing_key=queue,
                body=json.dumps(task)
            )
            logger.info(f"Task sent to {queue} after reconnect: {task}")
            self.info(logs=f"Task sent to {queue} after reconnect: {task}")
            return new_connection, new_channel  # Return new connection/channel for further use
        return connection, channel  # Return original connection/channel if successful

    def wait_for_response(self, queue_name):
        """Wait for a response."""
        logger.info(f"Waiting for response from queue: {queue_name}...")
        self.info(logs=f"Waiting for response from queue: {queue_name}...")
        response = self.response_queue.get()  # Block until a response is available
        logger.info(f"Received response from queue {queue_name}: {response}")
        return response

    def send_task_with_retry(self, queue, task, connection, channel, retries=3):
        """Send task with retry logic in case of connection errors."""
        attempt = 0
        delay = 5  # Initial delay (seconds)
        current_connection, current_channel = connection, channel
        while attempt < retries:
            try:
                current_connection, current_channel = self.send_task(queue, task, current_connection, current_channel)
                return current_connection, current_channel  # Success
            except pika.exceptions.AMQPConnectionError as e:
                logger.error(f"Connection lost: {e}. Retrying... ({attempt + 1}/{retries})")
                attempt += 1
                time.sleep(delay)
                delay *= 2  # Exponential backoff
                # Re-establish connection for retry
                current_connection, current_channel = self.establish_connection(queue_name=queue)
            except Exception as e:
                logger.error(f"Error sending task to {queue}: {e}")
                break  # No retry for non-connection related errors
        logger.error(f"Failed to send task to {queue} after {retries} retries.")
        return current_connection, current_channel

    def on_response(self, ch, method, properties, body):
        """Callback for responses."""
        logger.info(f"Received message on response_queue from queue: {properties.reply_to}: {body}")
        self.result(logs=f"Received message on response_queue from queue: {properties.reply_to}: {body}")
        self.response_queue.put(json.loads(body))  # Use process-safe queue
        ch.basic_ack(delivery_tag=method.delivery_tag)  # Acknowledge message

    def on_request(self, ch, method, properties, body, connection, channel):
        """Callback for requests from the request_queue."""
        try:
            request_data = json.loads(body)
            logger.info(f"{request_data = }")

            if request_data:
                logger.info(f"Received request: {request_data}")

                # Extract required fields from the request
                self.initialize_request()
                self.ImportRequestId = request_data.get("_id")
                self.SupplierID = request_data.get("SupplierID")
                self.SupplierName = request_data.get("SupplierName")
                self.InputFileName = request_data.get("InputFileName")
                self.File_UUID = request_data.get("FileUUID")
                self.RequestTypeId = request_data.get("RequestTypeObj").get("RequestTypeID")
                self.CreatedDate = request_data.get("CreatedDate")
                self.ImportTypeId = 1

                with self.request_task_lock:
                    # This will use validation_step=1 for primary validation
                    processing_result = self.start_process()

                    # Check if processing was successful
                    if not processing_result.get("success", False):
                        logger.error("Processing failed. Import process aborted.")
                        self.error_log(logs="Processing failed. Import process aborted.")
                        ch.basic_ack(delivery_tag=method.delivery_tag)
                        return connection, channel

                    # Check if approval_required is True in the processing result
                    if processing_result.get("approval_required", False):
                        # If approval_required is True, the _step_handle_approval method has already been called
                        # in the start_import method, so we just need to return
                        logger.info("Request has been routed for approval.")
                        self.info(logs="Request has been routed for approval.")
                        ch.basic_ack(delivery_tag=method.delivery_tag)
                        return connection, channel

                    # If we get here, processing was successful and no approval is required
                    # We don't need to do anything else as the finalize step has already been done
                    # in the start_import method

                ch.basic_ack(delivery_tag=method.delivery_tag)
            else:
                logger.warning("Received empty request data.")
                ch.basic_ack(delivery_tag=method.delivery_tag)
        except json.JSONDecodeError as e:
            logger.error(f"Failed to decode request body as JSON: {e}")
            ch.basic_ack(delivery_tag=method.delivery_tag)
        except Exception as e:
            logger.error(f"An error occurred while processing the request: {e}")
            ch.basic_ack(delivery_tag=method.delivery_tag)
        return connection, channel

    def on_approved(self, ch, method, properties, body, connection, channel):
        """Callback for approved queue messages."""
        try:
            approved_data = json.loads(body)
            logger.info(f"{approved_data = }")

            if approved_data:
                logger.info(f"Received message on approved_queue: {approved_data}")
                self.result(logs=f"Received message on approved_queue: {approved_data}")

                # Extract required fields from the request
                self.initialize_request()

                self.ImportRequestId = approved_data.get("import_request_id") or approved_data.get("_id")
                self.RequestTypeId = approved_data.get("request_type_id") or approved_data.get("RequestTypeObj", {}).get("RequestTypeID")
                self.SupplierID = approved_data.get("supplier_id") or approved_data.get("SupplierID")
                self.SupplierName = approved_data.get("supplier_name") or approved_data.get("SupplierName")
                self.InputFileName = approved_data.get("input_file_name") or approved_data.get("InputFileName")
                self.File_UUID = approved_data.get("file_uuid") or approved_data.get("FileUUID")
                self.CreatedDate = approved_data.get("created_date") or approved_data.get("CreatedDate")
                self.ImportTypeId = 1

                self.StartTime = time.time()
                self.TimeTaken = "00 hours, 00 minutes, 00 seconds"

                with self.approved_task_lock:
                    # Step 1: Perform validation after approval
                    # This will use validation_step=2 for secondary validation
                    processing_result = self.start_validation_process()

                    # Step 2: Check if processing was successful
                    if not processing_result.get("success", False):
                        logger.error("Processing failed. Import process aborted.")
                        self.error_log(logs="Processing failed. Import process aborted.")
                        ch.basic_ack(delivery_tag=method.delivery_tag)
                        return connection, channel

                    # Step 3: Check if approval_required is True in the processing result
                    if processing_result.get("approval_required", False):
                        # If approval_required is True, the _step_handle_approval method has already been called
                        # in the start_import method, so we just need to return
                        logger.info("Request has been routed for approval again.")
                        self.info(logs="Request has been routed for approval again.")
                        ch.basic_ack(delivery_tag=method.delivery_tag)
                        return connection, channel

                    # Step 4: If validation passes and no approval required, process tasks based on RequestTypeId
                    logger.info(f"Validation passed. Processing tasks for ImportRequestId: {self.ImportRequestId}")
                    self.info(logs=f"Validation passed. Processing tasks for ImportRequestId: {self.ImportRequestId}")

                    # Generate task list based on RequestTypeId
                    task_list = self.process_request_type(
                        import_request_id=self.ImportRequestId,
                        request_type_id=self.RequestTypeId,
                        supplier_id=self.SupplierID,
                        supplier_name=self.SupplierName,
                        created_date=self.CreatedDate,
                    )

                    if task_list:
                        connection, channel = self.process_tasks(task_list, connection, channel)
                    else:
                        logger.error("Task list is empty, no tasks to process.")

                ch.basic_ack(delivery_tag=method.delivery_tag)
            else:
                logger.warning("Received empty approved data.")
                ch.basic_ack(delivery_tag=method.delivery_tag)
        except json.JSONDecodeError as e:
            logger.error(f"Failed to decode approved queue message body as JSON: {e}")
            ch.basic_ack(delivery_tag=method.delivery_tag)
        except Exception as e:
            logger.error(f"Error processing approved queue message: {e}")
            self.error_log(logs=f"Error processing approved queue message: {e}")
            ch.basic_ack(delivery_tag=method.delivery_tag)
        return connection, channel

    def start_request_consumer(self):
        """Start the consumer to listen for requests in the request_queue."""
        while self._running.is_set():
            logger.info("Starting consumer process for request_queue...")
            connection = None
            channel = None
            try:
                connection, channel = self.establish_connection(queue_name='request_queue')

                channel.basic_consume(
                    queue='request_queue',
                    on_message_callback=lambda ch, method, properties, body: self.on_request(ch, method, properties, body, connection, channel),
                    auto_ack=False
                )
                logger.info("Listening for requests on the request_queue...")
                channel.start_consuming()
            except pika.exceptions.AMQPConnectionError as e:
                logger.info(f"Connection lost: {e}. Reconnecting...")
                time.sleep(5)
            except KeyboardInterrupt:
                logger.error("Consumer interrupted by user. Stopping...")
                self._running.clear()
                break
            except Exception as e:
                logger.error(f"Unexpected error: {e}")
                time.sleep(5)
            finally:
                if connection and not connection.is_closed:
                    connection.close()

    def start_response_consumer(self):
        """Start the consumer to listen for responses in the response_queue."""
        while self._running.is_set():
            logger.info("Starting consumer process for response_queue...")
            connection = None
            try:
                connection, channel = self.establish_connection(queue_name='response_queue')

                channel.basic_consume(queue='response_queue', on_message_callback=self.on_response, auto_ack=False)
                logger.info("Listening for responses on the response_queue...")
                channel.start_consuming()
            except pika.exceptions.AMQPConnectionError as e:
                logger.info(f"Connection lost: {e}. Reconnecting...")
                time.sleep(5)
            except KeyboardInterrupt:
                logger.error("Consumer interrupted by user. Stopping...")
                self._running.clear()
                break
            except Exception as e:
                logger.error(f"Unexpected error: {e}")
                time.sleep(5)
            finally:
                if connection and not connection.is_closed:
                    connection.close()

    def start_approved_queue_consumer(self):
        """Start the consumer to listen for messages in the approved_queue."""
        while self._running.is_set():
            logger.info("Starting consumer process for approved_queue...")
            connection = None
            channel = None
            try:
                connection, channel = self.establish_connection(queue_name='approved_queue')

                channel.basic_consume(
                    queue='approved_queue',
                    on_message_callback=lambda ch, method, properties, body: self.on_approved(ch, method, properties, body, connection, channel),
                    auto_ack=False
                )
                logger.info("Listening for messages on the approved_queue...")
                channel.start_consuming()
            except pika.exceptions.AMQPConnectionError as e:
                logger.info(f"Connection lost: {e}. Reconnecting...")
                time.sleep(5)
            except KeyboardInterrupt:
                logger.error("Consumer interrupted by user. Stopping...")
                self._running.clear()
                break
            except Exception as e:
                logger.error(f"Unexpected error: {e}")
                time.sleep(5)
            finally:
                if connection and not connection.is_closed:
                    connection.close()

    def start(self):
        """Start the Middleware."""
        logger.info("Middleware is running... Waiting for requests.")

    def shutdown(self):
        """Gracefully shutdown the Middleware."""
        logger.info("Shutting down Middleware...")

        # Signal processes to stop
        self._running.clear()

        # Join processes with timeout
        processes_to_join = [
            ("consumer_process", self.consumer_process),
            ("response_consumer_process", self.response_consumer_process),
            ("approved_process", self.approved_process)
        ]

        for process_name, process in processes_to_join:
            if process and process.is_alive():
                logger.info(f"Waiting for {process_name} to terminate...")
                process.join(timeout=5)  # Wait up to 5 seconds for each process
                if process.is_alive():
                    logger.warning(f"{process_name} did not terminate within timeout")
                else:
                    logger.info(f"{process_name} terminated successfully")

        # Final verification
        all_terminated = all(not process.is_alive() for _, process in processes_to_join if process)
        if all_terminated:
            logger.info("All processes terminated successfully")
        else:
            logger.warning("Some processes did not terminate properly")

        logger.info("Middleware shutdown complete.")

    def process_tasks(self, task_list, connection, channel):
        """Coordinate tasks sequentially and stop on error."""
        logger.info(f"Processing task list: {task_list}")
        self.info(logs=f"Processing task list: {task_list}")

        all_tasks_successful = True
        self.process_time = {
            "ProductProcess": "",
            "PriceProcess": "",
            "StockProcess": "",
            "UploadProcess": ""
        }

        for task in task_list:
            if task["ImportType"] == "product_queue":
                connection, channel = self.send_task("product_queue", task, connection, channel)
                response = self.wait_for_response("product_queue")
                self.process_time["ProductProcess"] = response.get("ProductTimeTaken", "")
                if response["Status"] == "Canceled":
                    logger.error(f"Error in Product Queue for task {task['ImportRequestId']}: {response}")
                    self.error_log(logs=f"Error in Product Queue for task {task['ImportRequestId']}: {response}")
                    all_tasks_successful = False
                    self.time_calculator()
                    self.update_request_status(status_id=5, stage_id=3)
                    break

            elif task["ImportType"] == "price_queue":
                connection, channel = self.send_task("price_queue", task, connection, channel)
                response = self.wait_for_response("price_queue")
                self.process_time["PriceProcess"] = response.get("PriceTimeTaken", "")
                if response["Status"] == "Canceled":
                    logger.error(f"Error in Price Queue for task {task['ImportRequestId']}: {response}")
                    self.error_log(logs=f"Error in Price Queue for task {task['ImportRequestId']}: {response}")
                    all_tasks_successful = False
                    self.time_calculator()
                    self.update_request_status(status_id=5, stage_id=4)
                    break

            elif task["ImportType"] == "stock_queue":
                connection, channel = self.send_task("stock_queue", task, connection, channel)
                response = self.wait_for_response("stock_queue")
                self.process_time["StockProcess"] = response.get("StockTimeTaken", "")
                if response["Status"] == "Canceled":
                    logger.error(f"Error in Stock Queue for task {task['ImportRequestId']}: {response}")
                    self.error_log(logs=f"Error in Stock Queue for task {task['ImportRequestId']}: {response}")
                    all_tasks_successful = False
                    self.time_calculator()
                    self.update_request_status(status_id=5, stage_id=5)
                    break

        if all_tasks_successful:
            logger.info("All tasks processed successfully! Now processing import via upload_queue...")
            upload_task = {
                "RequestTypeId": self.RequestTypeId,
                "ImportRequestId": self.ImportRequestId,
                "SupplierID": self.SupplierID,
                "SupplierName": self.SupplierName,
                "CreatedDate": self.CreatedDate
            }
            connection, channel = self.send_task("upload_queue", upload_task, connection, channel)
            response = self.wait_for_response("upload_queue")
            self.process_time["UploadProcess"] = response.get("UploadTimeTaken", "")
            if response["Status"] == "Canceled":
                self.time_calculator()
                self.update_request_status(status_id=5, stage_id=6)
                logger.error(f"Error in Upload Queue for task {upload_task['ImportRequestId']}: {response}")
                self.error_log(logs=f"Error in Upload Queue for task {upload_task['ImportRequestId']}: {response}")
            else:
                logger.info(f"Import process completed successfully: {self.ImportRequestId}")
                self.time_calculator()
                self.update_request_status(status_id=4, stage_id=6)
                self.result(logs=f"Import process completed successfully: {self.ImportRequestId}")
        else:
            logger.error("Task processing failed. Import process aborted.")
            self.error_log(logs="Task processing failed. Import process aborted.")

        return connection, channel
