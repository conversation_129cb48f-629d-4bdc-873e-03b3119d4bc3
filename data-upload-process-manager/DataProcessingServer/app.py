import signal
import sys
import platform
import time
from server import logger, middleware

def signal_handler(_sig, _frame):
    """Handle shutdown signals gracefully."""
    logger.info("Shutdown signal received. Shutting down gracefully...")
    middleware.shutdown()
    sys.exit(0)

if __name__ == '__main__':
    # Register signal handlers
    signal.signal(signal.SIGINT, signal_handler)   # Ctrl+C
    signal.signal(signal.SIGTERM, signal_handler)  # Termination

    logger.info("Data Processing Server started. RabbitMQ middleware is running...")

    try:
        # Check the operating system
        if platform.system() == "Windows":
            # Use sleep loop for Windows
            while True:
                time.sleep(1)
        else:
            # Use signal.pause() on Linux/Unix
            signal.pause()
    except (KeyboardInterrupt, SystemExit):
        pass
