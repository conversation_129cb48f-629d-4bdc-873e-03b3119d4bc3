[Data]
VENV = D:\PythonProjects\DataProcessingServer/venv/Scripts/python
TYPE = DataProcessingServer
PORT = # PORT
ISPRODUCTION = # ISPRODUCTION
SCHEDULERSEC = # SECOND
RABBITMQ = # RABBITMQ connection string
BUCKET_UUID = # BUCKET_UUID
UPLOAD_FILE_PATH = # UPLOAD_FILE_PATH

[PublisherSchedulerData]
TYPE = PublisherScheduler

[TestingAPI]
MiddlewareBaseUrl = # Testing Api Baseurl
FileManagerBaseUrl = # Testing Api cloud doc Api

[ProductionApi]
MiddlewareBaseUrl = # Live Api Baseurl
FileManagerBaseUrl = # Live Api cloud doc Api

[MongoDB]
URL = # Mongodb connection URL
DATABASE_NAME = # Temp Dataimporting database name

[PATH]
MainTempFolder = Temp
ScriptsFolder = Scripts
LogFilesFolder = Log
QueueLogFilesFolder = Log/QueueLog
RequestLogFilesFolder = Log/RequestLog
Input = Temp/Input
ProductTemp = Temp/ProductSheet
PriceTemp = Temp/PriceSheet
StockTemp = Temp/StockSheet
ReportTemp = Temp/ReportSheet

[RequiredColumn]
REQUIRED_COLUMNS = CASNumber,MDLNumber,AvailabilityID,ProductURL,Smile,Purity,ProductName
MANDATORY_COLUMNS = SupplierCatalogID,SupplierID

[Scripts]
AllowedScript = [
                 {"TypeId":1, "ScriptName":"AllImport"},
                 {"TypeId":2, "ScriptName":"ProductImport"},
                 {"TypeId":3, "ScriptName":"PriceImport"},
                 {"TypeId":4, "ScriptName":"StockImport"}
                ]

[CREDENTIAL]
SERVER = # server ip
USERNAME = # server username
PASSWORD = # server password
DATABASE = # Database name