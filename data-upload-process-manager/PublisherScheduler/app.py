import json
import threading
import pika
import sys
import configparser
import os
import requests
import time
from loggerfile import logger


def find_config_file(filename="config.cfg", max_levels=2):
    """Finds the config file by searching only up to two parent directories."""
    current_folder = os.path.dirname(os.path.abspath(__file__))

    for _ in range(max_levels + 1):  # Search up to 'max_levels' parents
        config_path = os.path.join(current_folder, filename)
        if os.path.isfile(config_path):
            return config_path  # Return the first match found
        current_folder = os.path.dirname(current_folder)  # Move one level up

    raise FileNotFoundError(f"{filename} not found within {max_levels} parent directories.")


# Get the config file path
cfgfile = find_config_file()

print(f"\n{'*' * 20}\nConfig File Path: {cfgfile}\nFile Exists: {os.path.isfile(cfgfile)}\n{'*' * 20}\n")

config = configparser.ConfigParser()
config.read(cfgfile)

rabbit_mq = config.get('Data', 'RABBITMQ')
scheduler_interval = config.getint('Data', 'SCHEDULERSEC')
ISPRODUCTION = config.getboolean("Data", "ISPRODUCTION")


class PublisherWithScheduler:
    def __init__(self):
        self.connection = None
        self.channel = None
        self.lock = threading.Lock()
        self.__middleware_base_url = config.get("ProductionApi", "MiddlewareBaseUrl") if ISPRODUCTION else config.get(
            "TestingAPI", "MiddlewareBaseUrl")
        self.__update_request_endpoint = self.__middleware_base_url + "api/updateRequestStatus"
        self.connect_to_rabbitmq()

    def connect_to_rabbitmq(self):
        """Connect to RabbitMQ and declare the queue."""
        while True:
            try:
                # self.connection = pika.BlockingConnection(pika.ConnectionParameters(rabbit_mq))

                connection_params = pika.URLParameters(rabbit_mq)

                # Additional connection settings
                connection_params.heartbeat = 30000
                connection_params.blocked_connection_timeout = 30000
                connection_params.retry_delay = 5
                connection_params.connection_attempts = 5

                # Establish the connection with the specified parameters
                self.connection = pika.BlockingConnection(connection_params)

                self.channel = self.connection.channel()
                self.channel.queue_declare(queue="request_queue", durable=True)
                logger.info("Connected to RabbitMQ and request_queue declared.")
                break
            except Exception as e:
                logger.error(f"Failed to connect to RabbitMQ: {e}. Retrying in 5 seconds...")
                time.sleep(5)

    def get_open_product_import_request(self):
        try:
            get_open_product = self.__middleware_base_url + "api/getRemainingFile"
            res = requests.get(get_open_product, verify=False)
            return res
        except Exception as e:
            print(e)
            return

    def fetch_tasks(self):
        try:
            response = self.get_open_product_import_request()
            if response and response.status_code == 200:
                tasks = response.json().get("data", [])
                return tasks
            else:
                logger.error("Failed to fetch valid tasks.")
                return []
        except Exception as e:
            logger.error(f"Error fetching tasks: {e}")
            return []

    def publish_task(self, task):
        """Publish a single task to the request queue."""
        try:
            # Check if the task is an empty list
            if task == []:
                message_properties = {
                    'expiration': '60000'  # 60000 ms = 1 minute
                }
            else:
                message_properties = {}

            self.channel.basic_publish(
                exchange="",
                routing_key="request_queue",
                body=json.dumps(task),
                properties=pika.BasicProperties(
                    expiration=message_properties.get('expiration', None)  # Apply TTL if task is []
                )
            )
            logger.info(f"Task published to request_queue: {task}")
        except pika.exceptions.AMQPConnectionError:
            logger.error("Connection to RabbitMQ lost. Reconnecting...")
            self.connect_to_rabbitmq()
            self.publish_task(task)  # Retry publishing the task

    def scheduler_function(self):
        """Scheduler function to fetch and publish tasks periodically."""
        with self.lock:
            sys.stdout.write('\rScheduler function executed.\n')
            tasks = self.fetch_tasks()
            if tasks:
                for task in tasks:
                    print(task)
                    self.update_request(import_id=task.get("_id"), status_id=2, stage_id=1, request_time=None,
                                        sub_stage_id=None, process_request_time={})
                    self.publish_task(task)
            else:
                # self.publish_task(tasks)
                print("No new tasks to publish.")

        # Schedule the next execution
        threading.Timer(scheduler_interval, self.scheduler_function).start()

    def update_request(self, status_id: int, stage_id: int, import_id: int, request_time, sub_stage_id: int,
                       process_request_time: dict):
        try:
            payload = {"StatusId": status_id, "StageId": stage_id, "ImportRequestID": import_id}
            if request_time is not None:
                payload["RequestTime"] = request_time
            if sub_stage_id is not None:
                payload["SubStageId"] = sub_stage_id
            if process_request_time != {}:
                payload["ProcessRequestTime"] = process_request_time

            resp = self.post(self.__update_request_endpoint, payload=payload)
            print(resp.json())
            return resp
        except Exception as e:
            print(e)
            return

    def post(self, endpoint: str, payload: dict):

        try:
            res = requests.put(endpoint, json=payload, verify=False)
            return res
        except Exception as e:
            logger.error(e, exc_info=True)
            return

    def start(self):
        """Start the scheduler."""
        logger.info("Starting the publisher scheduler...")
        self.scheduler_function()


if __name__ == "__main__":
    publisher = PublisherWithScheduler()
    publisher.start()
