import logging
import os
from datetime import datetime

thisfolder = os.path.dirname(os.path.abspath(__file__))

# Make sure the 'Log' directory exists
log_dir = os.path.join(thisfolder, 'Log')
os.makedirs(log_dir, exist_ok=True)  # This ensures the directory exists

# Create a logger
logger = logging.getLogger('my_logger')
logger.setLevel(logging.INFO)

# Create a StreamHandler to output log messages to stdout
stream_handler = logging.StreamHandler()
stream_handler.setLevel(logging.INFO)

# Make a path for logfiles
log_file = os.path.join(thisfolder, 'Log', f'{datetime.today().strftime("%d-%m-%Y")}.txt')

# Create a FileHandler to store log messages in a file
file_handler = logging.FileHandler(log_file)
file_handler.setLevel(logging.INFO)

# Create a formatter and add it to the handlers
formatter = logging.Formatter(
    "[%(asctime)s]:[PID %(process)d]:[%(levelname)s]:%(name)s::%(filename)s|%(lineno)d|%(message)s",
    datefmt='%d-%m-%Y %H:%M:%S')
stream_handler.setFormatter(formatter)
file_handler.setFormatter(formatter)

# Add the handlers to the logger
logger.addHandler(stream_handler)
logger.addHandler(file_handler)
