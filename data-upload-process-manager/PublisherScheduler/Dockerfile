# Use an official Microsoft runtime as a parent image
FROM mcr.microsoft.com/windows/servercore:ltsc2019

# Use the official small size Python 3.10-slim base image as runtime
FROM python:3.10-slim AS runtime

# Set the working directory to /app within the container
WORKDIR /app

# Copy only the necessary files for installing Python dependencies
COPY requirements.txt .

# Install Python dependencies from requirements.txt, skipping cache
RUN pip install --no-cache-dir -r requirements.txt

# Copy the entire application code into the container
COPY . .

# Define the command to run when the container starts
CMD ["python", "app.py"]
