services:
  # Data Processing Server
  dataprocessor-server-prod:
    image: dataprocessor-server-prod:latest
    build:
      context: ./DataProcessingServer
      dockerfile: Dockerfile
    restart: unless-stopped
    environment:
      - PORT=1234
      - PRODUCTION=True
    hostname: dataprocessor-server-prod
    container_name: dataprocessor-server-prod
    volumes:
      - ./config.cfg:/app/config.cfg  # Mount the config file inside the container

  # # Publisher Scheduler
  # publisher-scheduler-queue-prod:
  #   image: publisher-scheduler-queue-prod:latest
  #   build: 
  #     context: ./PublisherScheduler
  #     dockerfile: Dockerfile
  #   restart: unless-stopped
  #   environment:
  #     - PRODUCTION=True
  #   hostname: publisher-scheduler-queue-prod
  #   container_name: publisher-scheduler-queue-prod
  #   volumes:
  #     - ./config.cfg:/app/config.cfg  # Mount the same config file for this service if needed
