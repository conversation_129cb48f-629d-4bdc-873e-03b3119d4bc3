import os
import io

import pandas as pd
from datetime import datetime, timezone
from httpx import AsyncClient
from fastapi import APIRouter, Depends, File, Form, UploadFile

from app.services.db_service import save_data_to_db, get_db
from app.services.update_request import update_request_obj
from app.utils.settings import ALLOWED_EXTENSIONS, CLOUD_DOC_BASE_URL, BUCKET_UUID, REQ_COLLECTION_NAME, CLOUD_DOC_BASE_URL, ROW_LIMIT
from app.utils.messages import (
    FILE_CREATED_SUCCESSFULLY_MESSAGE,
    ERROR_INVALID_FILE_FORMAT,
    FAILED_TO_UPLOAD_FILE,
    ERROR_DB_SAVE_FAILED,
    FILE_EXCEPTION_GENERIC
)
from app.schemas.success_responses import SuccessResponse
from app.schemas.error_responses import ErrorResponse
from app.utils.utils import generate_unique_4_digit
from app.utils.rabitmq_client import RabbitMQClient

router = APIRouter()

# Define row limit per request
# ROW_LIMIT = 20000

@router.post("/upload", response_model=SuccessResponse, deprecated=True)
async def create_data_import_request(
        SupplierID: str = Form(...),
        SupplierName: str = Form(...),
        RequestTypeId: int = Form(...),
        file: UploadFile = File(...),
        CreatedDate: str = Form(None),  # Optional date parameter
        db=Depends(get_db)
):
    """
    Handles file upload and processing.

    - Validates the file extension.
    - Splits the file if row count exceeds 1 lakh.
    - Sends each chunk separately to the external service.
    - Saves metadata and request details to the database.
    - Accepts an optional date parameter that will be used as the CreatedDate if provided.
      If no date is provided, the current date and time will be used.
    """
    collection_data = {
        "collection_data": [
            {"name": "Status", "obj_key": "StatusObj", "mapping_key": ["StatusId", "StatusName"]},
            {"name": "Stages", "obj_key": "StageObj", "mapping_key": ["StageId", "StageName"]},
            {"name": "RequestType", "obj_key": "RequestTypeObj", "mapping_key": ["RequestTypeID", "RequestTypeName"]},
        ]
    }

    try:
        # Validate file extension
        file_extension = os.path.splitext(file.filename)[1].lower().strip('.')
        if file_extension not in ALLOWED_EXTENSIONS:
            return ErrorResponse(
                message=ERROR_INVALID_FILE_FORMAT.format(allowed_formats=', '.join(ALLOWED_EXTENSIONS)),
                status_code=400
            )

        file_content = await file.read()

        # Load file data into a DataFrame
        if file_extension == "csv":
            df = pd.read_csv(io.BytesIO(file_content))
        else:
            df = pd.read_excel(io.BytesIO(file_content))

        # Load file data into a DataFrame
        # df = pd.read_csv(io.BytesIO(file_content)) if file_extension in ["csv"] else pd.read_excel(io.BytesIO(file_content))

        # Get total rows
        total_rows = len(df)

        if total_rows > ROW_LIMIT:
            num_parts = (total_rows // ROW_LIMIT) + (1 if total_rows % ROW_LIMIT > 0 else 0)

            # Generate a unique batch ID for all split requests
            request_gorup_id = generate_unique_4_digit()

        else:
            num_parts = 1
            request_gorup_id = None

        responses = []

        for i in range(num_parts):
            start_row = i * ROW_LIMIT
            end_row = min((i + 1) * ROW_LIMIT, total_rows)

            # Extract subset of data
            df_chunk = df.iloc[start_row:end_row]

            # Convert subset to a new file-like object
            output_buffer = io.BytesIO()
            file_part_name = f"{file.filename}_part{i+1}.{file_extension}"

            if file_extension == "csv":
                df_chunk.to_csv(output_buffer, index=False)
            else:
                df_chunk.to_excel(output_buffer, index=False, engine="openpyxl")

            output_buffer.seek(0)

            # Send the file to the external API
            async with AsyncClient() as client:
                files = {'file': (f"{file_part_name}", output_buffer)}
                res = await client.post(
                        f"{CLOUD_DOC_BASE_URL}/api/external/file/uploadFile/", files=files,
                        params={"bucket_uuid": BUCKET_UUID}
                )

            res_data = res.json()

            if res_data['status_code'] == 200:
                # Prepare data for saving to database
                # Use created date if provided, otherwise use current date
                created_date = datetime.now(timezone.utc)

                if CreatedDate:
                    try:
                        # Attempt to parse the provided CreatedDate
                        selected_date = datetime.strptime(CreatedDate, '%Y-%m-%d')
                        now = datetime.now(timezone.utc)
                        created_date = selected_date.replace(hour=now.hour, minute=now.minute, second=now.second, microsecond=now.microsecond, tzinfo=timezone.utc)
                    except ValueError:
                        # If date parsing fails, use current date
                        pass

                data = {
                    "RequestGroupID": request_gorup_id,
                    "StatusObj": 2,
                    "StageObj": 1,
                    "RequestTypeObj": RequestTypeId,
                    "InputFileName": res_data['data']['filename'],
                    "SupplierID": SupplierID,
                    "SupplierName": SupplierName,
                    "FileUUID": res_data['data']['uuid'],
                    "RequestTime": None,
                    "CreatedDate": created_date,
                    "UpdatedDate": datetime.now(timezone.utc),
                }

                # Update request object and save to DB
                updated_status_obj = await update_request_obj(data, db, collection_data)
                success = await save_data_to_db(REQ_COLLECTION_NAME, updated_status_obj, db)
                publisher = RabbitMQClient(queue_name="request_queue")
                publisher.publish(updated_status_obj)
                if success:
                    responses.append({
                        "message": FILE_CREATED_SUCCESSFULLY_MESSAGE.format(filename=f"{file.filename}_part{i+1}"),
                        "status_code": 200,
                        "data": updated_status_obj
                    })
                else:
                    responses.append({
                        "message": ERROR_DB_SAVE_FAILED.format(filename=f"{file.filename}_part{i+1}"),
                        "status_code": 500
                    })
            else:
                responses.append({
                    "message": FAILED_TO_UPLOAD_FILE.format(service_error=res.text),
                    "status_code": res.status_code
                })

        return SuccessResponse(
            message="File processed successfully",
            status_code=200,
            data={"responses": responses}
        )

    except Exception as e:
        return ErrorResponse(
            message=FILE_EXCEPTION_GENERIC.format(error=str(e)),
            status_code=500
        )
