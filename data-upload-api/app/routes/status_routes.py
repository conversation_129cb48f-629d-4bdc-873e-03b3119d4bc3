from fastapi import APIRouter, Depends
from app.schemas.status_schemas import UpdateStageStatusRequest
from app.services.db_service import get_db
from app.services.update_request import update_request_obj
from app.utils.settings import REQ_COLLECTION_NAME
from app.utils.messages import (
    STATUS_UPDATE_SUCCESSFUL,
    DATA_NOT_FOUND,
    STATUS_EXCEPTION_GENERIC,
)
from app.schemas.success_responses import SuccessResponse
from app.schemas.error_responses import ErrorResponse

router = APIRouter()

@router.put("/updateRequestStatus")
async def update_file_stage_status(update_data: UpdateStageStatusRequest, db=Depends(get_db)):
    try:
        collection_data = {
            "collection_data": [
                {
                    "name": "Status",
                    "obj_key": "StatusId",
                    "mapping_key": ["StatusId", "StatusName"],
                },
                {
                    "name": "Stages",
                    "obj_key": "StageId",
                    "mapping_key": ["StageId", "StageName"],
                },
            ]
        }

        if update_data.SubStageId is not None:
            collection_data["collection_data"].append({
                "name": "SubStages",
                "obj_key": "SubStageId",
                "mapping_key": ["SubStageId", "SubStageName"],
                "stage_field": "StageId",
            })

        updated_status_obj = await update_request_obj(update_data.dict(), db, collection_data)
        import_request_id = updated_status_obj.get("ImportRequestID")

        if not import_request_id:
            return ErrorResponse(
                message=DATA_NOT_FOUND,
                status_code=404,
                data=[{"error": "ImportRequestID not found"}],
            )

        existing_doc = await db[REQ_COLLECTION_NAME].find_one({"_id": import_request_id})

        if not existing_doc:
            return ErrorResponse(
                message=DATA_NOT_FOUND,
                status_code=404,
                data=[{"ImportRequestID": import_request_id}],
            )

        stage_obj = updated_status_obj.get("StageId", {})
        status_obj = updated_status_obj.get("StatusId", {})

        update_query = {"$set": {}}

        if "StageId" in updated_status_obj:
            update_query["$set"]["StageObj.StageId"] = stage_obj.get("StageId")
            update_query["$set"]["StageObj.StageName"] = stage_obj.get("StageName")

        if "StatusId" in updated_status_obj:
            update_query["$set"]["StatusObj.StatusId"] = status_obj.get("StatusId")
            update_query["$set"]["StatusObj.StatusName"] = status_obj.get("StatusName")

        if update_data.RequestTime is not None:
            update_query["$set"]["RequestTime"] = str(update_data.RequestTime)

        # Ensure `ProcessRequestTimeList` is properly converted to a dictionary
        if update_data.ProcessRequestTime:
            update_query["$set"]["ProcessRequestTime"] = update_data.ProcessRequestTime if isinstance(update_data.ProcessRequestTime, dict) else update_data.ProcessRequestTime.model_dump()

        # First update without SubStageObj
        await db[REQ_COLLECTION_NAME].update_one(
            {"_id": import_request_id},
            update_query,
        )

        # Now update SubStageObj separately if provided
        if update_data.SubStageId is not None:
            substage_obj = updated_status_obj.get("SubStageId", {})
            await db[REQ_COLLECTION_NAME].update_one(
                {"_id": import_request_id},
                {"$set": {"StageObj.SubStageObj": substage_obj}},
            )

        return SuccessResponse(
            message=STATUS_UPDATE_SUCCESSFUL,
            status_code=200,
            data=[{"ImportRequestID": import_request_id, "Data": updated_status_obj}],
        )

    except Exception as e:
        return ErrorResponse(
            message=STATUS_EXCEPTION_GENERIC.format(error=str(e)),
            status_code=500,
            data=[{"error": str(e)}],
        )
