from fastapi import Depends, APIRouter
from app.schemas.error_responses import ErrorResponse
from app.schemas.success_responses import SuccessResponse
from app.services.db_service import get_db
from app.utils.settings import REQ_COLLECTION_NAME
from app.utils.messages import (
    FILE_NOT_FOUND,
    REMAINING_FILE_EXCEPTION_GENERIC,
)

router = APIRouter()

@router.get("/getRemainingFile", response_model=SuccessResponse)
async def get_remaining_file(db=Depends(get_db)):
    """
    Fetch all remaining files with a specific status.

    Parameters:
    - db: The database connection dependency.

    Returns:
    - A list of files that match the filter condition if found.
    - A 404 error if no files are found.
    - A 500 error if any unexpected exception occurs.
    """
    try:
        filter_condition = {"StatusObj.StatusId": 1}
        collection = db[REQ_COLLECTION_NAME]

        remaining_files_cursor = collection.find(filter_condition)
        remaining_files = await remaining_files_cursor.to_list(length=None)

        if not remaining_files:
            return ErrorResponse(
                message=FILE_NOT_FOUND,
                status_code=404
            )

        return SuccessResponse(
            message="Remaining files fetched successfully.",
            status_code=200,
            data=remaining_files
        )

    except Exception as e:
        return ErrorResponse(
            message=REMAINING_FILE_EXCEPTION_GENERIC.format(error=str(e)),
            status_code=500
        )