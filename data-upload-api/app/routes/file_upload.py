from datetime import datetime
from pathlib import Path

import asyncio
from fastapi import APIRouter, Form, File, UploadFile, HTTPException

from app.schemas.success_responses import SuccessResponse
from app.schemas.error_responses import ErrorResponse
from app.tasks.worker import process_excel_task
from app.utils.file_utils import save_uploaded_file, get_system_memory, get_dynamic_semaphore_limit
from app.utils.settings import ALLOWED_EXTENSIONS

router = APIRouter()


@router.post("/new-upload", response_model=SuccessResponse)
async def upload_excel(
    SupplierID: str = Form(...),
    SupplierName: str = Form(...),
    RequestTypeId: int = Form(...),
    file: UploadFile = File(...),
    CreatedDate: str = Form(None),  # Optional date parameter
):
    file_extension = Path(file.filename).suffix.lower()[1:]  # Extract file extension without dot (e.g., 'xlsx')

    if file_extension not in ALLOWED_EXTENSIONS:
        raise HTTPException(status_code=400, detail=f"Only the following file types are allowed: {', '.join(ALLOWED_EXTENSIONS)}")
    
    try:
        # Get the available system memory once
        available_memory = get_system_memory()

        # Dynamically get the concurrency limit based on available system memory
        concurrency_limit = get_dynamic_semaphore_limit(available_memory)

        # Create a semaphore based on the dynamic limit
        upload_semaphore = asyncio.Semaphore(concurrency_limit)

        # Acquire the semaphore to limit concurrent uploads
        async with upload_semaphore:
            # Generate a timestamped filename for the uploaded file
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            filename = f"{timestamp}_{file.filename}"

            # Save the uploaded file asynchronously
            file_path = await save_uploaded_file(file, filename, available_memory)
            
            # Pass all the form data along with the file path to the Celery task
            task = process_excel_task.delay(
                file_path,
                SupplierID,
                SupplierName,
                RequestTypeId,
                CreatedDate
            )
            
            return SuccessResponse(
                message="File uploaded successfully and processing started",
                status_code=200,
                data={"responses": 'File processing will start shortly', "task_id": task.id, "filename": filename}
            )
    
    except Exception as e:
        return ErrorResponse(
            message=f"Error processing file: {str(e)}",
            status_code=500
        )
