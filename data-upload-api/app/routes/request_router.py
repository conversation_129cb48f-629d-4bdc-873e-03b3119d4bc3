from typing import Optional, Dict
from fastapi import APIRouter, Query, Request as fastapi_request
from fastapi.responses import HTMLResponse, JSONResponse
from jinja2 import Environment, FileSystemLoader
from app.schemas.request_schemas import Request, RequestListResponse
from app.services.request_service import get_data_import_requests, get_data_import_request_by_id, get_request_counts
from app.services.approval_service import transform_request
from app.schemas.error_responses import ErrorResponse
from app.schemas.success_responses import SuccessResponse
from app.utils.settings import BUCKET_UUID, CLOUD_DOC_BASE_URL

router = APIRouter()

# Define the path for Jinja2 templates
template_env = Environment(loader=FileSystemLoader('templates'))

@router.get("/requests", response_model=RequestListResponse)
async def get_requests(
    skip: int = Query(0, ge=0),
    limit: int = Query(10, ge=1, le=100),
    search_value: Optional[str] = Query(None, alias="search[value]"),
    order_column: Optional[int] = Query(0, alias="order[0][column]"),
    order_dir: Optional[str] = Query("asc", alias="order[0][dir]"),
    active_tab: Optional[str] = Query(None, alias="active_tab")
):
    """
    Fetch paginated, sorted, and searched request data.
    """
    # Define the sorting column mapping
    sort_columns = ["requestTypeId", "InputFileName", "StatusObj.StatusName", "CreatedDate"]
    sort_field = sort_columns[order_column] if order_column < len(sort_columns) else "CreatedDate"
    # Set filter conditions based on tab selection
    if active_tab == "openTab":
        status_filter = {"StatusObj.StatusId": {"$in": [1, 2, 3, 6, 7]}}
    else:
        status_filter = {"StatusObj.StatusId": {"$in": [4, 5, 8]}}
    # Fetch data from MongoDB
    requests_data, total_filtered, total_records = await get_data_import_requests(
        search_value=search_value,
        status_filter=status_filter,
        skip=skip,
        limit=limit,
        sort_field=sort_field,
        sort_direction=order_dir
    )

   # Format response data
    requests = [
            Request(**{
            **request,
            "requestTypeId": str(request.get("_id")),
            "request_group_id": request.get("RequestGroupID") if request.get("RequestGroupID") is not None else None
        })
        for request in requests_data
    ]

    return RequestListResponse(
        data=requests,  # The requested page of data
        recordsTotal=total_records,  # Total number of records without filters
        recordsFiltered=total_filtered  # Number of records after filtering but before pagination
    )

@router.get("/request_detail/{request_id}")
async def get_request_detail(url_request: fastapi_request, request_id: str):
    """
    Fetch detailed information about a specific request based on its _id.
    """
    try:
        request_object_id = request_id
    except Exception as e:
        return ErrorResponse(
                message=str(e),
                status_code=500,
                data={},
            )

    request_data = await get_data_import_request_by_id(request_object_id)

    if not request_data:
        return SuccessResponse(
            message="Request not found",
            status_code=200,
            data={}
        )

    # Transform the request to get approval reasons
    transformed_data = await transform_request(request_data)

    # Merge the original request data with the transformed data
    request_dict = {
        **request_data,
        "requestTypeId": str(request_data.get("_id")),
        "OutputFile": request_data.get("OutputFile", []),
        "downloadFileBaseURL": CLOUD_DOC_BASE_URL,
        "bucketUUID": BUCKET_UUID,
        # Add approval reasons and categories from the transformed data
        "approval_reasons": transformed_data.get("approval_reasons", []),
        "approval_categories": transformed_data.get("approval_categories", {}),
        "is_multi_scenario": transformed_data.get("is_multi_scenario", False)
    }
    print(request_dict)

    import json

    print("*" * 40 + "\n")
    print(json.dumps(request_dict, indent=4, default=str))
    print("*" * 40 + "\n")

    # Render the HTML page using Jinja2 template and pass the base_url
    template = template_env.get_template('request_detail.html')
    return HTMLResponse(content=template.render(request=request_dict, request_url=url_request), status_code=200)

@router.get("/api/request-counts")
async def get_counts() -> Dict:
    """
    Get counts of requests by status and today's requests.

    Returns:
        Dict: A dictionary containing counts for total, completed, pending, and today's requests.
    """
    try:
        counts = await get_request_counts()
        return counts
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"error": f"Failed to fetch request counts: {str(e)}"}
        )
