# approval_router.py
from fastapi import APIRouter, HTTPException, status, Depends
from bson import ObjectId
from app.schemas.approver_schemas import (
    ApprovalRequest,
    RequestResponse,
    PendingRequestResponse
)
from app.services.approval_service import (
    update_request_status_service,
    fetch_approved_requests_service,
    fetch_pending_requests_service,
    fetch_rejected_requests_service
)
from app.services.db_service import get_db
from app.utils.settings import REQ_COLLECTION_NAME
from typing import List

router = APIRouter()

# Route to approve/reject requests
@router.put("/requests/{request_id}/status", response_model=RequestResponse)
# async def update_request_status(request_id: str, approval: ApprovalRequest):
async def update_request_status(
    request_id: int,
    approval: ApprovalRequest,
    db=Depends(get_db)
):
    """ API endpoint to approve or reject requests """
    # Call the service to update the request status
    # status = await update_request_status_service(request_id, approval.approved)
    print("*"*40)
    print(request_id, approval.approved)

    status = await update_request_status_service(request_id, approval.approved, db)

    print(f"{status = }")
    print("*"*40)


    if status is None:
        request = await db[REQ_COLLECTION_NAME].find_one({"_id": request_id})
        if not request:
            raise HTTPException(status_code=404, detail="Request not found")

        # If request exists but status is None, it means it's not in Pending status
        current_status = request.get("StatusObj", {}).get("StatusName", "Unknown")
        raise HTTPException(
            status_code=400,
            detail=f"Only requests in 'Pending' status can be approved or rejected. Current status {current_status}"
        )

    # Return the updated status
    return {"request_id": request_id, "status": status}

# Route to fetch all approved requests
@router.get("/requests/approved/", response_model=List[PendingRequestResponse])
async def get_approved_requests():
    """ API endpoint to retrieve all approved requests """
    return await fetch_approved_requests_service()

# Route to fetch all pending requests
@router.get("/requests/pending/", response_model=List[PendingRequestResponse])
async def get_pending_requests():
    """ API endpoint to retrieve all pending requests in approval stage """
    return await fetch_pending_requests_service()

# Route to fetch all rejected requests
@router.get("/requests/rejected/", response_model=List[PendingRequestResponse])
async def get_rejected_requests():
    """ API endpoint to retrieve all rejected requests """
    return await fetch_rejected_requests_service()
