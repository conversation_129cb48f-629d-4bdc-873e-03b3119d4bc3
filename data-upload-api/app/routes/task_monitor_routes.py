from fastapi import APIRouter, HTTPException, Query, Path, Depends, Request
from typing import Optional
import logging

from app.schemas.task_monitor_schemas import (
    TaskListResponse, WorkerListResponse, TaskDetailResponse,
    MonitoringResponse, TaskActionRequest, TaskFilterRequest
)
from app.services.celery_monitor_service import celery_monitor

logger = logging.getLogger(__name__)
router = APIRouter()


def get_current_user(request: Request):
    """Check if the user is authenticated"""
    user = request.cookies.get("user")
    if not user:
        raise HTTPException(status_code=401, detail="Authentication required")
    return user


@router.get("/tasks", response_model=TaskListResponse)
async def get_tasks(
    state: Optional[str] = Query(None, description="Filter by task state"),
    worker: Optional[str] = Query(None, description="Filter by worker"),
    limit: int = Query(100, description="Maximum number of tasks to return"),
    offset: int = Query(0, description="Number of tasks to skip"),
    user: str = Depends(get_current_user)
):
    """Get list of tasks with optional filtering"""
    try:
        logger.info(f"Getting tasks with filters: state={state}, worker={worker}, limit={limit}, offset={offset}")
        
        # For now, we'll use the existing get_tasks method
        # In the future, we can enhance it to support offset and worker filtering
        tasks_response = await celery_monitor.get_tasks(limit=limit, state=state)
        
        return tasks_response
    except Exception as e:
        logger.error(f"Error getting tasks: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error retrieving tasks: {str(e)}")


@router.get("/tasks/{task_id}", response_model=TaskDetailResponse)
async def get_task_detail(
    task_id: str = Path(..., description="Task ID"),
    user: str = Depends(get_current_user)
):
    """Get detailed information about a specific task"""
    try:
        logger.info(f"Getting task detail for task_id: {task_id}")
        
        task_detail = await celery_monitor.get_task_detail(task_id)
        
        if not task_detail:
            raise HTTPException(status_code=404, detail=f"Task {task_id} not found")
        
        return task_detail
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting task detail for {task_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error retrieving task detail: {str(e)}")


@router.get("/workers", response_model=WorkerListResponse)
async def get_workers(user: str = Depends(get_current_user)):
    """Get list of workers and their status"""
    try:
        logger.info("Getting workers list")
        
        workers_response = await celery_monitor.get_workers()
        
        return workers_response
    except Exception as e:
        logger.error(f"Error getting workers: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error retrieving workers: {str(e)}")


@router.post("/tasks/{task_id}/revoke", response_model=MonitoringResponse)
async def revoke_task(
    action_request: TaskActionRequest,
    task_id: str = Path(..., description="Task ID"),
    user: str = Depends(get_current_user)
):
    """Revoke or terminate a task"""
    try:
        logger.info(f"Revoking task {task_id} with action: {action_request.action}")
        
        if action_request.action not in ["revoke", "terminate"]:
            raise HTTPException(status_code=400, detail="Invalid action. Must be 'revoke' or 'terminate'")
        
        terminate = action_request.action == "terminate" or action_request.terminate
        
        result = await celery_monitor.revoke_task(task_id, terminate=terminate)
        
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error revoking task {task_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error revoking task: {str(e)}")


@router.get("/tasks/{task_id}/result", response_model=MonitoringResponse)
async def get_task_result(
    task_id: str = Path(..., description="Task ID"),
    user: str = Depends(get_current_user)
):
    """Get task result"""
    try:
        logger.info(f"Getting task result for task_id: {task_id}")
        
        result = await celery_monitor.get_task_result(task_id)
        
        return result
    except Exception as e:
        logger.error(f"Error getting task result for {task_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error retrieving task result: {str(e)}")


@router.get("/stats")
async def get_task_stats(user: str = Depends(get_current_user)):
    """Get task and worker statistics"""
    try:
        logger.info("Getting task and worker statistics")
        
        # Get worker stats
        worker_stats = await celery_monitor.get_worker_stats()
        
        # Get task stats (we'll get this from the tasks list)
        tasks_response = await celery_monitor.get_tasks(limit=1000)  # Get more tasks for better stats
        
        stats = {
            "workers": worker_stats,
            "tasks": {
                "total": tasks_response.stats.total,
                "active": tasks_response.stats.active,
                "processed": tasks_response.stats.processed,
                "failed": tasks_response.stats.failed,
                "succeeded": tasks_response.stats.succeeded,
                "retried": tasks_response.stats.retried,
                "revoked": tasks_response.stats.revoked
            }
        }
        
        return stats
    except Exception as e:
        logger.error(f"Error getting statistics: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error retrieving statistics: {str(e)}")
