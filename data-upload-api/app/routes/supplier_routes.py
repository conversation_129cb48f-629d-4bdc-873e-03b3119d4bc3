from typing import List

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel

from app.services.db_service import get_db
from app.services.supplier_service import get_all_suppliers, Supplier
from app.schemas.supplier_schemas import Supplier


router = APIRouter()

@router.get("/suppliers", response_model=List[Supplier])
async def get_suppliers(db=Depends(get_db)):
    """Fetch suppliers from both OMS and MongoDB"""
    try:
        suppliers = await get_all_suppliers(db)
        if not suppliers:
            raise HTTPException(status_code=404, detail="No suppliers found")
        return suppliers
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    