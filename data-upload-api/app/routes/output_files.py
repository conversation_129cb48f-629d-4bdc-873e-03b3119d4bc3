from bson import ObjectId
from fastapi import Depends, APIRouter
from app.schemas.error_responses import ErrorResponse
from app.schemas.output_schemas import OutputFileload
from app.schemas.success_responses import SuccessResponse
from app.services.db_service import get_db
from app.services.update_request import update_request_obj
from app.utils.settings import REQ_COLLECTION_NAME
from app.utils.messages import (
    DATA_IMPORT_REQUEST_NOT_FOUND,
    OUTPUT_EXCEPTION_GENERIC,
    OUTPUT_FILE_ADDED,
)

router = APIRouter()

@router.post("/addOutputFile", response_model=SuccessResponse)
async def add_output_file(payload: OutputFileload, db=Depends(get_db)):
    """
    Add an output file to an existing Data Import Request.

    Parameters:
    - payload (OutputFileload): The output file details to be added.
    - db: The database connection dependency.

    Returns:
    - Success message with the ImportRequestID if file is successfully added.
    - Error message if the Data Import Request is not found or if any exception occurs.
    """
    try:
        import_request_id = (
            ObjectId(payload.ImportRequestID)
            if ObjectId.is_valid(payload.ImportRequestID)
            else payload.ImportRequestID
        )

        data_import_request = await db[REQ_COLLECTION_NAME].find_one({"_id": import_request_id})

        if not data_import_request:
            return ErrorResponse(
                message=DATA_IMPORT_REQUEST_NOT_FOUND,
                status_code=404
            )

        payload_data = payload.dict()
        del payload_data["ImportRequestID"]

        updated_status_obj = await update_request_obj(payload_data, db, {
            "collection_data": [
                {
                    "name": "OutputFileType",
                    "obj_key": "OutputFileTypeId",
                    "mapping_key": ["OutputFileTypeId", "OutputFileType"]
                },
                {
                    "name": "ImportType",
                    "obj_key": "ImportTypeId",
                    "mapping_key": ["ImportTypeId", "ImportTypeName"]
                }
            ]
        })

        result = await db[REQ_COLLECTION_NAME].update_one(
            {"_id": import_request_id},
            {"$push": {"OutputFile": updated_status_obj}}
        )

        if result.modified_count == 0:
            return ErrorResponse(
                message=OUTPUT_EXCEPTION_GENERIC.format(error="Failed to update the Data Import Request with the new output file"),
                status_code=500
            )

        return SuccessResponse(
            message=OUTPUT_FILE_ADDED.format(filename=payload.FileName),
            status_code=200,
            data={"ImportRequestID": payload.ImportRequestID, "Data": updated_status_obj}
        )

    except Exception as e:
        return ErrorResponse(
            message=OUTPUT_EXCEPTION_GENERIC.format(error=str(e)),
            status_code=500
        )