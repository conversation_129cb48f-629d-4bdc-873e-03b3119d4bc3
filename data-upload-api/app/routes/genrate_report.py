import io
import csv
import json
from fastapi import APIRouter, Depends
from app.services.db_service import get_db
from app.utils.messages import REMAINING_FILE_EXCEPTION_GENERIC
from app.schemas.success_responses import SuccessResponse
from app.utils.settings import REQ_COLLECTION_NAME
from app.schemas.error_responses import ErrorResponse
from fastapi.responses import StreamingResponse

router = APIRouter()

# Helper function to safely get values from a dictionary or return default if None
def safe_get(d, keys, default=''):
    """
    This function safely gets the value from a dictionary using a sequence of keys.
    If any key is missing or the dictionary is None, it returns a default value.
    """
    if d is None:
        return default
    for key in keys:
        d = d.get(key, {})
        if d is None:
            return default
    return d or default

def flatten_data_for_csv(data):
    flattened_data = []
    all_keys = []  # Maintain order for field names

    for item in data:
        row = {
            "Id": safe_get(item, ['_id']),
            "InputFileName": safe_get(item, ['InputFileName']),
            "StatusId": safe_get(item, ['StatusObj', 'StatusId']),
            "StatusName": safe_get(item, ['StatusObj', 'StatusName']),
            "StageId": safe_get(item, ['StageObj', 'StageId']),
            "StageName": safe_get(item, ['StageObj', 'StageName']),
            "RequestTypeID": safe_get(item, ['RequestTypeObj', 'RequestTypeID']),
            "RequestTypeName": safe_get(item, ['RequestTypeObj', 'RequestTypeName']),
            "CreatedDate": safe_get(item, ['CreatedDate']),
            "UpdatedDate": safe_get(item, ['UpdatedDate']),
            "FileUUID": safe_get(item, ['FileUUID'])
        }

        if not all_keys:
            all_keys.extend(row.keys())  # Ensure the base columns are in order

        # Handle OutputFile details
        output_file_keys = []
        for index, output_file in enumerate(item.get('OutputFile', []), start=1):
            row[f"OutputFileName_{index}"] = safe_get(output_file, ['FileName'])
            row[f"OutputFileId_{index}"] = safe_get(output_file, ['OutputFileObj', 'OutputFileId'])
            row[f"OutputFileType_{index}"] = safe_get(output_file, ['OutputFileObj', 'OutputFileType'])

            output_file_keys.extend([f"OutputFileName_{index}", f"OutputFileId_{index}", f"OutputFileType_{index}"])

        if output_file_keys:
            all_keys.extend(output_file_keys)  # Ensure output file columns appear in order

        # Handle RequestReport details in a single row with "_(index)" suffix
        request_report_keys = []
        for index, report in enumerate(item.get('RequestReport', []), start=1):
            row[f"ImportTypeId_{index}"] = safe_get(report, ['ImportTypeObj', 'ImportTypeId'])
            row[f"ImportTypeName_{index}"] = safe_get(report, ['ImportTypeObj', 'ImportTypeName'])
            row[f"InputDataCount_{index}"] = safe_get(report, ['InputDataCount'])
            row[f"NewDataCount_{index}"] = safe_get(report, ['NewDataCount'])
            row[f"ExistsDataCount_{index}"] = safe_get(report, ['ExistsDataCount'])
            row[f"ErrorDataCount_{index}"] = safe_get(report, ['ErrorDataCount'])
            row[f"UpdateDataCount_{index}"] = safe_get(report, ['UpdateDataCount'])
            row[f"DuplicateDataCount_{index}"] = safe_get(report, ['DuplicateDataCount'])

            request_report_keys.extend([
                f"ImportTypeId_{index}", f"ImportTypeName_{index}",
                f"InputDataCount_{index}", f"NewDataCount_{index}",
                f"ExistsDataCount_{index}", f"ErrorDataCount_{index}",
                f"UpdateDataCount_{index}", f"DuplicateDataCount_{index}"
            ])

        if request_report_keys:
            all_keys.extend(request_report_keys)  # Ensure request report columns appear in order

        flattened_data.append(row)

    return flattened_data, all_keys


@router.get("/download_csv")
async def download_csv(db=Depends(get_db)):
    try:
        collection = db[REQ_COLLECTION_NAME]

        # Fetch data from the database
        get_all_data = collection.find({})
        remaining_files = await get_all_data.to_list(length=None)

        if not remaining_files:
            return ErrorResponse(
                message="No data available to generate CSV.",
                status_code=404
            )

                # Flatten the data for CSV and get all keys (fieldnames)
        flattened_data, all_keys = flatten_data_for_csv(remaining_files)

        # Create an in-memory CSV file
        output = io.StringIO()
        writer = csv.DictWriter(output, fieldnames=all_keys)  # Sort keys if needed
        writer.writeheader()
        writer.writerows(flattened_data)


        # Seek to the beginning of the StringIO object before sending it
        output.seek(0)

        # Return CSV as downloadable file
        return StreamingResponse(output, media_type="text/csv", headers={"Content-Disposition": "attachment; filename=report.csv"})

    except Exception as e:
        return ErrorResponse(
            message=REMAINING_FILE_EXCEPTION_GENERIC.format(error=str(e)),
            status_code=500
        )
    
@router.get("/create_json")
async def create_json(db=Depends(get_db)):
    try:
        collection = db[REQ_COLLECTION_NAME]

        # Fetch data from the database
        get_all_data = collection.find({})
        remaining_files = await get_all_data.to_list(length=None)

        if not remaining_files:
            return ErrorResponse(
                message="No data available to generate JSON.",
                status_code=404
            )

        # Convert data to JSON format
        json_data = json.dumps(remaining_files, indent=4, default=str)

        # Create an in-memory file for JSON output
        output = io.StringIO()
        output.write(json_data)
        output.seek(0)

        # Return JSON as downloadable file
        return StreamingResponse(
            output,
            media_type="application/json",
            headers={"Content-Disposition": "attachment; filename=data.json"}
        )

    except Exception as e:
        return ErrorResponse(
            message=f"Error generating JSON file: {str(e)}",
            status_code=500
        )
