from fastapi import APIRouter, Request, Form
from fastapi.templating import <PERSON><PERSON><PERSON><PERSON>emplates
from fastapi.responses import RedirectResponse
from app.schemas.user_schema import UserLoginResponse
from app.utils.settings import CLOUD_DOC_BASE_URL
import httpx
from fastapi.responses import HTMLResponse

router = APIRouter()

# Setup Jinja2 templates for HTML rendering
templates = Jinja2Templates(directory="templates")

# GET method: Serve the login page
@router.get("/login")
async def get_login_page(request: Request):
    return templates.TemplateResponse("login.html", {"request": request})

# POST method: Handle login form submission
@router.post("/login")
async def post_login(request: Request, 
                     username: str = Form(...),  # Use Form to get form field data
                     password: str = Form(...)):  # Use Form for password as well
    # Prepare the request payload
    request_data = {
        "user_name": username,
        "password": password,
    }
    url = f"{CLOUD_DOC_BASE_URL}/api/authLogin/"

    # Use httpx to make an asynchronous HTTP request
    async with httpx.AsyncClient(timeout=30) as client:
        response = await client.post(url, json=request_data)

    # If the login is successful (e.g., status code 200), redirect to the target URL
    if response.json().get("message") == "User successfully authenticated":
        if response.status_code == 200 and response.json().get("data").get("is_authenticated") == True and response.json().get("data").get("is_active") == True:
            # Create the RedirectResponse and set cookies
            response_cookie = RedirectResponse(url="/", status_code = 303)  # Redirect to homepage or target page
            response_cookie.set_cookie(
                key="user", 
                value=username, 
            )

            return response_cookie

    # If login fails, you can return an error message or render the login page again with an error
    return templates.TemplateResponse("login.html", {"request": request, "error_message": "Login failed. Please try again."})

@router.get("/logout")
async def logout(request: Request):
    response = RedirectResponse(url="/login")  # Redirect to login page after logging out
    response.delete_cookie("user")  # Delete the user cookie
    return response
