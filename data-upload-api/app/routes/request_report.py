from bson import ObjectId
from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Depends, APIRouter
from app.schemas.data_import_request_schemas import RequestReport
from app.schemas.error_responses import ErrorResponse
from app.schemas.success_responses import SuccessResponse
from app.utils.settings import REQ_COLLECTION_NAME
from app.services.db_service import get_db
from app.services.update_request import update_request_obj
from app.utils.messages import (
    REPORT_ADDED_SUCCESSFULLY,
    FAILED_TO_UPLOAD_DATA_IMPORT_REPORT,
    DATA_ALREADY_EXISTS_FOR_IMPORT_TYPE,
    INVALID_IMPORT_TYPE,
    DATA_IMPORT_REQUEST_REPORT_EXCEPTION_GENERIC,
)

router = APIRouter()

@router.post("/addRequestReport", response_model=SuccessResponse)
async def create_data_import_request_report(payload: RequestReport, db=Depends(get_db)):
    try:
        # Convert ImportRequestID to ObjectId if valid
        import_request_id = (
            ObjectId(payload.ImportRequestID)
            if payload.ImportRequestID and ObjectId.is_valid(str(payload.ImportRequestID))
            else payload.ImportRequestID
        )

        # Check if the request exists
        data_import_request = await db[REQ_COLLECTION_NAME].find_one({"_id": import_request_id})
        if not data_import_request:
            return ErrorResponse(
                message=FAILED_TO_UPLOAD_DATA_IMPORT_REPORT,
                status_code=404
            )

        # Extract ImportTypeId
        import_type_id = payload.ImportTypeId

        # Check if the same ImportTypeId already exists in RequestReport
        existing_request_report = await db[REQ_COLLECTION_NAME].find_one(
            {
                "_id": import_request_id,
                "RequestReport": {
                    "$elemMatch": {"ImportTypeId": import_type_id}
                }
            }
        )
        if existing_request_report:
            return ErrorResponse(
                message=DATA_ALREADY_EXISTS_FOR_IMPORT_TYPE,
                status_code=400
            )

        # Validate import type range
        if isinstance(import_type_id, int) and import_type_id > 4:
            return ErrorResponse(
                message=INVALID_IMPORT_TYPE,
                status_code=400
            )

        # Extract payload data, excluding ImportRequestID
        payload_data = payload.dict(exclude={"ImportRequestID"})

        # Extract ChemIndexObj if available
        chem_index_data = payload.ChemIndexObj.dict() if payload.ChemIndexObj else {}

        # Process and update request object
        updated_status_obj = await update_request_obj(payload_data, db, {
            "collection_data": [
                {
                    "name": "ImportType",
                    "obj_key": "ImportTypeId",
                    "mapping_key": ["ImportTypeId", "ImportTypeName"]
                }
            ]
        })

        # Include ChemIndexObj into the final object
        updated_status_obj["ChemIndexObj"] = chem_index_data

        # Update the document by pushing RequestReport
        result = await db[REQ_COLLECTION_NAME].update_one(
            {"_id": import_request_id},
            {"$push": {"RequestReport": updated_status_obj}}
        )

        if result.modified_count == 0:
            return ErrorResponse(
                message=DATA_IMPORT_REQUEST_REPORT_EXCEPTION_GENERIC.format(error="Failed to update RequestReport"),
                status_code=500
            )

        return SuccessResponse(
            message=REPORT_ADDED_SUCCESSFULLY,
            status_code=200,
            data={"ImportRequestID": str(import_request_id), "Data": updated_status_obj}
        )

    except Exception as e:
        return ErrorResponse(
            message=DATA_IMPORT_REQUEST_REPORT_EXCEPTION_GENERIC.format(error=str(e)),
            status_code=500
        )
