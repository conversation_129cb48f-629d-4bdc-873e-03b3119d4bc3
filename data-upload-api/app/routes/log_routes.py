from fastapi import APIRouter, Depends
from app.schemas.logs_schemas import LogPayload
from app.services.db_service import save_data_to_db, get_db
from app.utils.messages import (
    LOG_ADDED_SUCCESSFULLY,
    FAILED_TO_ADD_LOG,
    LOG_EXCEPTION_GENERIC,
)
from app.services.request_service import get_logs_data_request_by_id
from app.schemas.logs_schemas import LogPayload, LogResponse
from app.schemas.success_responses import SuccessResponse
from app.schemas.error_responses import ErrorResponse
from datetime import datetime

router = APIRouter()

@router.post("/addLogs", response_model=SuccessResponse)
async def add_logs(payload: LogPayload, db=Depends(get_db)):
    """
    Add a new log entry to the database.

    Parameters:
    - payload (LogPayload): The log details to be added.
    - db: The database connection dependency.

    Returns:
    - Success message with the generated report ID if log addition is successful.
    - Error message if log addition fails or an exception occurs.
    """
    try:
        logs_data = {
            "ImportRequestID": payload.ImportRequestID,
            "ComponentName": payload.ComponentName,
            "LogType": payload.LogType,
            "Logs": payload.Logs,
            "CreatedDateTime": payload.CreatedDateTime or datetime.utcnow().strftime("%Y-%m-%d %H:%M:%S")
        }
        result = await save_data_to_db("Logs", logs_data, db)
        if result:
            return SuccessResponse(
                message=LOG_ADDED_SUCCESSFULLY.format(report_id=str(result)),
                status_code=200,
                data={"Result": str(result), "Data": logs_data}
            )
        else:
            return ErrorResponse(
                message=FAILED_TO_ADD_LOG.format(component_name=payload.ComponentName),
                status_code=500
            )
    except Exception as e:
        return ErrorResponse(
            message=LOG_EXCEPTION_GENERIC.format(error=str(e)),
            status_code=500
        )
        
@router.get("/get_log_by_id/{request_id}", response_model=SuccessResponse)
async def get_log_by_id(request_id: str):
    try:
        try:
            request_object_id = int(request_id)
        except ValueError:
            return ErrorResponse(
                message="Invalid request_id format. It must be an integer.",
                status_code=400,
                data=[]
            )

        logs_data = await get_logs_data_request_by_id(request_object_id)

        if not logs_data:
            return ErrorResponse(
                message=f"No logs found for request_id {request_id}",
                status_code=404,
                data=[]
            )

        # Ensure Logs is always a list
        formatted_logs = [
            LogResponse(
                ImportRequestID=log.get("ImportRequestID", 0),
                ComponentName=log.get("ComponentName", "Unknown"),
                LogType=log.get("LogType", "General"),
                Logs=[{"message": msg} for msg in log.get("Logs", [])] 
                    if isinstance(log.get("Logs"), list) 
                    else [{"message": log.get("Logs")}] if isinstance(log.get("Logs"), str) 
                    else [{"message": "No logs available"}],  # Ensure it's always a list of dicts
                CreatedDateTime=log.get("CreatedDateTime", datetime.utcnow().isoformat())
                )
            for log in logs_data
        ]

        return SuccessResponse(
            message=f"Logs fetched successfully for request_id {request_id}",
            status_code=200,
            data=formatted_logs
        )

    except Exception as e:
        return ErrorResponse(
            message=str(e),
            status_code=500,
            data=[]
        )
