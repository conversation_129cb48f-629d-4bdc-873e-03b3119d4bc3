from typing import Optional, Dict
from app.services.db_service import get_db
from app.utils.settings import REQ_COLLECTION_NAME, LOG_COLLECTION_NAME
from pymongo import ASCENDING, DESCENDING
from datetime import datetime, timedelta

async def get_data_import_requests(
    search_value: Optional[str] = None,
    status_filter: dict = None,
    skip: int = 0,
    limit: int = 10,
    sort_field: str = "_id",
    sort_direction: str = "asc"
):
    """
    Fetch DataImportRequest data with optional filters, sorting, and pagination.
    """
    db = get_db()
    collection = db[REQ_COLLECTION_NAME]

    # Base query
    query = {}

    # Apply status filter correctly
    if status_filter and "StatusObj.StatusId" in status_filter:
        query["StatusObj.StatusId"] = status_filter["StatusObj.StatusId"]

    # Apply search filter
    if search_value:
        search_conditions = [
            {"InputFileName": {"$regex": search_value, "$options": "i"}},
            {"StatusObj.StatusName": {"$regex": search_value, "$options": "i"}},
            {"RequestGroupID": {"$regex": search_value, "$options": "i"}},
            {"_id": {"$regex": search_value, "$options": "i"}}
        ]

        # Check if search_value is an integer and add it to search conditions
        if search_value.isdigit():
            search_conditions.append({"RequestGroupID": int(search_value)})
            search_conditions.append({"_id": int(search_value)})

        # Ensure existing filters are not overridden
        if query:
            query = {"$and": [query, {"$or": search_conditions}]}
        else:
            query["$or"] = search_conditions

    # Count total records before filtering
    total_records = await collection.count_documents({"StatusObj.StatusId": status_filter["StatusObj.StatusId"]} if status_filter else {})

    # Count records after filtering but before pagination
    total_filtered = await collection.count_documents(query)

    # Apply sorting and pagination
    sort_order = ASCENDING if sort_direction == "asc" else DESCENDING

    # Apply multi-level sorting: First by sort_field, then by _id
    sorting_criteria = [(sort_field, sort_order), ("_id", DESCENDING)]

    results = await collection.find(query).sort(sorting_criteria).skip(skip).limit(limit).to_list(length=limit)

    return results, total_filtered, total_records

async def get_data_import_request_by_id(request_id: int):
    """
    Fetch a specific request from MongoDB by its _id.
    """
    db = get_db()
    collection = db[REQ_COLLECTION_NAME]

    # Query the database for the specific request by _id
    request_data = await collection.find_one({"_id": int(request_id)})

    return request_data

async def get_logs_data_request_by_id(request_id: int):
    """
    Fetch a specific request from MongoDB by its _id.
    """
    db = get_db()
    collection = db[LOG_COLLECTION_NAME]

    # Query the database for the specific request by _id
    request_data = await collection.find({"ImportRequestID": request_id}).to_list(None)

    return request_data

async def get_request_counts():
    """
    Get counts of requests by status and today's requests.

    Returns:
        Dict: A dictionary containing counts for total, completed, pending, and today's requests.
    """
    db = get_db()
    collection = db[REQ_COLLECTION_NAME]

    # Get total count of all requests
    total_count = await collection.count_documents({})

    # Get count of completed requests (Status ID 4)
    # Status ID 4 = Done
    completed_count = await collection.count_documents({
        "StatusObj.StatusId": 4
    })

    # Get count of pending requests (Status ID 6)
    # Status ID 6 = Pending
    pending_count = await collection.count_documents({
        "StatusObj.StatusId": 6
    })

    # Get count of today's requests
    today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    tomorrow = today + timedelta(days=1)

    today_count = await collection.count_documents({
        "CreatedDate": {"$gte": today, "$lt": tomorrow}
    })

    return {
        "total": total_count,
        "completed": completed_count,
        "pending": pending_count,
        "today": today_count
    }
