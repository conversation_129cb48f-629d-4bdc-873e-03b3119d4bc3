import logging
import asyncio
from typing import Dict, Optional, Any
from datetime import datetime
from celery import Celery
from app.schemas.task_monitor_schemas import (
    TaskInfo, WorkerInfo, TaskStats, WorkerStats,
    TaskListResponse, WorkerListResponse, TaskDetailResponse,
    MonitoringResponse
)

logger = logging.getLogger(__name__)

class CeleryMonitorService:
    """Service to monitor Celery using direct inspect functionality"""

    def __init__(self, celery_app: Optional[Celery] = None):
        """
        Initialize the monitor service with a Celery app instance

        Args:
            celery_app: Celery application instance. If None, will import from worker.py
        """
        if celery_app is None:
            # Import the celery app from worker.py
            from app.tasks.worker import celery_app as worker_celery_app
            self.celery_app = worker_celery_app
        else:
            self.celery_app = celery_app

        self.inspect = self.celery_app.control.inspect()
        self.timeout = 10.0

    def _parse_active_task(self, task_data: Dict[str, Any], worker_name: str, state: str) -> TaskInfo:
        """Parse active task data from Celery inspect"""
        return TaskInfo(
            uuid=task_data.get('id', ''),
            name=task_data.get('name'),
            state=state,
            worker=worker_name,
            started=task_data.get('time_start'),
            args=task_data.get('args'),
            kwargs=task_data.get('kwargs')
        )

    def _parse_reserved_task(self, task_data: Dict[str, Any], worker_name: str, state: str) -> TaskInfo:
        """Parse reserved task data from Celery inspect"""
        return TaskInfo(
            uuid=task_data.get('id', ''),
            name=task_data.get('name'),
            state=state,
            worker=worker_name,
            args=task_data.get('args'),
            kwargs=task_data.get('kwargs')
        )

    def _parse_scheduled_task(self, task_data: Dict[str, Any], worker_name: str, state: str) -> TaskInfo:
        """Parse scheduled task data from Celery inspect"""
        request_data = task_data.get('request', {})
        return TaskInfo(
            uuid=request_data.get('id', ''),
            name=request_data.get('name'),
            state=state,
            worker=worker_name,
            eta=task_data.get('eta'),
            args=request_data.get('args'),
            kwargs=request_data.get('kwargs')
        )
    
    async def get_tasks(self, limit: int = 100, state: Optional[str] = None) -> TaskListResponse:
        """Get list of tasks using Celery inspect"""
        try:
            tasks = []
            stats = TaskStats()

            # Get active tasks from all workers
            active_tasks = self.inspect.active()
            if active_tasks:
                for worker_name, worker_tasks in active_tasks.items():
                    for task_data in worker_tasks:
                        if len(tasks) >= limit:
                            break

                        task_info = self._parse_active_task(task_data, worker_name, "STARTED")
                        if not state or task_info.state.upper() == state.upper():
                            tasks.append(task_info)
                            stats.total += 1
                            stats.active += 1

            # Get scheduled tasks from all workers
            scheduled_tasks = self.inspect.scheduled()
            if scheduled_tasks:
                for worker_name, worker_tasks in scheduled_tasks.items():
                    for task_data in worker_tasks:
                        if len(tasks) >= limit:
                            break

                        task_info = self._parse_scheduled_task(task_data, worker_name, "PENDING")
                        if not state or task_info.state.upper() == state.upper():
                            tasks.append(task_info)
                            stats.total += 1
                            stats.active += 1

            # Get reserved tasks from all workers
            reserved_tasks = self.inspect.reserved()
            if reserved_tasks:
                for worker_name, worker_tasks in reserved_tasks.items():
                    for task_data in worker_tasks:
                        if len(tasks) >= limit:
                            break

                        task_info = self._parse_reserved_task(task_data, worker_name, "PENDING")
                        if not state or task_info.state.upper() == state.upper():
                            tasks.append(task_info)
                            stats.total += 1
                            stats.active += 1

            # Note: Celery inspect doesn't provide historical completed tasks
            # For completed tasks, we would need to use a result backend or external storage
            logger.info(f"Retrieved {len(tasks)} tasks from Celery inspect")

            return TaskListResponse(
                tasks=tasks,
                total_count=len(tasks),
                stats=stats
            )
        except Exception as e:
            logger.error(f"Error getting tasks from Celery inspect: {str(e)}")
            return TaskListResponse(
                tasks=[],
                total_count=0,
                stats=TaskStats()
            )
    
    async def get_task_detail(self, task_id: str) -> Optional[TaskDetailResponse]:
        """Get detailed information about a specific task"""
        try:
            # First, try to find the task in active tasks
            active_tasks = self.inspect.active()
            if active_tasks:
                for worker_name, worker_tasks in active_tasks.items():
                    for task_data in worker_tasks:
                        if task_data.get('id') == task_id:
                            task_info = self._parse_active_task(task_data, worker_name, "STARTED")
                            return TaskDetailResponse(task=task_info)

            # Try to find in scheduled tasks
            scheduled_tasks = self.inspect.scheduled()
            if scheduled_tasks:
                for worker_name, worker_tasks in scheduled_tasks.items():
                    for task_data in worker_tasks:
                        if task_data.get('request', {}).get('id') == task_id:
                            task_info = self._parse_scheduled_task(task_data, worker_name, "PENDING")
                            return TaskDetailResponse(task=task_info)

            # Try to find in reserved tasks
            reserved_tasks = self.inspect.reserved()
            if reserved_tasks:
                for worker_name, worker_tasks in reserved_tasks.items():
                    for task_data in worker_tasks:
                        if task_data.get('id') == task_id:
                            task_info = self._parse_reserved_task(task_data, worker_name, "PENDING")
                            return TaskDetailResponse(task=task_info)

            # If not found in active/scheduled/reserved, try to get result from backend
            try:
                from celery.result import AsyncResult
                result = AsyncResult(task_id, app=self.celery_app)

                task_info = TaskInfo(
                    uuid=task_id,
                    name=result.name if hasattr(result, 'name') else None,
                    state=result.state or "UNKNOWN",
                    result=str(result.result) if result.result else None,
                    exception=str(result.traceback) if result.traceback else None,
                    traceback=str(result.traceback) if result.traceback else None
                )

                return TaskDetailResponse(task=task_info)

            except Exception as result_error:
                logger.warning(f"Could not get result for task {task_id}: {str(result_error)}")

            return None

        except Exception as e:
            logger.error(f"Error getting task detail for {task_id}: {str(e)}")
            return None
    
    async def get_workers(self) -> WorkerListResponse:
        """Get list of workers using Celery inspect"""
        try:
            workers = []
            stats = WorkerStats()

            # Get worker statistics
            worker_stats = self.inspect.stats()
            if not worker_stats:
                return WorkerListResponse(workers=[], stats=stats)

            # Get active tasks to count active tasks per worker
            active_tasks = self.inspect.active() or {}

            for worker_name, worker_stat_data in worker_stats.items():
                try:
                    # Count active tasks for this worker
                    worker_active_count = len(active_tasks.get(worker_name, []))

                    # Extract worker information from stats
                    worker_info = WorkerInfo(
                        hostname=worker_name,
                        pid=worker_stat_data.get('pid'),
                        clock=worker_stat_data.get('clock'),
                        active=worker_active_count,
                        processed=worker_stat_data.get('total', {}).get('tasks.worker.process_excel_task', 0),
                        loadavg=worker_stat_data.get('rusage', {}).get('loadavg'),
                        sw_ident=worker_stat_data.get('sw_ident'),
                        sw_ver=worker_stat_data.get('sw_ver'),
                        sw_sys=worker_stat_data.get('sw_sys')
                    )
                    workers.append(worker_info)

                    # Update stats - if we got stats, the worker is online
                    stats.total += 1
                    stats.online += 1

                except Exception as worker_error:
                    logger.warning(f"Error processing worker {worker_name}: {str(worker_error)}")

            return WorkerListResponse(workers=workers, stats=stats)
        except Exception as e:
            logger.error(f"Error getting workers from Celery inspect: {str(e)}")
            return WorkerListResponse(workers=[], stats=WorkerStats())
    
    async def get_worker_stats(self) -> Dict[str, Any]:
        """Get worker statistics using Celery inspect"""
        try:
            worker_stats = self.inspect.stats()
            if not worker_stats:
                return {"online": 0, "offline": 0, "total": 0}

            # If we can get stats from workers, they are online
            online = len(worker_stats)
            total = online  # We can only count online workers with inspect
            offline = 0  # Can't detect offline workers with inspect alone

            return {
                "online": online,
                "offline": offline,
                "total": total
            }
        except Exception as e:
            logger.error(f"Error getting worker stats from Celery inspect: {str(e)}")
            return {"online": 0, "offline": 0, "total": 0}
    
    async def revoke_task(self, task_id: str, terminate: bool = False) -> MonitoringResponse:
        """Revoke a task using Celery control"""
        try:
            # Use Celery's control interface to revoke the task
            result = self.celery_app.control.revoke(task_id, terminate=terminate)

            return MonitoringResponse(
                success=True,
                data={"task_id": task_id, "terminated": terminate, "result": result},
                message=f"Task {task_id} revoked successfully",
                timestamp=datetime.now()
            )
        except Exception as e:
            logger.error(f"Error revoking task {task_id}: {str(e)}")
            return MonitoringResponse(
                success=False,
                message=f"Error revoking task: {str(e)}",
                timestamp=datetime.now()
            )
    
    async def get_task_result(self, task_id: str) -> MonitoringResponse:
        """Get task result using Celery AsyncResult"""
        try:
            from celery.result import AsyncResult
            result = AsyncResult(task_id, app=self.celery_app)

            if result.state == 'PENDING':
                return MonitoringResponse(
                    success=False,
                    data=None,
                    message="Task result not found or task is still pending",
                    timestamp=datetime.now()
                )

            return MonitoringResponse(
                success=True,
                data={
                    "task_id": task_id,
                    "state": result.state,
                    "result": result.result,
                    "traceback": result.traceback,
                    "successful": result.successful(),
                    "failed": result.failed()
                },
                message="Task result retrieved successfully",
                timestamp=datetime.now()
            )
        except Exception as e:
            logger.error(f"Error getting task result for {task_id}: {str(e)}")
            return MonitoringResponse(
                success=False,
                message=f"Error getting task result: {str(e)}",
                timestamp=datetime.now()
            )

# Global instance - Uses the Celery app from worker.py
celery_monitor = CeleryMonitorService()
