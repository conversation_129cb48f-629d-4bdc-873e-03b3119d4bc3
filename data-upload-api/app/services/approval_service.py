import logging
from typing import Optional, List, Dict, Any
from datetime import datetime, timezone

from app.services.db_service import get_db
from app.utils.rabitmq_client import Rabbit<PERSON>Q<PERSON>lient

from app.utils.settings import REQ_COLLECTION_NAME, LOG_COLLECTION_NAME


logging.getLogger('pika').setLevel(logging.WARNING)

logger = logging.getLogger(__name__)

def publish_to_rabbitmq(request_data):
    try:
        
        client = RabbitMQClient(queue_name="approved_queue")

        publish_data = {
            "import_request_id": request_data.get("_id"),
            "supplier_id": request_data.get("SupplierID"),
            "supplier_name": request_data.get("SupplierName"),
            "request_type_id": request_data.get("RequestTypeObj", {}).get("RequestTypeID"),
            "input_file_name": request_data.get("InputFileName"),
            "file_uuid": request_data.get("FileUUID"),
            "created_date": request_data.get("CreatedDate"),
        }

        logger.info(f"Publishing approved request: {publish_data}")
        client.publish(publish_data)
        client.close()
        return True

    except Exception as e:
        logger.error(f"Failed to publish to RabbitMQ: {str(e)}")
        return False


async def publish_if_approved(request: dict, approved: bool):
    if not approved:
        return False  # If not approved, no need to publish
    try:
        request["_id"] = request["_id"]  # Convert int ID to string for serialization if needed
        is_published = publish_to_rabbitmq(request)
        if is_published:
            logger.info(f"Request {request['_id']} approved and published to RabbitMQ")
        return is_published
    except Exception as e:
        logger.error(f"Failed to publish request {request['_id']} to RabbitMQ: {str(e)}")
        return False


async def update_request_status_service(request_id: int, approved: bool, db):
    """
    Transform a request from Pending status to either Approved or Rejected status.
    Only approve if the request is successfully published to RabbitMQ.
    """
    requests_collection = db[REQ_COLLECTION_NAME]

    # Find the request by ID
    request = await requests_collection.find_one({"_id": request_id})

    if not request:
        return None

    # Get current status from the request
    current_status_obj = request.get("StatusObj", {})
    current_status_name = current_status_obj.get("StatusName")
    current_status_id = current_status_obj.get("StatusId")

    # Verify the request is in Pending status (StatusId = 6)
    if current_status_id != 6 or current_status_name != "Pending":
        # Only Pending requests can be approved or rejected
        return None

    # Define new status and stage based on approval outcome
    new_status_name = "Approved" if approved else "Rejected"  # Capitalized to match your data
    new_status_id = 7 if approved else 8  # 7 for Approved, 8 for Rejected

    updated_time = datetime.now(timezone.utc)

    # Attempt to publish to RabbitMQ
    if approved:
        request["StatusObj"] = {
            "StatusId": new_status_id,
            "StatusName": new_status_name
        }
        request["is_approval"] = approved
        request["UpdatedDate"] = updated_time

        # Only update status in DB if publishing to RabbitMQ was successful
        is_published = await publish_if_approved(request, approved)
        # is_published = True
        if not is_published:
            logger.error(f"Request {request['_id']} could not be published to RabbitMQ. Status not updated.")
            return None  # Do not update status if not successfully published

    # Update the request in the database
    update_result = await requests_collection.update_one(
        {"_id": request["_id"]},
        {"$set": {
            "StatusObj": {
                "StatusId": new_status_id,
                "StatusName": new_status_name
            },
            "is_approval": approved,
            "UpdatedDate": updated_time
        }}
    )

    if update_result.modified_count > 0:
        logger.info(f"Request {request['_id']} status updated to {new_status_name}.")
        return new_status_name

    return None


async def transform_request(req: Dict[str, Any]) -> Dict[str, Any]:
    """Transform MongoDB request document to API response format."""
    req_id = str(req["_id"])
    status = req.get("StatusObj", {}).get("StatusName", "Unknown")
    stage = req.get("StageObj", {}).get("StageName", "Unknown")
    is_approval = req.get("is_approval", status == "Approved")
    input_file_name = req.get("InputFileName", "Unknown")
    request_type = req.get("RequestTypeObj", {}).get("RequestTypeName", "Unknown")
    # We're no longer using sub_stage in the UI

    validation_report = None
    for report in req.get("RequestReport", []):
        if report.get("ImportTypeId", {}).get("ImportTypeName") == "ValidationProcess":
            validation_report = report
            break

    # Extract approval reasons from logs if in approval stage
    approval_reasons = []
    approval_categories = {
        "supplier": [],
        "catalog": [],
        "property": [],
        "warehouse": [],
        "unit": []
    }

    if status == "Pending" and stage == "Approval":
        db = get_db()
        logs_collection = db[LOG_COLLECTION_NAME]

        # Find all approval-related logs with improved regex patterns
        logs_cursor = logs_collection.find({
            "ImportRequestID": int(req_id),
            "LogType": "INFO",
            "$or": [
                # {"Logs": {"$regex": "Approval reason:"}},
                {"Logs": {"$regex": "Approval required for the following reasons:"}},
                # {"Logs": {"$regex": "approval reason"}},
                # {"Logs": {"$regex": "approval required"}},
                # {"Logs": {"$regex": "requires approval"}}
            ]
        }).sort("CreatedDateTime", -1).limit(1)

        logs = await logs_cursor.to_list(length=None)

        # Process all logs to extract reasons with improved pattern matching
        for log in logs:
            log_text = log.get("Logs", "")
            log_text = log_text.strip()

            # Print log for debugging
            print(f"Processing log: {log_text}")

            # Extract individual reasons with case-insensitive matching
            if "Approval reason:" in log_text or "approval reason:" in log_text:
                # Handle both capitalized and lowercase variants
                if "Approval reason:" in log_text:
                    reason = log_text.split("Approval reason:", 1)[1].strip()
                else:
                    reason = log_text.split("approval reason:", 1)[1].strip()

                print(f"Found reason: {reason}")

                # Skip if this is just a single warehouse name that was split incorrectly
                if not (reason.endswith('.') and len(reason.split()) == 1):
                    approval_reasons.append(reason)

                    # Categorize the reason
                    if "New supplier" in reason or "new supplier" in reason or "supplier has no existing data" in reason:
                        approval_categories["supplier"].append(reason)
                    elif "catalog ID match" in reason or "match percentage" in reason:
                        approval_categories["catalog"].append(reason)
                    elif "Unmapped property" in reason or "unmapped property" in reason or "unmapped columns" in reason:
                        approval_categories["property"].append(reason)
                    elif "Invalid warehouse" in reason or "invalid warehouse" in reason:
                        approval_categories["warehouse"].append(reason)
                    elif "Invalid unit" in reason or "invalid unit" in reason or "invalid units" in reason:
                        approval_categories["unit"].append(reason)

            # Extract combined reasons with case-insensitive matching
            elif "Approval required for the following reasons:" in log_text or "approval required for the following reasons:" in log_text:
                # Handle both capitalized and lowercase variants
                if "Approval required for the following reasons:" in log_text:
                    reasons_part = log_text.split("Approval required for the following reasons:", 1)[1].strip()
                else:
                    reasons_part = log_text.split("approval required for the following reasons:", 1)[1].strip()

                print(f"Found combined reasons: {reasons_part}")

                reasons_parts = reasons_part.split(';')  # Normalize to semicolon
                print(f"Normalized reasons: {reasons_parts}")
                for reason in reasons_parts:
                    # Split by semicolon or comma, but be careful not to split warehouse lists
                    # First, check if this is a warehouse list

                    if "Invalid warehouses found:" in reason or "Supplier has no warehouses." in reason:
                        clean_reason = reason.strip()
                        approval_reasons.append(clean_reason)
                        approval_categories["warehouse"].append(clean_reason)
                    else:
                        # Regular processing for other types of reasons
                        if reason.strip():
                            clean_reason = reason.strip()
                            # Skip if this is just a single warehouse name that was split incorrectly
                            if not (clean_reason.endswith('.') and len(clean_reason.split()) == 1):
                                approval_reasons.append(clean_reason)
                                print(f"  - {clean_reason}")

                                # Categorize the reason
                                if "New supplier" in clean_reason or "new supplier" in clean_reason or "supplier has no existing data" in clean_reason:
                                    approval_categories["supplier"].append(clean_reason)
                                elif "catalog ID match" in clean_reason or "match percentage" in clean_reason:
                                    approval_categories["catalog"].append(clean_reason)
                                elif "Unmapped property" in clean_reason or "unmapped property" in clean_reason or "unmapped columns" in clean_reason:
                                    approval_categories["property"].append(clean_reason)
                                elif "Invalid warehouse" in clean_reason or "invalid warehouse" in clean_reason:
                                    approval_categories["warehouse"].append(clean_reason)
                                elif "Invalid unit" in clean_reason or "invalid unit" in clean_reason or "invalid units" in clean_reason:
                                    approval_categories["unit"].append(clean_reason)
                
            # Check for other patterns that might contain approval reasons
            elif "requires approval" in log_text.lower() or "approval required" in log_text.lower():
                # Try to extract the reason from the log text
                print(f"Found requires approval text: {log_text}")

                # Add the entire log as a reason if we can't extract a specific part
                clean_reason = log_text.strip()

                # Skip if this is just a single warehouse name that was split incorrectly
                if not (clean_reason.endswith('.') and len(clean_reason.split()) == 1):
                    approval_reasons.append(clean_reason)

                    # Categorize based on content
                    if "supplier" in clean_reason.lower():
                        approval_categories["supplier"].append(clean_reason)
                    elif "catalog" in clean_reason.lower() or "match percentage" in clean_reason.lower():
                        approval_categories["catalog"].append(clean_reason)
                    elif "property" in clean_reason.lower() or "column" in clean_reason.lower():
                        approval_categories["property"].append(clean_reason)
                    elif "warehouse" in clean_reason.lower():
                        approval_categories["warehouse"].append(clean_reason)
                    elif "unit" in clean_reason.lower():
                        approval_categories["unit"].append(clean_reason)

        # Clean up warehouse reasons - consolidate multiple entries about invalid warehouses
        warehouse_reasons = []
        invalid_warehouses = set()

        # First, extract all invalid warehouse names
        for reason in approval_categories["warehouse"]:
            if "Invalid warehouses found:" in reason:
                # Extract warehouse names after the colon
                warehouse_part = reason.split("Invalid warehouses found:", 1)[1].strip()
                # Split by comma and process each warehouse
                for warehouse in warehouse_part.replace('.', '').split(','):
                    if warehouse.strip():
                        invalid_warehouses.add(warehouse.strip())
            elif "Invalid warehouse" in reason:
                # Extract warehouse name after "Invalid warehouse"
                warehouse_part = reason.split("Invalid warehouse", 1)[1].strip()
                if warehouse_part.startswith(':'):
                    warehouse_part = warehouse_part[1:].strip()
                invalid_warehouses.add(warehouse_part.replace('.', '').strip())
            elif "Supplier has no warehouses." in reason:
                invalid_warehouses.add(reason.replace('.', '').strip())

        # Create a consolidated warehouse reason if we found any invalid warehouses
        if invalid_warehouses:
            consolidated_reason = f"Invalid warehouses found: {', '.join(sorted(invalid_warehouses))}."
            warehouse_reasons.append(consolidated_reason)

        # Replace the warehouse category with our consolidated list
        approval_categories["warehouse"] = warehouse_reasons

        # Remove duplicates from each category
        for category in approval_categories:
            approval_categories[category] = list(set(approval_categories[category]))

        # Rebuild the main approval_reasons list from the categories
        approval_reasons = []
        for category in approval_categories.values():
            approval_reasons.extend(category)

        # Remove duplicates from the main list
        approval_reasons = list(set(approval_reasons))

        # If no reasons were found but we're in approval stage, add a default reason
        if len(approval_reasons) == 0 and status == "Pending" and stage == "Approval":
            default_reason = "Validation complete — general approval required to proceed"
            approval_reasons.append(default_reason)
            approval_categories["supplier"].append(default_reason)

    # Check if this is a multi-scenario case
    is_multi_scenario = sum(1 for category in approval_categories.values() if len(category) > 0) > 1

    return {
        "request_id": req_id,
        "status": status,
        "stage": stage,
        "is_approval": is_approval,
        "input_file_name": input_file_name,
        "request_type": request_type,
        "created_date": req.get("CreatedDate"),
        "updated_date": req.get("UpdatedDate"),
        "validation_report": validation_report,
        "approval_reasons": approval_reasons,
        "approval_categories": approval_categories,
        "is_multi_scenario": is_multi_scenario
    }


async def fetch_requests_service(status_id: int, stage_id: Optional[int] = None, is_approval: Optional[bool] = None) -> List[Dict[str, Any]]:
    """Fetch requests based on status and optional stage/approval filters."""
    db = get_db()
    requests_collection = db[REQ_COLLECTION_NAME]

    query = {"StatusObj.StatusId": status_id}
    if stage_id is not None:
        query["StageObj.StageId"] = stage_id
    if is_approval is not None:
        query["$or"] = [{"is_approval": is_approval}, {"is_approval": {"$exists": False}}]

    requests = await requests_collection.find(query).to_list(length=None)

    # Transform each request asynchronously
    transformed_requests = []
    for req in requests:
        transformed_req = await transform_request(req)
        transformed_requests.append(transformed_req)

    return transformed_requests

async def fetch_approved_requests_service() -> List[Dict[str, Any]]:
    """Fetch all approved requests."""
    return await fetch_requests_service(status_id=7)

async def fetch_pending_requests_service() -> List[Dict[str, Any]]:
    """Fetch all pending requests in approval stage."""
    return await fetch_requests_service(status_id=6, stage_id=7)  #, is_approval=False)

async def fetch_rejected_requests_service() -> List[Dict[str, Any]]:
    """Fetch all rejected requests."""
    return await fetch_requests_service(status_id=8)
