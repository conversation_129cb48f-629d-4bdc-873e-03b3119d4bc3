import json
from typing import List
import httpx
from pydantic import BaseModel

from app.utils.encryption import Encdcr
from app.schemas.supplier_schemas import Supplier
from app.utils.settings import OMS_API_BASE_URL

OMS_API_URL = f"{OMS_API_BASE_URL}/api/SupplierWareHouse/GetAllSuppliersByWarehouse"


async def get_suppliers_from_oms() -> List[Supplier]:
    """Fetch suppliers from OMS, decrypt response, and return as list of Supplier objects."""
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(OMS_API_URL)
        
        if response.status_code != 200:
            return []

        response_json = response.json()
        encrypted_response = response_json.get("responseData")

        if not encrypted_response:
            return []

        decrypted_response = Encdcr.decrypt(encrypted_response)

        try:
            suppliers_data = json.loads(decrypted_response)
            supplier_list = suppliers_data.get("data", [])

            return [
                Supplier(supplier_id=str(supplier["supplierId"]), display_name=supplier["supplierName"])
                for supplier in supplier_list
            ]

        except json.JSONDecodeError:
            return []

    except Exception:
        return []
    

async def get_suppliers_from_db(db) -> List[Supplier]:
    """Fetch suppliers from MongoDB"""
    supplier_collection = db["suppliers"]
    suppliers = await supplier_collection.find({}, {"_id": 0, "supplier_id": 1, "display_name": 1}).to_list(None)
    return [Supplier(**supplier) for supplier in suppliers]


async def get_all_suppliers(db) -> List[Supplier]:
    """Combine suppliers from OMS and MongoDB"""
    
    oms_suppliers = await get_suppliers_from_oms()
    # db_suppliers = await get_suppliers_from_db(db)

    # Combine the lists (you may need to handle duplicate `supplier_id` values)
    # combined_suppliers = {supplier.supplier_id: supplier for supplier in oms_suppliers + db_suppliers}
    combined_suppliers = {supplier.supplier_id: supplier for supplier in oms_suppliers}

    return list(combined_suppliers.values())  # Remove duplicates by using dictionary keys
