async def update_request_obj(data_obj, db, collection_data):
    async def fetch_and_map(collection_name, obj_key, mapping_keys, obj_id):
        collection = db[collection_name]
        item = await collection.find_one({mapping_keys[0]: obj_id})
        return {
            obj_key: {
                key: item.get(key) for key in mapping_keys if key in item
            } if item else None
        }

    result_obj = data_obj

    for collection in collection_data["collection_data"]:
        collection_name = collection["name"]
        obj_key = collection["obj_key"]
        mapping_keys = collection["mapping_key"]
        obj_id = data_obj[obj_key]

        print(f"Fetching and mapping data for {obj_key} from collection: {collection_name}")
        
        mapped_data = await fetch_and_map(
            collection_name, obj_key, mapping_keys, obj_id
        )
        
        result_obj.update(mapped_data)

    return result_obj