from motor.motor_asyncio import AsyncIOMotorClient
from app.utils.settings import DB_NAME

class MongoDB:
    client: AsyncIOMotorClient = None

    @classmethod
    async def connect(cls, uri: str):
        cls.client = AsyncIOMotorClient(uri)

    @classmethod
    async def close(cls):
        if cls.client:
            cls.client.close()

    @classmethod
    def get_database(cls, db_name: str):
        if not cls.client:
            raise RuntimeError("MongoDB client is not initialized.")
        return cls.client[db_name]


async def get_next_sequence_value(db, sequence_name="auto_increment_id", step=1):
    """
    Get the next value for an auto-incrementing ID.
    Increments the counter by `step` and returns the starting value.
    """
    counter_collection = db["counters"]
    counter = await counter_collection.find_one_and_update(
        {"_id": sequence_name},
        {"$inc": {"sequence_value": step}},
        upsert=True,
        return_document=True  # returns the updated document
    )
    return counter["sequence_value"] - step + 1

async def save_data_to_db(collection_name: str, data: dict, db):
    """
    Save data to the specified collection, with an auto-incrementing ID.
    """
    collection = db[collection_name]
    next_id = await get_next_sequence_value(db, collection_name)
    data["_id"] = next_id
    result = await collection.insert_one(data)
    return result.acknowledged

async def bulk_save_data_to_db(collection_name: str, data_list: list, db):
    """
    Save a list of data to the specified collection, with auto-incrementing IDs.
    """
    if not data_list:
        return False
    collection = db[collection_name]
    step = len(data_list)
    next_id = await get_next_sequence_value(db, collection_name, step=step)
    for idx, data in enumerate(data_list):
        data["_id"] = next_id + idx
    result = await collection.insert_many(data_list)
    return result.acknowledged

def get_db():
    return MongoDB.get_database(DB_NAME)
