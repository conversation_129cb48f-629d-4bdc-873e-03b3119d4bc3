import random
import time

used_ids = set()

def generate_unique_4_digit():
    while True:
        # Generate a time-based seed for randomness
        unique_id = int(str(int(time.time() * 1000))[-4:])  # Last 4 digits of timestamp
        unique_id += random.randint(1, 9)  # Small random addition to prevent collisions
        
        # Ensure uniqueness in the current session
        if unique_id not in used_ids and 1000 <= unique_id <= 9999:
            used_ids.add(unique_id)
            return unique_id