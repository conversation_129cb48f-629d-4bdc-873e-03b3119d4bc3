import json
import pika
import time
import logging
from threading import Lock

from app.utils.settings import RABBITMQ_URL

logging.getLogger("pika").setLevel(logging.WARNING)

class RabbitMQClient:
    def __init__(self, queue_name: str):
        self.queue_name = queue_name
        self.connection = None
        self.channel = None
        self.lock = Lock()
        self._connect()

    def _connect(self):
        while True:
            try:
                print(f"Connecting to RabbitMQ at {RABBITMQ_URL}...")
                params = pika.URLParameters(RABBITMQ_URL)
                params.heartbeat = 30000
                params.blocked_connection_timeout = 30000
                params.retry_delay = 5
                params.connection_attempts = 5

                self.connection = pika.BlockingConnection(params)
                self.channel = self.connection.channel()
                self.channel.queue_declare(queue=self.queue_name, durable=True)
                print(f"Connected and declared queue: {self.queue_name}")
                break
            except Exception as e:
                print(f"RabbitMQ connection failed: {e}. Retrying in 5 seconds...")
                time.sleep(5)

    def publish(self, message: dict, ttl_ms: int = None):
        try:
            with self.lock:
                properties = pika.BasicProperties(
                    delivery_mode=2  # Persistent
                )

                self.channel.basic_publish(
                    exchange='',
                    routing_key=self.queue_name,
                    body=json.dumps(message, default=str),
                    properties=properties
                )
                print(f"Published message to {self.queue_name}: {message}")
        except pika.exceptions.AMQPConnectionError:
            print("Lost RabbitMQ connection. Reconnecting and retrying publish...")
            self._connect()
            self.publish(message, ttl_ms)
        except Exception as e:
            print(f"Failed to publish message: {e}")

    def close(self):
        if self.connection and self.connection.is_open:
            self.connection.close()