# Static messages for success, error, and exceptions

# Success messages
SUCCESS_FILE_UPLOAD = "File {filename} uploaded and stored in DB successfully."

# Error messages
ERROR_INVALID_FILE_FORMAT = "Invalid file format. Allowed formats are: {allowed_formats}"
ERROR_FILE_UPLOAD_FAILED = "Failed to upload file."
ERROR_DB_SAVE_FAILED = "Failed to store file in DB."

# Exception messages
EXCEPTION_GENERIC = "An unexpected error occurred: {error}"

# Success messages
SUCCESS_LOG_ADDED = "Log added successfully."

# Error messages
ERROR_LOG_ADD_FAILED = "Failed to add log data to the database."

# Exception messages
EXCEPTION_LOG_GENERIC = "An unexpected error occurred: {error}"


# Static messages for adding output files

# Success messages
SUCCESS_OUTPUT_FILE_ADDED = "Output file added successfully."

# Error messages
ERROR_DATA_IMPORT_REQUEST_NOT_FOUND = "DataImportRequest not found."
ERROR_OUTPUT_FILE_ADD_FAILED = "Failed to add output file data to the database."

# Exception messages
EXCEPTION_OUTPUT_GENERIC = "An unexpected error occurred: {error}"

# Static messages for creating data import request reports

# Success messages
SUCCESS_REPORT_ADDED = "Report added successfully."

# Error messages
ERROR_DATA_IMPORT_REQUEST_NOT_FOUND = "DataImportRequest not found."
ERROR_REPORT_ADD_FAILED = "Failed to add the report to the database."

# Exception messages
EXCEPTION_REQUEST_REPORT_GENERIC = "An unexpected error occurred: {error}"

# Exception messages
EXCEPTION_REPORT_GENERIC = "An unexpected error occurred: {error}"

# Static messages for updating file stage and status

# Success messages
SUCCESS_FILE_UPDATED = "File updated successfully."

# Informational messages
INFO_NO_CHANGES_MADE = "No changes made to the file."

# Error messages
ERROR_FILE_NOT_FOUND = "File not found."

# Exception messages
EXCEPTION_STATUS_GENERIC = "An unexpected error occurred: {error}"

ERROR_OBJ_NOT_FOUND = "Object Not Found"














# File Messages
FILE_CREATED_SUCCESSFULLY_MESSAGE = "The file '{filename}' was uploaded and processed successfully."
ERROR_INVALID_FILE_FORMAT = (
    "The uploaded file format is invalid. Allowed formats are: {allowed_formats}. "
    "Please upload a file with one of the supported extensions."
)
FAILED_TO_STORE_FILE_IN_DB = (
    "The file '{filename}' was successfully processed, but an error occurred while saving it to the database. "
    "Please try again later or contact support."
)
FAILED_TO_UPLOAD_FILE = (
    "The file could not be uploaded to the external service. "
    "Service Response: {service_error}"
)
FILE_EXCEPTION_GENERIC = (
    "An unexpected error occurred while processing the file. Details: {error}. "
    "Please ensure the file is valid and try again. If the issue persists, contact support."
)

# Log Messages
LOG_ADDED_SUCCESSFULLY = "Log entry added successfully. Report ID: {report_id}"
LOG_FETCHED_SUCCESSFULLY = "Log entry fetched successfully. Report ID: {report_id}"
FAILED_TO_ADD_LOG = "Failed to add log data for the component: {component_name}."
LOG_EXCEPTION_GENERIC = "An unexpected error occurred while processing the log entry: {error}"

# Output Messages
DATA_IMPORT_REQUEST_NOT_FOUND = "Data Import Request with the given ID was not found."
OUTPUT_FILE_ADDED = "Output file '{filename}' added successfully to the Data Import Request."
OUTPUT_EXCEPTION_GENERIC = "An unexpected error occurred: {error}. Please try again."

# Remaining Files Messages
FILE_NOT_FOUND = "No remaining files were found with the specified criteria."
REMAINING_FILE_EXCEPTION_GENERIC = (
    "An unexpected error occurred while fetching the remaining files: {error}. Please try again later."
)

# Report Messages
REPORT_ADDED_SUCCESSFULLY = "Report added successfully to the Data Import Request."
FAILED_TO_UPLOAD_DATA_IMPORT_REPORT = "Failed to find the specified Data Import Request."
DATA_ALREADY_EXISTS_FOR_IMPORT_TYPE = "Data already exists for the specified Import Type ID."
INVALID_IMPORT_TYPE = "The specified Import Type ID is invalid. Please provide a valid ID (1-4)."
DATA_IMPORT_REQUEST_REPORT_EXCEPTION_GENERIC = "An unexpected error occurred: {error}. Please try again later."

# Status Messages
STATUS_UPDATE_SUCCESSFUL = "File stage and status updated successfully."
STATUS_NO_CHANGE_DETECTED = "No changes were detected in the file's stage or status."
DATA_NOT_FOUND = "The specified Data Import Request was not found."
STATUS_EXCEPTION_GENERIC = "An unexpected error occurred: {error}. Please try again later."