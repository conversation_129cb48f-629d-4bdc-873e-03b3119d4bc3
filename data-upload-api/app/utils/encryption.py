import logging
from base64 import b64encode, b64decode

from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad

from app.utils.settings import AES_KEY, AES_IV

# Logger setup
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)


class Encdcr:
    @classmethod
    def encrypt(cls, data: str) -> str:
        """Encrypts the given data using AES CBC mode and base64 encodes the result."""
        try:
            cipher = AES.new(AES_KEY, AES.MODE_CBC, AES_IV)
            encrypted_data = cipher.encrypt(pad(data.encode(), AES.block_size))
            return b64encode(encrypted_data).decode("utf-8")
        except Exception as e:
            logger.error(f"Encryption Error: {e}", exc_info=True)
            return None

    @classmethod
    def decrypt(cls, encrypted_text: str) -> str:
        """Decrypts the given base64-encoded AES CBC encrypted text."""
        try:
            encrypted_data = b64decode(encrypted_text)
            cipher = AES.new(AES_KEY, AES.MODE_CBC, AES_IV)
            decrypted_data = unpad(cipher.decrypt(encrypted_data), AES.block_size)
            return decrypted_data.decode()
        except Exception as e:
            logger.error(f"Decryption Error: {e}", exc_info=True)
            return f"Decryption Error: {e}"
