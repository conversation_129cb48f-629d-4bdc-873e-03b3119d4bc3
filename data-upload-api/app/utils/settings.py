import configparser
from base64 import b64decode


# Load Configuration File
config = configparser.ConfigParser()
config.read('config/config.cfg')


# Validation configuration (constant values)
ALLOWED_EXTENSIONS = config['validation']['ALLOWED_EXTENSIONS'].split(',')

# Common database collection names (constant across environments)
REQ_COLLECTION_NAME = config['database.collections']['REQ_COLLECTION_NAME']
LOG_COLLECTION_NAME = config['database.collections']['LOG_COLLECTION_NAME']

# Determine environment (Production or Development)
IS_PRODUCTION = config.getboolean('environment', 'IS_PRODUCTION')
ACTIVE_ENV = "production" if IS_PRODUCTION else "development"

# Environment-Based Configuration
# Database configuration
MONGO_URI = config[f"{ACTIVE_ENV}.database"]["MONGO_URI"]
DB_NAME = config[f"{ACTIVE_ENV}.database"]["MONGO_DBNAME"]

# Bucket UUID
BUCKET_UUID = config[f"{ACTIVE_ENV}.bucket"]['BUCKET_UUID']

# API endpoints
CLOUD_DOC_BASE_URL = config[f"{ACTIVE_ENV}.api"]['CLOUD_DOC_BASE_URL']
OMS_API_BASE_URL = config[f"{ACTIVE_ENV}.api"]['OMS_API_BASE_URL']

# RabbitMQ configuration
RABBITMQ_URL = config[f"{ACTIVE_ENV}.rabbitmq"]['RABBITMQ_URL']

# GRAPH configuration
GRAPH_URL = config[f"{ACTIVE_ENV}.graph"]['GRAPH_BASE_URL']

# AES Encryption Configuration
AES_KEY = b64decode(config['encryption']['AES_KEY'])
AES_IV = b64decode(config['encryption']['AES_IV'])

ROW_LIMIT = config.getint('validation', 'ROW_LIMIT', fallback=20000)

DEFAULT_STATUS_OBJ = 2
DEFAULT_STAGE_OBJ = 1
