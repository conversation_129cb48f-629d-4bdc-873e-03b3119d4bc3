import psutil
import aiofiles
from pathlib import Path
from fastapi import UploadFile

UPLOAD_DIR = Path("uploaded_files")
UPLOAD_DIR.mkdir(parents=True, exist_ok=True)

# Define constants for minimum and maximum chunk sizes in bytes
MIN_CHUNK_SIZE = 1 * 1024 * 1024  # 1 MB
MAX_CHUNK_SIZE = 20 * 1024 * 1024  # 20 MB (max chunk size)

def get_system_memory() -> int:
    """
    Get the available system memory once to avoid redundant checks.
    """
    try:
        # Return the available memory in bytes
        return psutil.virtual_memory().available
    except Exception as e:
        raise Exception(f"Error checking system memory: {str(e)}")

def get_optimal_chunk_size(available_memory: int) -> int:
    """
    Determines the optimal chunk size based on available memory.

    Args:
        available_memory (int): Available memory in bytes.

    Returns:
        int: Optimal chunk size in bytes.
    """
    try:
        # If available memory is less than 1 GB, adjust chunk size
        if available_memory < 1 * 1024 * 1024 * 1024:  # Less than 1 GB
            chunk_size = min(MIN_CHUNK_SIZE, available_memory)
        else:
            # Use 10% of available memory as the chunk size, but within min and max limits
            chunk_size = int(available_memory * 0.1)  # 10% of available memory

        # Ensure the chunk size is between the minimum and maximum limits
        chunk_size = max(MIN_CHUNK_SIZE, chunk_size)
        chunk_size = min(MAX_CHUNK_SIZE, chunk_size)

        return chunk_size

    except Exception as e:
        # In case of error, default to MIN_CHUNK_SIZE (1 MB)
        return MIN_CHUNK_SIZE

def get_dynamic_semaphore_limit(available_memory: int) -> int:
    """
    Calculates the number of concurrent uploads based on available system memory.

    Args:
        available_memory (int): Available memory in bytes.

    Returns:
        int: Semaphore limit based on memory.
    """
    try:
        # Use 10% of available memory for concurrency, capped at 20
        max_concurrency = int(available_memory * 0.1)  # 10% of available memory
        max_concurrency = max(1, min(max_concurrency, 20))  # Limit the concurrency between 1 and 20
        return max_concurrency
    except Exception as e:
        raise Exception(f"Error calculating concurrency: {str(e)}")


async def save_uploaded_file(file: UploadFile, filename: str, available_memory: int) -> str:
    """
    Saves the uploaded file asynchronously using aiofiles.

    Args:
        file (UploadFile): The file to be saved.
        filename (str): The name to save the file as.
        available_memory (int): Available memory in bytes.

    Returns:
        str: The full file path where the file is saved.
    """
    chunk_size = get_optimal_chunk_size(available_memory)  # Get the optimal chunk size dynamically
    file_path = UPLOAD_DIR / filename

    try:
        # Open the file for writing in binary mode using aiofiles
        async with aiofiles.open(file_path, 'wb') as out_file:
            while content := await file.read(chunk_size):
                await out_file.write(content)
        
        return str(file_path)
    
    except Exception as e:
        # Raise exception if there's an issue saving the file
        raise Exception(f"Error saving file: {str(e)}")
