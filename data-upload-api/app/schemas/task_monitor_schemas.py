from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime


class TaskInfo(BaseModel):
    """Information about a Celery task"""
    uuid: str = Field(..., description="Task UUID")
    name: Optional[str] = Field(None, description="Task name")
    state: str = Field(..., description="Task state (PENDING, STARTED, SUCCESS, FAILURE, etc.)")
    received: Optional[float] = Field(None, description="Timestamp when task was received")
    sent: Optional[float] = Field(None, description="Timestamp when task was sent")
    started: Optional[float] = Field(None, description="Timestamp when task was started")
    succeeded: Optional[float] = Field(None, description="Timestamp when task succeeded")
    failed: Optional[float] = Field(None, description="Timestamp when task failed")
    retried: Optional[float] = Field(None, description="Timestamp when task was retried")
    revoked: Optional[float] = Field(None, description="Timestamp when task was revoked")
    rejected: Optional[float] = Field(None, description="Timestamp when task was rejected")
    result: Optional[str] = Field(None, description="Task result")
    exception: Optional[str] = Field(None, description="Task exception")
    traceback: Optional[str] = Field(None, description="Task traceback")
    worker: Optional[str] = Field(None, description="Worker that processed the task")
    args: Optional[List[Any]] = Field(None, description="Task arguments")
    kwargs: Optional[Dict[str, Any]] = Field(None, description="Task keyword arguments")
    eta: Optional[float] = Field(None, description="Estimated time of arrival")
    expires: Optional[float] = Field(None, description="Task expiration time")
    retries: Optional[int] = Field(None, description="Number of retries")
    runtime: Optional[float] = Field(None, description="Task runtime in seconds")


class WorkerInfo(BaseModel):
    """Information about a Celery worker"""
    hostname: str = Field(..., description="Worker hostname")
    pid: Optional[int] = Field(None, description="Worker process ID")
    clock: Optional[int] = Field(None, description="Worker clock")
    active: int = Field(0, description="Number of active tasks")
    processed: int = Field(0, description="Number of processed tasks")
    loadavg: Optional[List[float]] = Field(None, description="System load average")
    sw_ident: Optional[str] = Field(None, description="Software identifier")
    sw_ver: Optional[str] = Field(None, description="Software version")
    sw_sys: Optional[str] = Field(None, description="System information")


class TaskStats(BaseModel):
    """Task statistics"""
    total: int = Field(0, description="Total number of tasks")
    active: int = Field(0, description="Number of active tasks")
    processed: int = Field(0, description="Number of processed tasks")
    failed: int = Field(0, description="Number of failed tasks")
    succeeded: int = Field(0, description="Number of succeeded tasks")
    retried: int = Field(0, description="Number of retried tasks")
    revoked: int = Field(0, description="Number of revoked tasks")


class WorkerStats(BaseModel):
    """Worker statistics"""
    total: int = Field(0, description="Total number of workers")
    online: int = Field(0, description="Number of online workers")
    offline: int = Field(0, description="Number of offline workers")


class TaskListResponse(BaseModel):
    """Response model for task list"""
    tasks: List[TaskInfo] = Field(..., description="List of tasks")
    total_count: int = Field(..., description="Total number of tasks")
    stats: TaskStats = Field(..., description="Task statistics")


class WorkerListResponse(BaseModel):
    """Response model for worker list"""
    workers: List[WorkerInfo] = Field(..., description="List of workers")
    stats: WorkerStats = Field(..., description="Worker statistics")


class TaskDetailResponse(BaseModel):
    """Response model for task detail"""
    task: TaskInfo = Field(..., description="Task information")


class MonitoringResponse(BaseModel):
    """Generic monitoring response"""
    success: bool = Field(..., description="Whether the operation was successful")
    data: Optional[Dict[str, Any]] = Field(None, description="Response data")
    message: str = Field(..., description="Response message")
    timestamp: datetime = Field(..., description="Response timestamp")


class TaskActionRequest(BaseModel):
    """Request model for task actions"""
    action: str = Field(..., description="Action to perform (revoke, terminate)")
    terminate: bool = Field(False, description="Whether to terminate the task")


class TaskFilterRequest(BaseModel):
    """Request model for filtering tasks"""
    state: Optional[str] = Field(None, description="Filter by task state")
    worker: Optional[str] = Field(None, description="Filter by worker")
    limit: int = Field(100, description="Maximum number of tasks to return")
    offset: int = Field(0, description="Number of tasks to skip")
