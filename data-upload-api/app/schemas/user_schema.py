from pydantic import BaseModel
from datetime import datetime

class TokenData(BaseModel):
    access_token: str
    expires_at: datetime
    token_type: str

class UserData(BaseModel):
    user_id: int
    user_name: str
    full_name: str
    is_active: bool
    is_authenticated: bool
    token: TokenData

class UserLoginResponse(BaseModel):
    message: str
    status_code: int
    data: UserData
    
class UserLogOutResponse(BaseModel):
    msg: str
