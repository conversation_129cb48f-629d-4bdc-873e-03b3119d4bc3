from pydantic import BaseModel
from typing import Optional, Dict, Any, List
from datetime import datetime


class ApprovalRequest(BaseModel):
    approved: bool

class RequestResponse(BaseModel):
    request_id: int
    status: str

class ValidationReport(BaseModel):
    ImportTypeId: Optional[Dict[str, Any]] = None
    InputDataCount: Optional[int] = None
    NewDataCount: Optional[int] = None
    ExistsDataCount: Optional[int] = None
    ErrorDataCount: Optional[int] = None
    UpdateDataCount: Optional[int] = None
    DuplicateDataCount: Optional[int] = None

class PendingRequestResponse(BaseModel):
    request_id: int
    status: str
    stage: str
    is_approval: bool
    input_file_name: Optional[str] = None
    request_type: Optional[str] = None
    sub_stage: Optional[str] = None
    created_date: Optional[datetime] = None
    updated_date: Optional[datetime] = None
    validation_report: Optional[Dict[str, Any]] = None
    approval_reasons: Optional[List[str]] = None
