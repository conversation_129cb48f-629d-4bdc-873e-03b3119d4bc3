from pydantic import BaseModel, Field
from typing import List, Optional
from datetime import datetime

# Pydantic model for Request
class RequestType(BaseModel):
    RequestTypeID: int
    RequestTypeName: str

class ImportType(BaseModel):
    ImportTypeId: int
    ImportTypeName: str

class OutputFileType(BaseModel):
    OutputFileTypeId: int
    OutputFileType: str

class RequestReport(BaseModel):
    ImportTypeId: ImportType
    InputDataCount: int
    NewDataCount: int
    ExistsDataCount: int
    ErrorDataCount: int
    UpdateDataCount: int
    DuplicateDataCount: int

class OutputFile(BaseModel):
    FileName: str
    OutputFileTypeId: OutputFileType
    ImportTypeId: ImportType
    FileUUID: str

class StatusObj(BaseModel):
    StatusId: int
    StatusName: str

class StageObj(BaseModel):
    StageId: int
    StageName: str

class Request(BaseModel):
    request_group_id: Optional[int] = Field(None)
    requestTypeId: str
    InputFileName: str
    StatusObj: StatusObj
    StageObj: StageObj
    RequestTypeObj: RequestType
    CreatedDate: datetime

    # Use the Pydantic validator to ensure correct conversion of datetime fields
    @classmethod
    def validate_created_date(cls, value):
        if isinstance(value, datetime):
            return value.isoformat()  # Convert datetime to ISO format string
        return value

    class Config:
        # Ensure datetime is parsed correctly from JSON (e.g., from string to datetime)
        json_encoders = {
            datetime: lambda v: v.isoformat()  # Converts datetime to ISO format when serializing
        }

# Response model that wraps a list of requests
class RequestListResponse(BaseModel):
    data: List[Request]
    recordsTotal: int
    recordsFiltered: int
