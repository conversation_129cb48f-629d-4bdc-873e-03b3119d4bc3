from typing import Optional
from pydantic import BaseModel

class ChemIndexRequestReport(BaseModel):
    ChemIndexInputDataCount: Optional[int] = None
    ChemIndexNewDataCount: Optional[int] = None
    ChemIndexErrorDataCount: Optional[int] = None
    ChemIndexAssociationDataCount: Optional[int] = None
    ChemIndexExistDataCount: Optional[int] = None

    
class RequestReport(BaseModel):
    ImportRequestID: Optional[int] = None
    ImportTypeId: Optional[int] = None
    InputDataCount: Optional[int] = None
    NewDataCount: Optional[int] = None
    ExistsDataCount: Optional[int] = None
    ErrorDataCount: Optional[int] = None
    UpdateDataCount: Optional[int] = None
    DuplicateDataCount: Optional[int] = None
    ChemIndexObj: Optional[ChemIndexRequestReport] = None
    
