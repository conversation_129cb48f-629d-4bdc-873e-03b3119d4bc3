from pydantic import BaseModel
from datetime import datetime
from bson import ObjectId
from .request_report_schemas import RequestReport

class StatusObj(BaseModel):
    StatusId: int
    StatusName: str
    

class StageObj(BaseModel):
    StageId: int
    StageName: str


class RequestTypeObj(BaseModel):
    RequestTypeID: int
    RequestTypeName: str


class DataImportRequest(BaseModel):
    ImportRequestID: int
    InputFileName: str
    StatusObj: StatusObj
    StageObj: StageObj
    RequestTypeObj: RequestTypeObj
    CreatedDate: datetime
    UpdatedDate: datetime
    FileUUID: str
    RequestReport: RequestReport

    class Config:
        json_encoders = {ObjectId: str}