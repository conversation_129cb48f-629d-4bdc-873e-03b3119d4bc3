from pydantic import BaseModel
from typing import Optional

class ProcessRequestTime(BaseModel):
    ProductProcess: Optional[str] = None
    PriceProcess: Optional[str] = None
    StockProcess: Optional[str] = None
    UploadProcess: Optional[str] = None

class UpdateStageStatusRequest(BaseModel):
    ImportRequestID: int
    StageId: int
    StatusId: int
    RequestTime: Optional[str] = None
    ProcessRequestTime: Optional[dict] = None  # Allow it to accept a dictionary
    SubStageId: Optional[int] = None
