# data-upload-api/app/tasks/worker.py

import os
import logging
import asyncio
from celery import Celery
from celery.signals import worker_process_init, worker_process_shutdown
from app.services.db_service import MongoDB, get_db
from app.services.file_process_service import process_file
from app.utils.settings import RABBITMQ_URL, MONGO_URI, ROW_LIMIT

# Set up logging
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)

# Celery configuration
celery_app = Celery(
    'excel_processor',
    broker=RABBITMQ_URL,
    backend='rpc://'
)

# Celery configuration settings
celery_app.conf.update(
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
    task_track_started=True,
    task_time_limit=3600,
    task_soft_time_limit=3000,
    worker_concurrency=1,
    task_default_queue='excel_queue',
    task_acks_late=True,  # This ensures tasks won't be acknowledged until they're fully processed
    task_reject_on_worker_lost=True,  # Ensures tasks are re-queued if the worker is lost
)

@worker_process_init.connect
def init_worker(**kwargs):
    """Initialize MongoDB connection when the worker process starts."""
    try:
        logger.info(f"Initializing worker with ROW_LIMIT: {ROW_LIMIT}")
        loop = asyncio.get_event_loop()
        if loop.is_closed():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        loop.run_until_complete(MongoDB.connect(MONGO_URI))
        logger.info("MongoDB connection initialized for worker process.")
    except Exception as e:
        logger.error(f"Failed to initialize MongoDB connection: {str(e)}")
        raise

@worker_process_shutdown.connect
def shutdown_worker(**kwargs):
    """Close MongoDB connection when the worker process shuts down."""
    try:
        loop = asyncio.get_event_loop()
        if loop.is_closed():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        loop.run_until_complete(MongoDB.close())
        logger.info("MongoDB connection closed for worker process.")
    except Exception as e:
        logger.warning(f"Failed to close MongoDB connection: {str(e)}")

@celery_app.task
def process_excel_task(
    file_path, supplier_id: str, supplier_name: str, request_type_id: int, created_date: str
):
    """
    Processes an Excel file asynchronously in the background.

    Args:
        file_path (str): Path to the Excel file.
        supplier_id (str): The ID of the supplier.
        supplier_name (str): The name of the supplier.
        request_type_id (int): The request type ID.
        created_date (str): The creation date in string format.

    Returns:
        dict: A dictionary containing the status and message of the processing result.
    """
    try:
        logger.info(f"Processing file: {file_path}")
        logger.info(f"CreatedDate: {created_date}")
        logger.info(f"RequestTypeId: {request_type_id}")
        logger.info(f"SupplierID: {supplier_id}")
        logger.info(f"SupplierName: {supplier_name}")

        # Get the database client
        db = get_db()

        # Run the async process_file function
        loop = asyncio.get_event_loop()
        if loop.is_closed():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

        result = loop.run_until_complete(
            process_file(
                file_path,
                created_date=created_date,
                request_type_id=request_type_id,
                supplier_id=supplier_id,
                supplier_name=supplier_name,
                db=db
            )
        )

        # Clean up the file only if processing was successful
        if result:
            try:
                os.remove(file_path)
                logger.info(f"Deleted file: {file_path}")
            except OSError as e:
                logger.warning(f"Failed to delete file {file_path}: {str(e)}")

        logger.info(f"Successfully processed file: {file_path}")
        return {
            "status": "success",
            "message": f"Processed file: {file_path}",
            "data": result
        }

    except Exception as e:
        logger.error(f"Error processing file {file_path}: {str(e)}")
        return {
            "status": "error",
            "message": f"Error processing file: {str(e)}"
        }
