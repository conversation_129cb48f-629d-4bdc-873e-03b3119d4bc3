{% extends "base.html" %}

{% block title %}Data Import{% endblock %}
{% block extra_head %}
<style>
    /* Modern Data Import Page Styles */
    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-lg);
        padding-bottom: var(--spacing-md);
        border-bottom: 1px solid var(--gray-200);
    }

    .page-header h2 {
        margin: 0;
        color: var(--gray-900);
        font-size: 1.75rem;
        display: flex;
        align-items: center;
    }

    .page-header h2 i {
        margin-right: var(--spacing-sm);
        color: var(--primary);
    }

    .page-subtitle {
        color: var(--gray-600);
        font-size: 1rem;
        margin-top: var(--spacing-xs);
    }

    /* Center the main container */
    .main-index {
        display: flex;
        flex-direction: column;
        max-width: 800px;
        margin: 0 auto;
    }

    /* Enhanced Card for File Upload and Request Type */
    .card {
        background: white;
        border-radius: var(--border-radius-lg);
        box-shadow: var(--shadow);
        padding: var(--spacing-xl);
        margin-bottom: var(--spacing-xl);
        transition: all var(--transition);
        border: 1px solid var(--gray-200);
        position: relative;
        overflow: hidden;
    }

    .card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: linear-gradient(to bottom, var(--primary), var(--secondary));
        opacity: 0.8;
    }

    .card:hover {
        box-shadow: var(--shadow-md);
        transform: translateY(-2px);
    }

    /* Form Section Styles */
    .form-section {
        /* margin-bottom: var(--spacing-lg); */
        transition: all var(--transition);
        /* padding: var(--spacing-sm) 0; */
        position: relative;
    }

    .form-section.hidden {
        opacity: 0;
        height: 0;
        margin: 0;
        overflow: hidden;
        transform: translateY(10px);
    }

    .form-section.visible {
        opacity: 1;
        animation: fade-in 0.5s ease-out;
    }

    @keyframes fade-in {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    /* Form Labels */
    .card label {
        font-weight: var(--font-weight-bold);
        display: block;
        margin-bottom: var(--spacing-sm);
        color: var(--gray-700);
        font-size: 1.05rem;
        transition: all var(--transition-fast);
    }

    .required::after {
        content: " *";
        color: var(--danger);
        font-size: 1.2rem;
    }

    /* Form Controls */
    .card select,
    .card input[type="date"] {
        width: 100%;
        padding: var(--spacing-md) var(--spacing-lg);
        font-size: 1rem;
        border-radius: var(--border-radius);
        border: 1px solid var(--gray-300);
        margin-bottom: var(--spacing-md);
        transition: all var(--transition-fast);
        background-color: white;
        box-shadow: var(--shadow-sm);
        color: var(--gray-800);
        height: 100%;
    }

    .card select:hover,
    .card input[type="date"]:hover {
        border-color: var(--primary-light);
    }

    .card select:focus,
    .card input[type="date"]:focus {
        border-color: var(--primary);
        box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
        outline: none;
    }

    .card select:disabled {
        background-color: var(--gray-100);
        cursor: not-allowed;
        opacity: 0.7;
    }

    /* Form section with icon */
    .form-section-header {
        display: flex;
        align-items: center;
        margin-bottom: var(--spacing-xs);
    }

    .form-section-icon {
        margin-right: var(--spacing-sm);
        color: var(--primary);
        font-size: 1.2rem;
    }

    .form-section-help {
        font-size: 0.9rem;
        color: var(--gray-600);
        margin-top: 0;
        margin-bottom: var(--spacing-sm);
    }

    /* Enhanced Upload Button */
    #uploadBtn {
        padding: var(--spacing-md) var(--spacing-lg);
        background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
        color: white;
        border: none;
        border-radius: var(--border-radius);
        cursor: pointer;
        width: 100%;
        font-size: 1.1rem;
        font-weight: var(--font-weight-bold);
        transition: all var(--transition);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-md);
        height: 54px;
        box-shadow: 0 4px 6px rgba(67, 97, 238, 0.2);
        position: relative;
        overflow: hidden;
        margin-top: var(--spacing-md);
    }

    #uploadBtn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: all 0.6s ease;
    }

    #uploadBtn:hover:not(:disabled)::before {
        left: 100%;
    }

    #uploadBtn:hover:not(:disabled) {
        background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary) 100%);
        transform: translateY(-3px);
        box-shadow: 0 6px 12px rgba(67, 97, 238, 0.25);
    }

    #uploadBtn:active:not(:disabled) {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(67, 97, 238, 0.2);
    }

    #uploadBtn:disabled {
        background: linear-gradient(135deg, var(--gray-400) 0%, var(--gray-500) 100%);
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
        opacity: 0.7;
    }

    #uploadBtn i {
        font-size: 1.3rem;
    }

    /* Enhanced Response Message */
    #message {
        margin-top: var(--spacing-md);
        font-weight: var(--font-weight-bold);
        color: var(--danger);
        text-align: center;
        min-height: 24px;
        padding: var(--spacing-sm);
        border-radius: var(--border-radius);
        transition: all var(--transition-fast);
        display: none;
        opacity: 0;
        transform: translateY(-10px);
        animation: none;
    }

    #message.show-error {
        display: block;
        background-color: rgba(244, 67, 54, 0.05);
        border-left: 3px solid var(--danger);
        opacity: 1;
        transform: translateY(0);
        animation: fade-in 0.3s ease-out;
    }

    /* Enhanced Loader */
    #loader {
        margin-top: var(--spacing-md);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-md);
        color: var(--primary);
        padding: var(--spacing-md);
        background-color: rgba(67, 97, 238, 0.05);
        border-radius: var(--border-radius);
        font-weight: var(--font-weight-bold);
    }

    #loader .spinner {
        width: 28px;
        height: 28px;
        border: 3px solid rgba(67, 97, 238, 0.2);
        border-top-color: var(--primary);
        border-radius: 50%;
        animation: spin 1s linear infinite;
        box-shadow: 0 0 10px rgba(67, 97, 238, 0.1);
    }

    @keyframes spin {
        to { transform: rotate(360deg); }
    }

    /* Enhanced Modern Drag & Drop Area */
    .drag-drop-area {
        border: 2px dashed var(--gray-300);
        text-align: center;
        font-size: 1.1rem;
        color: var(--gray-600);
        background-color: rgba(248, 249, 250, 0.7);
        cursor: pointer;
        border-radius: var(--border-radius-lg);
        margin-bottom: var(--spacing-md);
        width: 100%;
        min-height: 180px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-md);
        transition: all var(--transition);
        position: relative;
        overflow: hidden;
        box-shadow: var(--shadow-sm);
    }

    .drag-drop-area::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(67, 97, 238, 0.03) 0%, rgba(76, 201, 240, 0.03) 100%);
        opacity: 0;
        transition: opacity var(--transition);
        z-index: 0;
    }

    .drag-drop-area > * {
        position: relative;
        z-index: 1;
    }

    .drag-drop-area i {
        font-size: 3rem;
        color: var(--gray-400);
        transition: all var(--transition);
        margin-bottom: var(--spacing-sm);
    }

    .drag-drop-area .file-types {
        font-size: 0.85rem;
        color: var(--gray-500);
        margin-top: var(--spacing-sm);
        background-color: var(--gray-100);
        padding: var(--spacing-xs) var(--spacing-md);
        border-radius: var(--border-radius);
        display: inline-block;
    }

    .drag-drop-area:hover {
        border-color: var(--primary);
        transform: translateY(-2px);
    }

    .drag-drop-area:hover::before {
        opacity: 1;
    }

    .drag-drop-area:hover i {
        color: var(--primary);
        transform: translateY(-5px) scale(1.1);
    }

    .drag-drop-area.dragover {
        background-color: rgba(67, 97, 238, 0.05);
        border-color: var(--primary);
        box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15), var(--shadow);
        transform: translateY(-4px);
    }

    .drag-drop-area.has-file {
        border-color: var(--success);
        background-color: rgba(76, 175, 80, 0.05);
        box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.15), var(--shadow-sm);
    }

    .drag-drop-area.has-file i {
        color: var(--success);
    }

    .drag-drop-area .file-size {
        font-size: 0.9rem;
        color: var(--gray-600);
        margin-top: var(--spacing-xs);
        background-color: rgba(76, 175, 80, 0.1);
        padding: var(--spacing-xs) var(--spacing-md);
        border-radius: var(--border-radius);
        display: inline-block;
    }

    /* Ultra Modern Success Popup with Neumorphic Design */
    .popup {
        position: fixed;
        top: 20px;
        right: 20px;
        color: var(--gray-800);
        padding: 24px;
        border-radius: 20px;
        z-index: 9999;
        box-shadow:
            0 10px 30px rgba(0, 0, 0, 0.08),
            0 5px 15px rgba(0, 0, 0, 0.03),
            inset 0 -2px 0 rgba(0, 0, 0, 0.05);
        width: 460px;
        font-family: var(--font-family);
        transform: translateX(400px) translateY(-10px);
        transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
        background: #ffffff;
        border: 1px solid rgba(255, 255, 255, 0.8);
        overflow: hidden;
    }

    .popup::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 8px;
        height: 100%;
        background: linear-gradient(to bottom, #4CAF50, #2E7D32);
        border-top-left-radius: 20px;
        border-bottom-left-radius: 20px;
    }

    .popup::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0) 50%);
        pointer-events: none;
        z-index: -1;
    }

    .popup.show {
        transform: translateX(0) translateY(0);
        animation: popup-appear 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
    }

    @keyframes popup-appear {
        0% { transform: translateX(400px) translateY(-10px); opacity: 0; }
        60% { transform: translateX(-15px) translateY(5px); opacity: 1; }
        80% { transform: translateX(5px) translateY(-2px); }
        100% { transform: translateX(0) translateY(0); }
    }

    .popup.hidden {
        display: none;
    }

    .popup .popup-header {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
        gap: 16px;
        color: #2E7D32;
        font-weight: 600;
        font-size: 1.25rem;
    }

    .popup .popup-header i {
        font-size: 1.5rem;
        background: linear-gradient(135deg, #4CAF50, #2E7D32);
        color: white;
        width: 48px;
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        box-shadow:
            0 6px 12px rgba(76, 175, 80, 0.2),
            0 2px 4px rgba(76, 175, 80, 0.1);
    }

    .popup .popup-message {
        display: block;
        margin-bottom: 16px;
        line-height: 1.6;
        color: #424242;
        font-size: 1.05rem;
        padding: 4px 0;
        border-radius: 4px;
    }

    .popup .popup-close {
        background: rgba(255, 255, 255, 0.9);
        border: none;
        font-size: 1rem;
        color: #757575;
        position: absolute;
        top: 12px;
        right: 12px;
        cursor: pointer;
        transition: all 0.2s ease;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        box-shadow:
            0 2px 5px rgba(0, 0, 0, 0.05),
            0 1px 2px rgba(0, 0, 0, 0.1);
    }

    .popup .popup-close:hover {
        color: #E53935;
        background-color: rgba(229, 57, 53, 0.1);
        transform: scale(1.1) rotate(90deg);
    }

    /* Progress bar inside popup */
    .popup-progress {
        width: 100%;
        height: 6px;
        background-color: #f5f5f5;
        border-radius: 100px;
        overflow: hidden;
        margin-top: 16px;
        box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
        position: relative;
    }

    .popup-progress-bar {
        width: 100%;
        height: 100%;
        background: linear-gradient(to right, #4CAF50, #2E7D32);
        animation: progressBar 3s cubic-bezier(0.1, 0.9, 0.2, 1) forwards;
        border-radius: 100px;
        position: relative;
        overflow: hidden;
    }

    .popup-progress-bar::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
            90deg,
            rgba(255, 255, 255, 0) 0%,
            rgba(255, 255, 255, 0.4) 50%,
            rgba(255, 255, 255, 0) 100%
        );
        animation: shimmer 1.5s infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
    }

    @keyframes progressBar {
        from { width: 100%; }
        to { width: 0%; }
    }

    /* Form Header */
    .form-header {
        margin-bottom: var(--spacing-md);
    }

    .form-header h3 {
        font-size: 1.3rem;
        color: var(--gray-800);
        margin: 0;
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
    }

    .form-header h3 i {
        color: var(--primary);
    }

    /* Enhanced Form Steps Indicator */
    .form-steps {
        display: flex;
        justify-content: space-between;
        position: relative;
        padding: var(--spacing-md) 0 var(--spacing-lg);
        margin-bottom: var(--spacing-lg);
        border-bottom: 1px solid var(--gray-200);
    }

    .form-steps::before {
        content: '';
        position: absolute;
        top: 36px;
        left: 12%;
        right: 12%;
        height: 2px;
        background-color: var(--gray-200);
        z-index: 1;
    }

    .step {
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        z-index: 2;
        width: 25%;
    }

    .step-number {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background-color: var(--gray-200);
        color: var(--gray-600);
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: var(--font-weight-bold);
        margin-bottom: var(--spacing-sm);
        transition: all var(--transition);
        box-shadow: var(--shadow-sm);
        border: 2px solid white;
        font-size: 0.9rem;
    }

    .step-label {
        font-size: 0.85rem;
        color: var(--gray-600);
        transition: all var(--transition);
        text-align: center;
        font-weight: var(--font-weight-normal);
    }

    .step.active .step-number {
        background-color: var(--primary);
        color: white;
        box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
    }

    .step.active .step-label {
        color: var(--primary);
        font-weight: var(--font-weight-bold);
    }

    .step.completed .step-number {
        background-color: var(--success);
        color: white;
        box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.15);
    }

    .step.completed .step-label {
        color: var(--success);
        font-weight: var(--font-weight-bold);
    }

    .step.completed .step-number::after {
        content: '\f00c';
        font-family: 'Font Awesome 6 Free';
        font-weight: 900;
    }
    .request-container {
        background: white;
        border-radius: var(--border-radius-lg);
        box-shadow: var(--shadow);
        padding: var(--spacing-xl);
        margin-bottom: var(--spacing-xl);
        transition: box-shadow var(--transition);
    }

    .request-container:hover {
        box-shadow: var(--shadow-md);
    }
</style>
{% endblock %}

{% block content %}
<div class="request-container">
    <div class="main-index">
        <!-- Page Header -->
        <div class="page-header">
            <div>
                <h2><i class="fas fa-file-upload"></i> Graph Generated</h2>
            </div>
        </div>

        <!-- File Upload Form Header -->
        <div class="form-header">
            <h3><i class="fas fa-cloud-upload-alt"></i> Show Price and Stock history in graph</h3>
        </div>

        <div class="card">
            <!-- Form Steps Indicator -->
            <div class="form-steps">
                <div class="step active" id="step1">
                    <div class="step-number">1</div>
                    <div class="step-label">Supplier</div>
                </div>
                <div class="step" id="step2">
                    <div class="step-number">2</div>
                    <div class="step-label">Catalog Number</div>
                </div>
            </div>

            <!-- Supplier Selection -->
            <div class="form-section supplier-box visible">
                <div class="form-section-header">
                    <i class="fas fa-building form-section-icon"></i>
                    <label for="supplierId" class="required">Select Supplier</label>
                </div>
                <p class="form-section-help">Choose the supplier for this data import</p>
                <select id="supplierId">
                    <option value="">-- Loading Suppliers... --</option>
                </select>
            </div>

            <!-- Catalog Number Section -->
            <div class="form-section request-box hidden">
                <div class="form-section-header">
                    <i class="fas fa-barcode form-section-icon"></i>
                    <label for="catalogNumber" class="required">Enter Catalog Number</label>
                </div>
                <p class="form-section-help">Enter the catalog number to fetch history</p>
                <input type="text" id="catalogNumber" placeholder="e.g., CAT12345" required />
            </div>

            <!-- Submit Button -->
            <button id="uploadBtn" disabled>
                <i class="fas fa-upload"></i> Submit
            </button>

            <!-- Loader -->
            <div id="loader" style="display: none;">
                <div class="spinner"></div>
                <span>Uploading file, please wait...</span>
            </div>

            <!-- Response Message -->
            <p id="message"></p>
        </div>

        <!-- Success Popup -->
        <div id="successPopup" class="popup hidden">
            <div class="popup-header">
                <i class="fas fa-check-circle"></i>
                <span>Submit Successful</span>
            </div>
            <button class="popup-close"><i class="fas fa-times"></i></button>
            <div class="popup-message"></div>
            <div class="popup-progress">
                <div class="popup-progress-bar"></div>
            </div>
        </div>
    </div>
</div>
<meta name="graph-base-url" content="{{ graph_url }}">

{% block extra_scripts %}
<script>
$(document).ready(function () {
    let supplierMap = {};

    function updateFormSteps(step) {
        $(".step").removeClass("active completed");
        for (let i = 1; i <= step; i++) {
            $(`#step${i}`).addClass(i === step ? "active" : "completed");
        }
    }

    function resetForm() {
        $("#supplierId").val("");
        $("#catalogNumber").val("");
        $("#uploadBtn").prop("disabled", true);
        $(".form-section").removeClass("visible").addClass("hidden");
        $(".supplier-box").removeClass("hidden").addClass("visible");
        updateFormSteps(1);
        $("#message").text("").removeClass("show-error");
        $("#loader").hide();
    }

    function fetchSuppliers() {
        $.ajax({
            url: "/api/suppliers",
            type: "GET",
            success: function (response) {
                let dropdown = $("#supplierId");
                dropdown.empty();
                dropdown.append('<option value="">-- Select Supplier --</option>');
                response.sort((a, b) => a.display_name.localeCompare(b.display_name));
                response.forEach(s => {
                    supplierMap[s.supplier_id] = s.display_name;
                    dropdown.append(`<option value="${s.supplier_id}">${s.display_name}</option>`);
                });
            },
            error: function () {
                $("#supplierId").html('<option value="">-- Failed to Load Suppliers --</option>');
                $("#message").text("Failed to load suppliers. Please refresh the page.").addClass("show-error");
            }
        });
    }

    $("#supplierId").change(function () {
        const selected = $(this).val();
        if (selected) {
            $(".request-box").removeClass("hidden").addClass("visible");
            updateFormSteps(2);
        } else {
            resetForm();
        }
    });

    $("#catalogNumber").on("input", function () {
        const catalogNumber = $(this).val().trim();
        $("#uploadBtn").prop("disabled", !catalogNumber);
    });

    $("#uploadBtn").click(function () {
        const supplierId = $("#supplierId").val();
        const catalogNumber = $("#catalogNumber").val().trim();

        if (!supplierId || !catalogNumber) {
            $("#message").text("Please fill in all required fields.").addClass("show-error");
            return;
        }

        $("#message").text("").removeClass("show-error");
        $("#loader").show();
        $("#uploadBtn").prop("disabled", true);

        const baseUrl = $('meta[name="graph-base-url"]').attr("content");
        const chartUrl = `${baseUrl}/generate_chart?supplier_catalog_id=${encodeURIComponent(catalogNumber)}&supplier_id=${encodeURIComponent(supplierId)}`;

        // Open in new tab
        window.open(chartUrl, "_blank");

        // Optionally still show popup or message
        showPopup("Graph generated successfully.", catalogNumber);
        resetForm();
    });

    function showPopup(message, requestId) {
        const msg = `
            <div style="font-size: 1.05rem; margin-bottom: 8px;">${message}</div>
            <div style="display: flex; align-items: center;">
                <span style="font-size: 0.9rem; margin-right: 8px; color: #616161;">Catalog Number:</span>
                <span style="font-weight: 600; color: #2E7D32; font-size: 0.95rem;">${requestId}</span>
            </div>`;

        $("#successPopup .popup-message").html(msg);
        $("#successPopup").removeClass("hidden").addClass("show");

        setTimeout(() => {
            $("#successPopup").removeClass("show");
            setTimeout(() => {
                $("#successPopup").addClass("hidden");
            }, 400);
        }, 3000);
    }

    $("#successPopup .popup-close").click(function () {
        $("#successPopup").removeClass("show");
        setTimeout(() => {
            $("#successPopup").addClass("hidden");
        }, 400);
    });

    fetchSuppliers();
    resetForm();
});
</script>
{% endblock %}
{% endblock %}
