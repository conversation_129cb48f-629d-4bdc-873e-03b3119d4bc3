<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Data Import{% endblock %}</title>
    <link rel="icon" href="/static/images/logo.png" type="image/png">
    <link rel="stylesheet" href="/static/css/global.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <!-- Flatpickr for modern date picker -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <!-- Common components -->
    <script src="/static/js/components/status-badge.js"></script>
    
    <style>
        /* Loader Styles */
        .loader-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.8);
            z-index: 1000;
            backdrop-filter: blur(3px);
            display: none; /* Hidden by default */
        }

        /* When visible, use flex to center the spinner */
        .loader-overlay.visible {
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .spinner {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            border: 3px solid transparent;
            border-top-color: var(--primary);
            border-bottom-color: var(--primary);
            animation: spin 1.2s ease infinite;
            position: relative;
            margin: 0; /* Reset any margins */
            transform: translateX(0); /* Ensure no horizontal offset */
        }

        .spinner:before, .spinner:after {
            content: '';
            position: absolute;
            border-radius: 50%;
            border: 3px solid transparent;
        }

        .spinner:before {
            top: 5px;
            left: 5px;
            right: 5px;
            bottom: 5px;
            border-top-color: var(--secondary);
            border-bottom-color: var(--secondary);
            animation: spin 1.8s linear infinite;
        }

        .spinner:after {
            top: 15px;
            left: 15px;
            right: 15px;
            bottom: 15px;
            border-top-color: var(--accent);
            border-bottom-color: var(--accent);
            animation: spin 1.5s ease-in-out infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* DataTables Customization */
        .dataTables_wrapper .dataTables_length,
        .dataTables_wrapper .dataTables_filter {
            margin-bottom: var(--spacing-md);
        }

        .dataTables_wrapper .dataTables_length select {
            width: auto;
            padding: var(--spacing-xs) var(--spacing-sm);
            margin: 0 var(--spacing-xs);
        }

        .dataTables_wrapper .dataTables_filter input {
            width: auto;
            margin-left: var(--spacing-sm);
            padding: var(--spacing-xs) var(--spacing-sm);
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button {
            padding: var(--spacing-xs) var(--spacing-sm);
            margin: 0 2px;
            border-radius: var(--border-radius);
            border: 1px solid var(--gray-300);
            background: white;
            color: var(--gray-700) !important;
            cursor: pointer;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button.current {
            background: var(--primary);
            color: white !important;
            border-color: var(--primary);
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
            background: var(--gray-100);
            color: var(--primary) !important;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
            background: var(--primary-dark);
            color: white !important;
        }

        /* Active page indicator */
        .sidebar ul li a.active {
            background: var(--primary);
            color: white;
        }

        /* Add icons to sidebar */
        .nav-icon {
            margin-right: var(--spacing-md);
            width: 20px;
            text-align: center;
        }
    </style>

    {% block extra_head %}{% endblock %}
</head>
<body>

    <!-- Modern Sidebar Navigation -->
    <div class="sidebar">
        <div>
            <h2><i class="fas fa-database nav-icon"></i> Data Portal</h2>
            <ul class="menu">
                <li><a href="/dashboard" class="{{ 'active' if request.path == '/dashboard' }}">
                    <i class="fas fa-tachometer-alt nav-icon"></i>
                    <span>Dashboard</span>
                </a></li>
                <li><a href="/" class="{{ 'active' if request.path == '/' }}">
                    <i class="fas fa-file-upload nav-icon"></i>
                    <span>Data Import</span>
                </a></li>
                <li><a href="/task-monitor" class="{{ 'active' if request.path == '/task-monitor' }}">
                    <i class="fas fa-tasks nav-icon"></i>
                    <span>Task Monitor</span>
                </a></li>
                <li><a href="/view_requests" class="{{ 'active' if request.path == '/view_requests' }}">
                    <i class="fas fa-list-ul nav-icon"></i>
                    <span>Request List</span>
                </a></li>
                <li><a href="/approval" class="{{ 'active' if request.path == '/approval' }}">
                    <i class="fas fa-check-circle nav-icon"></i>
                    <span>Approval Management</span>
                </a></li>
                <li><a href="/graph" class="{{ 'active' if request.path == '/graph' }}">
                    <i class="fas fa-chart-line nav-icon"></i>
                    <span>Echem Graph</span>
                </a></li>
            </ul>
        </div>
        <ul class="logout">
            <li><a href="/logout">
                <i class="fas fa-sign-out-alt nav-icon"></i>
                <span>Log Out</span>
            </a></li>
        </ul>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="loader-overlay">
            <div class="spinner"></div>
        </div>
        {% block content %}
        <!-- Dynamic content will be inserted here -->
        {% endblock %}
    </div>

    {% block extra_scripts %}
    {% endblock %}
<script>
    $(document).ready(function() {
        // Highlight current page in sidebar
        const currentPath = window.location.pathname;
        $(`.sidebar a[href="${currentPath}"]`).addClass('active');

        // Add loading indicator when navigating
        $(".menu a, .logout a").on("click", function() {
            showLoader();
        });
    });

    function showLoader() {
        // First add the visible class to enable flex centering
        $(".loader-overlay").addClass("visible");
        // Then fade in for smooth appearance
        $(".loader-overlay").fadeIn(300);
    }

    function hideLoader() {
        // Fade out first
        $(".loader-overlay").fadeOut(300, function() {
            // Remove visible class after fade out is complete
            $(this).removeClass("visible");
        });
    }
</script>
</body>
</html>
