{% extends "base.html" %}

    {% block title %}Request Details{% endblock %}
    {% block extra_head %}
    <!-- Include shared confirmation dialog script -->
    <script src="/static/js/shared-confirmation-dialog.js"></script>

    {% endblock %}

    {% block content %}
        <!-- Background overlay for when logs panel is open -->
        <div class="overlay" id="overlay"></div>

        <!-- Sliding Logs Panel -->
        <div class="logs-panel" id="logsPanel">
            <div class="logs-panel-header">
                <h3 style="color: white;"><i class="fas fa-history"></i> History</h3>
                <button class="logs-panel-close" id="closeLogs"><i class="fas fa-times"></i></button>
            </div>
            <div class="logs-panel-filter">
                <select id="logFilter" onchange="filterLogs()">
                    <option value="all">All Components</option>
                </select>
            </div>
            <div class="logs-panel-content" id="logContainer">
                <!-- Logs will be dynamically inserted here -->
            </div>
        </div>

        <div class="request-detail-container">
            <!-- Page Header with Log Icon and Back Button -->
            <div class="page-header">
                <h2><i class="fas fa-file-alt"></i> Request Details</h2>
                <div class="header-actions">
                    <button class="btn-tab" onclick="window.history.back()">
                        <i class="fas fa-arrow-left"></i> Back to Requests
                    </button>
                    <button class="log-icon-btn" id="showLogsBtn" title="View Logs">
                        <i class="fas fa-history"></i>
                    </button>
                </div>
            </div>

            <!-- Modern Approval Actions Section - Only shown for Pending requests in Approval stage -->
            {% if request.StatusObj.StatusName == "Pending" and request.StageObj.StageName == "Approval" %}
            <div class="approval-actions-container">
                <div class="approval-status">
                    <div class="approval-status-title">
                        <i class="fas fa-clock"></i> Pending Approval
                    </div>
                    <div class="approval-status-message">
                        This request requires your approval to proceed. Please review the details and take action.
                    </div>

                    <!-- Approval Reasons Section -->
                    <div class="approval-reasons-section" id="approvalReasonsSection">
                        <!-- Approval reasons will be loaded here via JavaScript -->
                    </div>
                </div>
                <div class="approval-buttons">
                    <button id="approveBtn" class="approve-button" onclick="approveRequest()">
                        <i class="fas fa-check-circle"></i> Approve
                    </button>
                    <button id="rejectBtn" class="reject-button" onclick="rejectRequest()">
                        <i class="fas fa-times-circle"></i> Reject
                    </button>
                </div>
            </div>
            {% endif %}

            <!-- Single Card for All Details -->
            <div class="detail-container">
                <div class="row"> <!-- This row will act as a flex container for the two columns -->
                    <!-- Left Column (First Half) -->
                    <div class="col-6">
                        <div class="detail-item">
                            <h3>Input RequestType</h3>
                            <p id="inputRequestType">{{request.RequestTypeObj.RequestTypeName}}</p>
                        </div>
                        <div class="detail-item">
                            <h3>File Name</h3>
                            <p id="fileName" title="{{request.InputFileName}}">{{request.InputFileName}}</p>
                        </div>
                        <div class="detail-item">
                            <h3>FileUUID</h3>
                            <p id="fileUUID">{{request.FileUUID}}</p>
                        </div>
                        <div class="detail-item">
                            <h3>Request Time</h3>
                            <p id="requestTime" class="formatted-time">{{request.RequestTime}}</p>
                        </div>
                        <div class="detail-item file-detail-item">
                            <h3>Input File</h3>
                            <ul id="fileDownloadList"></ul>
                        </div>
                    </div>

                    <!-- Right Column (Second Half) -->
                    <div class="col-6">
                        <div class="detail-item">
                            <h3>Status</h3>
                            <p id="status">
                                <script>
                                    document.write(createStatusBadge('{{request.StatusObj.StatusName}}'));
                                </script>
                            </p>
                        </div>
                        <div class="detail-item">
                            <h3>Stage</h3>
                            <p id="stage">{{request.StageObj.StageName}}</p>
                        </div>
                        <div class="detail-item">
                            <h3>Sub Stage</h3>
                            {% if request.StageObj.SubStageObj %}
                                <p id="sub_stage">{{ request.StageObj.SubStageObj.SubStageName }}</p>
                            {% else %}
                                <p id="sub_stage">N/A</p> <!-- Optional: Show "N/A" or leave it empty -->
                            {% endif %}
                        </div>
                        <div class="detail-item">
                            <h3>Created Date</h3>
                            <p id="createdDate" class="formatted-date">
                                <span class="date-part" id="formattedDate">{{ request.CreatedDate }}</span>
                            </p>
                        </div>
                        <div class="detail-item">
                            <h3>SupplierName</h3>
                            <p id="supplier">{{request.SupplierName}}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tabs for Sections with Modern Design -->
            <div class="tab-container">
                <button class="tab active" onclick="showCounts('ValidationProcess')">
                    <i class="fas fa-check-circle"></i> ValidationProcess
                </button>
                {% if request.RequestTypeObj.RequestTypeID == 1 or request.RequestTypeObj.RequestTypeID == 2 %}
                    <button class="tab" onclick="showCounts('ProductProcess')">
                        <i class="fas fa-box"></i> ProductProcess
                    </button>
                {% endif %}
                {% if request.RequestTypeObj.RequestTypeID == 1 or request.RequestTypeObj.RequestTypeID == 3 %}
                <button class="tab" onclick="showCounts('PriceProcess')">
                    <i class="fas fa-tag"></i> PriceProcess
                </button>
                {% endif %}
                {% if request.RequestTypeObj.RequestTypeID == 1 or request.RequestTypeObj.RequestTypeID == 4 %}
                <button class="tab" onclick="showCounts('StockProcess')">
                    <i class="fas fa-warehouse"></i> StockProcess
                </button>
                {% endif %}
            </div>


            <!-- Improved Data Count Sections -->
            <div id="ValidationProcess" class="count-details active">
                <h4>Validation Process Counts</h4>
                <div class="count-section">
                    <div class="row">
                        <div class="col-6">
                            <div class="count-grid">
                                <div class="count-label">Input Data Count:</div>
                                <div class="count-value" id="ValidationProcessInput">
                                    {% if request.RequestReport is defined %}
                                        {% for report in request.RequestReport %}
                                            {% if report.ImportTypeId.ImportTypeName == 'ValidationProcess' %}
                                                {{ report.InputDataCount }}
                                            {% endif %}
                                        {% endfor %}
                                    {% else %}
                                        0
                                    {% endif %}
                                </div>
                            </div>
                            <div class="count-grid count-new">
                                <div class="count-label">New Data Count:</div>
                                <div class="count-value" id="ValidationProcessNew">
                                    {% if request.RequestReport is defined %}
                                        {% for report in request.RequestReport %}
                                            {% if report.ImportTypeId.ImportTypeName == 'ValidationProcess' %}
                                                {{ report.NewDataCount }}
                                            {% endif %}
                                        {% endfor %}
                                    {% else %}
                                        0
                                    {% endif %}
                                </div>
                            </div>
                            <div class="count-grid count-exists">
                                <div class="count-label">Exists Data Count:</div>
                                <div class="count-value" id="ValidationProcessExists">
                                    {% for report in request.RequestReport %}
                                        {% if report.ImportTypeId.ImportTypeName == 'ValidationProcess' %}
                                            {{ report.ExistsDataCount }}
                                        {% endif %}
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="count-grid count-error">
                                <div class="count-label">Error Data Count:</div>
                                <div class="count-value" id="ValidationProcessError">
                                    {% for report in request.RequestReport %}
                                        {% if report.ImportTypeId.ImportTypeName == 'ValidationProcess' %}
                                            {{ report.ErrorDataCount }}
                                        {% endif %}
                                    {% endfor %}
                                </div>
                            </div>
                            <div class="count-grid count-update">
                                <div class="count-label">Update Data Count:</div>
                                <div class="count-value" id="ValidationProcessUpdate">
                                    {% for report in request.RequestReport %}
                                        {% if report.ImportTypeId.ImportTypeName == 'ValidationProcess' %}
                                            {{ report.UpdateDataCount }}
                                        {% endif %}
                                    {% endfor %}
                                </div>
                            </div>
                            <div class="count-grid">
                                <div class="count-label">Duplicate Data Count:</div>
                                <div class="count-value" id="ValidationProcessDuplicate">
                                    {% for report in request.RequestReport %}
                                        {% if report.ImportTypeId.ImportTypeName == 'ValidationProcess' %}
                                            {{ report.DuplicateDataCount }}
                                        {% endif %}
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="output-file-card">
                    <h4>Output Files</h4>
                    <ul id="ValidationProcessFiles" class="file-list">
                        {% if request.OutputFile is defined %}
                            {% for file in request.OutputFile %}
                                {% if file.ImportTypeId.ImportTypeName == 'ValidationProcess' %}
                                    <li class="file-item">
                                        <span title="{{ file.FileName }}">{{ file.FileName }}</span>
                                        <a href="{{ request.downloadFileBaseURL }}/api/external/file/downloadFile/?bucket_uuid={{ request.bucketUUID }}&file_uuid_name={{ file.FileUUID }}" class="file-download" download>
                                            <i class="fas fa-download"></i> Download
                                        </a>
                                    </li>
                                {% endif %}
                            {% endfor %}
                        {% endif %}
                    </ul>
                </div>
            </div>
            {% if request.RequestTypeObj.RequestTypeID == 1 or request.RequestTypeObj.RequestTypeID == 2 %}
            <div id="ProductProcess" class="count-details">
                {% if request.ProcessRequestTime %}
                    <h4>Product Process Counts <span class="time-duration"><i class="fas fa-stopwatch"></i><span class="time-duration-label">Time taken:</span><span class="time-duration-value" data-duration="{{request.ProcessRequestTime.ProductProcess}}">{{request.ProcessRequestTime.ProductProcess}}</span></span></h4>
                {% else %}
                    <h4>Product Process Counts </h4>
                {% endif %}
            <div class="count-section">
                <div class="row">
                    <!-- First Section: Input, New, Exists Data Counts -->
                    <div class="col-6">
                        <div class="count-grid">
                            <div class="count-label">Input Data Count:</div>
                            <div class="count-value" id="ProductProcessInput">
                                {% for report in request.RequestReport %}
                                    {% if report.ImportTypeId.ImportTypeName == 'ProductProcess' %}
                                        {{ report.InputDataCount }}
                                    {% endif %}
                                {% endfor %}
                            </div>
                        </div>
                        <div class="count-grid count-new">
                            <div class="count-label">New Data Count:</div>
                            <div class="count-value" id="ProductProcessNew">
                                {% for report in request.RequestReport %}
                                    {% if report.ImportTypeId.ImportTypeName == 'ProductProcess' %}
                                        {{ report.NewDataCount }}
                                    {% endif %}
                                {% endfor %}
                            </div>
                        </div>
                        <div class="count-grid count-exists">
                            <div class="count-label">Exists Data Count:</div>
                            <div class="count-value" id="ProductProcessExists">
                                {% for report in request.RequestReport %}
                                    {% if report.ImportTypeId.ImportTypeName == 'ProductProcess' %}
                                        {{ report.ExistsDataCount }}
                                    {% endif %}
                                {% endfor %}
                            </div>
                        </div>
                        <div class="count-grid count-error">
                            <div class="count-label">Error Data Count:</div>
                            <div class="count-value" id="ProductProcessError">
                                {% for report in request.RequestReport %}
                                    {% if report.ImportTypeId.ImportTypeName == 'ProductProcess' %}
                                        {{ report.ErrorDataCount }}
                                    {% endif %}
                                {% endfor %}
                            </div>
                        </div>
                        <div class="count-grid count-update">
                            <div class="count-label">Update Data Count:</div>
                            <div class="count-value" id="ProductProcessUpdate">
                                {% for report in request.RequestReport %}
                                    {% if report.ImportTypeId.ImportTypeName == 'ProductProcess' %}
                                        {{ report.UpdateDataCount }}
                                    {% endif %}
                                {% endfor %}
                            </div>
                        </div>
                        <div class="count-grid" style="display: none;">
                            <div class="count-label">Duplicate Data Count:</div>
                            <div class="count-value" id="ProductProcessDuplicate">
                                {% for report in request.RequestReport %}
                                    {% if report.ImportTypeId.ImportTypeName == 'ProductProcess' %}
                                        {{ report.DuplicateDataCount }}
                                    {% endif %}
                                {% endfor %}
                            </div>
                        </div>
                    </div>

                    <!-- Second Section: ChemIndex Counts -->
                    <div class="col-6">
                        {% for report in request.RequestReport %}
                            {% if report.ChemIndexObj and report.ImportTypeId.ImportTypeName == 'ProductProcess' %}
                                <div class="count-grid">
                                    <div class="count-label">ChemIndex Input Data Count:</div>
                                    <div class="count-value" id="ChemindexProductProcessInput">{{ report.ChemIndexObj.ChemIndexInputDataCount }}</div>
                                </div>
                                <div class="count-grid count-new">
                                    <div class="count-label">ChemIndex New Data Count:</div>
                                    <div class="count-value" id="ChemindexProductProcessNew">{{ report.ChemIndexObj.ChemIndexNewDataCount }}</div>
                                </div>
                                <div class="count-grid count-error">
                                    <div class="count-label">ChemIndex Error Data Count:</div>
                                    <div class="count-value" id="ChemindexProductProcessError">{{ report.ChemIndexObj.ChemIndexErrorDataCount }}</div>
                                </div>
                                <div class="count-grid">
                                    <div class="count-label">ChemIndex Association Data Count:</div>
                                    <div class="count-value" id="ChemindexProductProcessAssociation">{{ report.ChemIndexObj.ChemIndexAssociationDataCount }}</div>
                                </div>
                                <div class="count-grid count-exists">
                                    <div class="count-label">ChemIndex Exist Data Count:</div>
                                    <div class="count-value" id="ChemindexProductProcessExist">{{ report.ChemIndexObj.ChemIndexExistDataCount }}</div>
                                </div>
                            {% endif %}
                        {% endfor %}
                    </div>
                </div>
            </div>
            <div class="output-file-card">
                <h4>Output Files</h4>
                <ul id="ProductProcessFiles" class="file-list">
                    {% for file in request.OutputFile %}
                        {% if file.ImportTypeId.ImportTypeName == 'ProductProcess' %}
                            <li class="file-item">
                                <span title="{{ file.FileName }}">{{ file.FileName }}</span>
                                <a href="{{ request.downloadFileBaseURL }}/api/external/file/downloadFile/?bucket_uuid={{ request.bucketUUID }}&file_uuid_name={{ file.FileUUID }}" class="file-download" download>
                                    <i class="fas fa-download"></i> Download
                                </a>
                            </li>
                        {% endif %}
                    {% endfor %}
                </ul>
            </div>
        </div>
        {% endif %}

        {% if request.RequestTypeObj.RequestTypeID == 1 or request.RequestTypeObj.RequestTypeID == 3 %}
        <div id="PriceProcess" class="count-details">
            {% if request.ProcessRequestTime %}
                <h4>Price Process Counts <span class="time-duration"><i class="fas fa-stopwatch"></i><span class="time-duration-label">Time taken:</span><span class="time-duration-value" data-duration="{{request.ProcessRequestTime.PriceProcess}}">{{request.ProcessRequestTime.PriceProcess}}</span></span></h4>
            {% else %}
                <h4>Price Process Counts</h4>
            {% endif %}
            <div class="count-section">
                <div class="row">
                    <!-- First Section: Input, New, Exists Data Counts -->
                    <div class="col-6">
                        <div class="count-grid">
                            <div class="count-label">Input Data Count:</div>
                            <div class="count-value" id="PriceProcessInput">
                                {% for report in request.RequestReport %}
                                    {% if report.ImportTypeId.ImportTypeName == 'PriceProcess' %}
                                        {{ report.InputDataCount }}
                                    {% endif %}
                                {% endfor %}
                            </div>
                        </div>
                        <div class="count-grid count-new">
                            <div class="count-label">New Data Count:</div>
                            <div class="count-value" id="PriceProcessNew">
                                {% for report in request.RequestReport %}
                                    {% if report.ImportTypeId.ImportTypeName == 'PriceProcess' %}
                                        {{ report.NewDataCount }}
                                    {% endif %}
                                {% endfor %}
                            </div>
                        </div>
                        <div class="count-grid count-exists">
                            <div class="count-label">Exists Data Count:</div>
                            <div class="count-value" id="PriceProcessExists">
                                {% for report in request.RequestReport %}
                                    {% if report.ImportTypeId.ImportTypeName == 'PriceProcess' %}
                                        {{ report.ExistsDataCount }}
                                    {% endif %}
                                {% endfor %}
                            </div>
                        </div>
                    </div>

                    <!-- Second Section: Error, Update, Duplicate Data Counts -->
                    <div class="col-6">
                        <div class="count-grid count-error">
                            <div class="count-label">Error Data Count:</div>
                            <div class="count-value" id="PriceProcessError">
                                {% for report in request.RequestReport %}
                                    {% if report.ImportTypeId.ImportTypeName == 'PriceProcess' %}
                                        {{ report.ErrorDataCount }}
                                    {% endif %}
                                {% endfor %}
                            </div>
                        </div>
                        <div class="count-grid count-update">
                            <div class="count-label">Update Data Count:</div>
                            <div class="count-value" id="PriceProcessUpdate">
                                {% for report in request.RequestReport %}
                                    {% if report.ImportTypeId.ImportTypeName == 'PriceProcess' %}
                                        {{ report.UpdateDataCount }}
                                    {% endif %}
                                {% endfor %}
                            </div>
                        </div>
                        <div class="count-grid">
                            <div class="count-label">Duplicate Data Count:</div>
                            <div class="count-value" id="PriceProcessDuplicate">
                                {% for report in request.RequestReport %}
                                    {% if report.ImportTypeId.ImportTypeName == 'PriceProcess' %}
                                        {{ report.DuplicateDataCount }}
                                    {% endif %}
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="output-file-card">
                <h4>Output Files</h4>
                <ul id="PriceProcessFiles" class="file-list">
                    {% for file in request.OutputFile %}
                        {% if file.ImportTypeId.ImportTypeName == 'PriceProcess' %}
                            <li class="file-item">
                                <span title="{{ file.FileName }}">{{ file.FileName }}</span>
                                <a href="{{ request.downloadFileBaseURL }}/api/external/file/downloadFile/?bucket_uuid={{ request.bucketUUID }}&file_uuid_name={{ file.FileUUID }}" class="file-download" download>
                                    <i class="fas fa-download"></i> Download
                                </a>
                            </li>
                        {% endif %}
                    {% endfor %}
                </ul>
            </div>
        </div>
        {% endif %}

        {% if request.RequestTypeObj.RequestTypeID == 1 or request.RequestTypeObj.RequestTypeID == 4 %}
        <div id="StockProcess" class="count-details">
            {% if request.ProcessRequestTime %}
                <h4>Stock Process Counts <span class="time-duration"><i class="fas fa-stopwatch"></i><span class="time-duration-label">Time taken:</span><span class="time-duration-value" data-duration="{{request.ProcessRequestTime.StockProcess}}">{{request.ProcessRequestTime.StockProcess}}</span></span></h4>
            {% else %}
                <h4>Stock Process Counts</h4>
            {% endif %}
            <div class="count-section">
                <div class="row">
                    <!-- First Section: Input, New, Exists Data Counts -->
                    <div class="col-6">
                        <div class="count-grid">
                            <div class="count-label">Input Data Count:</div>
                            <div class="count-value" id="StockProcessInput">
                                {% for report in request.RequestReport %}
                                    {% if report.ImportTypeId.ImportTypeName == 'StockProcess' %}
                                        {{ report.InputDataCount }}
                                    {% endif %}
                                {% endfor %}
                            </div>
                        </div>
                        <div class="count-grid count-new">
                            <div class="count-label">New Data Count:</div>
                            <div class="count-value" id="StockProcessNew">
                                {% for report in request.RequestReport %}
                                    {% if report.ImportTypeId.ImportTypeName == 'StockProcess' %}
                                        {{ report.NewDataCount }}
                                    {% endif %}
                                {% endfor %}
                            </div>
                        </div>
                        <div class="count-grid count-exists">
                            <div class="count-label">Exists Data Count:</div>
                            <div class="count-value" id="StockProcessExists">
                                {% for report in request.RequestReport %}
                                    {% if report.ImportTypeId.ImportTypeName == 'StockProcess' %}
                                        {{ report.ExistsDataCount }}
                                    {% endif %}
                                {% endfor %}
                            </div>
                        </div>
                    </div>

                    <!-- Second Section: Error, Update, Duplicate Data Counts -->
                    <div class="col-6">
                        <div class="count-grid count-error">
                            <div class="count-label">Error Data Count:</div>
                            <div class="count-value" id="StockProcessError">
                                {% for report in request.RequestReport %}
                                    {% if report.ImportTypeId.ImportTypeName == 'StockProcess' %}
                                        {{ report.ErrorDataCount }}
                                    {% endif %}
                                {% endfor %}
                            </div>
                        </div>
                        <div class="count-grid count-update">
                            <div class="count-label">Update Data Count:</div>
                            <div class="count-value" id="StockProcessUpdate">
                                {% for report in request.RequestReport %}
                                    {% if report.ImportTypeId.ImportTypeName == 'StockProcess' %}
                                        {{ report.UpdateDataCount }}
                                    {% endif %}
                                {% endfor %}
                            </div>
                        </div>
                        <div class="count-grid">
                            <div class="count-label">Duplicate Data Count:</div>
                            <div class="count-value" id="StockProcessDuplicate">
                                {% for report in request.RequestReport %}
                                    {% if report.ImportTypeId.ImportTypeName == 'StockProcess' %}
                                        {{ report.DuplicateDataCount }}
                                    {% endif %}
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="output-file-card">
                <h4>Output Files</h4>
                <ul id="StockProcessFiles" class="file-list">
                    {% for file in request.OutputFile %}
                        {% if file.ImportTypeId.ImportTypeName == 'StockProcess' %}
                            <li class="file-item">
                                <span title="{{ file.FileName }}">{{ file.FileName }}</span>
                                <a href="{{ request.downloadFileBaseURL }}/api/external/file/downloadFile/?bucket_uuid={{ request.bucketUUID }}&file_uuid_name={{ file.FileUUID }}" class="file-download" download>
                                    <i class="fas fa-download"></i> Download
                                </a>
                            </li>
                        {% endif %}
                    {% endfor %}
                </ul>
            </div>
        </div>
        {% endif %}
        <!-- Old Approval Actions Section Removed -->

            <!-- Hidden elements for backward compatibility -->
            <div style="display: none;" id="LogsClass"></div>
            <section style="display: none;" id="logSection"></section>
        </div><!-- End of request-detail-container -->

    {% block extra_scripts %}

    <script>
        // Sample data from response
        const requestReport = {% if request.RequestReport is defined %}{{ request.RequestReport | tojson | safe }}{% else %}[]{% endif %};
        const outputFiles = {% if request.OutputFile is defined %}{{ request.OutputFile | tojson | safe }}{% else %}[]{% endif %};

        let allLogs = [];

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            // Set up logs panel toggle
            const showLogsBtn = document.getElementById('showLogsBtn');
            const logsPanel = document.getElementById('logsPanel');
            const overlay = document.getElementById('overlay');
            const closeLogs = document.getElementById('closeLogs');

            // Create loader overlay if it doesn't exist
            if (document.querySelector('.loader-overlay') === null) {
                const loaderOverlay = document.createElement('div');
                loaderOverlay.className = 'loader-overlay';

                const loader = document.createElement('div');
                loader.className = 'loader';

                loaderOverlay.appendChild(loader);
                document.querySelector('.request-detail-container').appendChild(loaderOverlay);
            }

            // Show logs panel when log icon is clicked
            showLogsBtn.addEventListener('click', function() {
                logsPanel.classList.add('active');
                overlay.classList.add('active');

                // Only fetch logs if we don't already have them
                if (!allLogs || allLogs.length === 0) {
                    fetchLogs(); // Fetch logs when panel is opened
                } else {
                    // Use the logs we already have
                    populateDropdown(allLogs);
                    displayLogs(allLogs);
                }
            });

            // Close logs panel when close button is clicked
            closeLogs.addEventListener('click', function() {
                logsPanel.classList.remove('active');
                overlay.classList.remove('active');
            });

            // Close logs panel when overlay is clicked
            overlay.addEventListener('click', function() {
                logsPanel.classList.remove('active');
                overlay.classList.remove('active');
            });

            // Format the date in a more readable way
            formatDate();

            // Format the request time
            formatRequestTime();

            // Format time durations
            formatTimeDurations();

            // Initialize counts for ValidationProcess
            showCounts('ValidationProcess');

            // Fetch and display approval reasons if in approval stage
            {% if request.StatusObj.StatusName == "Pending" and request.StageObj.StageName == "Approval" %}
                // Check if we have approval reasons from the backend
                const approvalReasons = {{ request.approval_reasons | tojson | safe if request.approval_reasons is defined else '[]' }};
                const approvalCategories = {{ request.approval_categories | tojson | safe if request.approval_categories is defined else '{}' }};
                const isMultiScenario = {{ request.is_multi_scenario | tojson | safe if request.is_multi_scenario is defined else 'false' }};

                console.log("Backend approval reasons:", approvalReasons);
                console.log("Backend approval categories:", approvalCategories);
                console.log("Is multi-scenario:", isMultiScenario);

                if (approvalReasons && approvalReasons.length > 0) {
                    // We have approval reasons from the backend, display them
                    console.log("Displaying approval reasons from backend data");
                    displayApprovalReasons(approvalReasons, approvalCategories, isMultiScenario);
                } else {
                    // Fallback to fetching approval reasons via API
                    console.log("Fetching approval reasons via API");
                    fetchApprovalReasons();
                }
            {% endif %}
        });

        // Function to display approval reasons from backend data
        function displayApprovalReasons(reasons, categories, isMultiScenario) {
            const approvalSection = document.getElementById('approvalReasonsSection');
            if (!approvalSection) return;

            let reasonsHTML = '';

            if (isMultiScenario) {
                // Multi-scenario case - show categorized view
                reasonsHTML = `
                    <div class="approval-reasons-title">
                        <i class="fas fa-exclamation-triangle"></i> Multiple Approval Issues (${reasons.length})
                    </div>
                `;

                const categoryLabels = {
                    'supplier': 'Supplier Issues',
                    'catalog': 'Catalog ID Issues',
                    'property': 'Property Mapping Issues',
                    'warehouse': 'Warehouse Issues',
                    'unit': 'Unit Issues'
                };

                const categoryIcons = {
                    'supplier': 'fa-building',
                    'catalog': 'fa-percentage',
                    'property': 'fa-columns',
                    'warehouse': 'fa-warehouse',
                    'unit': 'fa-ruler'
                };

                // Create a section for each category that has reasons
                for (const [category, categoryReasons] of Object.entries(categories)) {
                    if (categoryReasons.length > 0) {
                        const categoryLabel = categoryLabels[category] || 'Other Issues';
                        const categoryIcon = categoryIcons[category] || 'fa-info-circle';

                        reasonsHTML += `
                            <div class="approval-reason-category">
                                <div class="category-header ${category}-category">
                                    <i class="fas ${categoryIcon}"></i> ${categoryLabel} (${categoryReasons.length})
                                </div>
                                <div class="category-reasons">
                        `;

                        // Add each reason in this category
                        categoryReasons.forEach(reason => {
                            reasonsHTML += `
                                <div class="approval-reason-item ${category}-reason">
                                    <i class="fas ${categoryIcon}"></i>
                                    <span>${reason}</span>
                                </div>
                            `;
                        });

                        reasonsHTML += `
                                </div>
                            </div>
                        `;
                    }
                }
            } else {
                // Single scenario case
                reasonsHTML = `
                    <div class="approval-reasons-title">
                        <i class="fas fa-exclamation-triangle"></i> Approval Reasons (${reasons.length})
                    </div>
                `;

                reasons.forEach(reason => {
                    let reasonClass = 'approval-reason-item';
                    let icon = 'fa-info-circle';

                    // Determine reason type based on content
                    if (reason.includes('New supplier') || reason.includes('supplier has no existing data')) {
                        reasonClass += ' supplier-reason';
                        icon = 'fa-building';
                    } else if (reason.includes('catalog ID match') || reason.includes('match percentage')) {
                        reasonClass += ' catalog-reason';
                        icon = 'fa-percentage';
                    } else if (reason.includes('Unmapped property') || reason.includes('unmapped columns')) {
                        reasonClass += ' property-reason';
                        icon = 'fa-columns';
                    } else if (reason.includes('Invalid warehouse')) {
                        reasonClass += ' warehouse-reason';
                        icon = 'fa-warehouse';
                    } else if (reason.includes('Invalid unit') || reason.includes('invalid units')) {
                        reasonClass += ' unit-reason';
                        icon = 'fa-ruler';
                    }

                    reasonsHTML += `
                        <div class="${reasonClass}">
                            <i class="fas ${icon}"></i>
                            <span>${reason}</span>
                        </div>
                    `;
                });
            }

            approvalSection.innerHTML = reasonsHTML;

            // Store the reasons and categories for use in approval/rejection dialogs
            window.approvalData = {
                reasons: reasons,
                categories: categories,
                isMultiScenario: isMultiScenario
            };
        }

        // Function to format the request time in a more modern way
        function formatRequestTime() {
            const requestTimeElement = document.getElementById('requestTime');
            if (requestTimeElement) {
                const originalTime = requestTimeElement.textContent.trim();

                try {
                    // Parse the time string (assuming it's in the format "X hours, Y minutes, Z seconds")
                    const durationParts = originalTime.split(',').map(part => part.trim());

                    // Create a more readable format
                    let formattedDuration = '';

                    durationParts.forEach(part => {
                        // Extract the number and unit
                        const matches = part.match(/(\d+)\s+(\w+)/);
                        if (matches) {
                            const value = matches[1];
                            const unit = matches[2];

                            // Format each part
                            if (unit === 'hours' || unit === 'hour') {
                                formattedDuration += `${value}<span style="font-size:0.8em">h</span> `;
                            } else if (unit === 'minutes' || unit === 'minute') {
                                formattedDuration += `${value}<span style="font-size:0.8em">m</span> `;
                            } else if (unit === 'seconds' || unit === 'second') {
                                formattedDuration += `${value}<span style="font-size:0.8em">s</span>`;
                            } else {
                                formattedDuration += `${value} ${unit} `;
                            }
                        }
                    });

                    const timeHTML = `
                        <div class="time-duration">
                            <i class="fas fa-clock"></i>
                            <span class="time-duration-value">${formattedDuration || originalTime}</span>
                        </div>
                    `;

                    // Update the element
                    requestTimeElement.innerHTML = timeHTML;
                } catch (error) {
                    console.error('Error formatting request time:', error);
                    // Keep original time if there's an error
                }
            }
        }

        // Function to format time durations in a more modern way
        function formatTimeDurations() {
            const durationElements = document.querySelectorAll('.time-duration-value[data-duration]');

            durationElements.forEach(element => {
                const originalDuration = element.getAttribute('data-duration');

                try {
                    const cleanedDuration = originalDuration.split(/[^\d.,\s\w]/)[0];
                    const durationParts = cleanedDuration.split(',').map(part => part.trim());

                    // Create a more readable format
                    let formattedDuration = '';

                    durationParts.forEach(part => {
                        const matches = part.match(/(\d+(?:\.\d+)?)\s+(\w+)/);
                        if (matches) {
                            const value = matches[1];
                            const unit = matches[2];

                            if (unit.startsWith('hour')) {
                                formattedDuration += `${value}<span style="font-size:0.8em">h</span> `;
                            } else if (unit.startsWith('minute')) {
                                formattedDuration += `${value}<span style="font-size:0.8em">m</span> `;
                            } else if (unit.startsWith('second')) {
                                formattedDuration += `${value}<span style="font-size:0.8em">s</span>`;
                            } else {
                                formattedDuration += `${value} ${unit} `;
                            }
                        }
                    });

                    element.innerHTML = formattedDuration.trim() || originalDuration;
                } catch (error) {
                    console.error('Error formatting time duration:', error);
                    // Keep original duration if there's an error
                    element.textContent = originalDuration;
                }
            });
        }

        // Function to format the date in a more readable way
        function formatDate() {
            const dateElement = document.getElementById('formattedDate');
            if (dateElement) {
                const originalDate = dateElement.textContent.trim();

                try {
                    // Parse the date string
                    const date = new Date(originalDate);

                    if (!isNaN(date.getTime())) {
                        // Get date components
                        const day = date.getDate();
                        const month = date.toLocaleString('en-US', { month: 'short' });
                        const year = date.getFullYear();
                        const weekday = date.toLocaleString('en-US', { weekday: 'short' });

                        // Get time components
                        let hours = date.getHours();
                        const minutes = date.getMinutes().toString().padStart(2, '0');
                        const seconds = date.getSeconds().toString().padStart(2, '0');
                        const ampm = hours >= 12 ? 'PM' : 'AM';

                        // Convert to 12-hour format
                        hours = hours % 12;
                        hours = hours ? hours : 12; // the hour '0' should be '12'

                        // Create formatted date and time with icons
                        const formattedDate = `<i class="fas fa-calendar-alt"></i> ${weekday}, ${month} ${day}, ${year}`;
                        const formattedTime = `<i class="fas fa-clock"></i> ${hours}:${minutes}:${seconds} ${ampm}`;

                        // Create HTML structure for formatted date
                        const dateHTML = `
                            <div class="date-wrapper">
                                <div class="date-part">${formattedDate}</div>
                                <div class="time-part">${formattedTime}</div>
                            </div>
                        `;

                        // Update the element
                        dateElement.innerHTML = dateHTML;
                    }
                } catch (error) {
                    console.error('Error formatting date:', error);
                    // Keep original date if there's an error
                }
            }
        }

        function updateCounts(section, data) {
            // Update the count data
            document.getElementById(`${section}Input`).textContent = data?.InputDataCount || 0;
            document.getElementById(`${section}New`).textContent = data?.NewDataCount || 0;
            document.getElementById(`${section}Exists`).textContent = data?.ExistsDataCount || 0;
            document.getElementById(`${section}Error`).textContent = data?.ErrorDataCount || 0;
            document.getElementById(`${section}Update`).textContent = data?.UpdateDataCount || 0;

            // Check if ImportTypeName is "ProductProcess"
            if (data?.ImportTypeId?.ImportTypeName === "ProductProcess") {
                // Hide the duplicate count grid for ProductProcess
                const duplicateGrid = document.getElementById(`${section}Duplicate`).closest('.count-grid');
                if (duplicateGrid) {
                    duplicateGrid.style.display = "none";
                }
            } else {
                document.getElementById(`${section}Duplicate`).textContent = data?.DuplicateDataCount || 0;
            }

            // Clear previous files list
            const filesList = document.getElementById(`${section}Files`);
            filesList.innerHTML = ''; // Clear previous content

            // Filter files corresponding to the section (ImportTypeName)
            const sectionData = requestReport.find(r => r.ImportTypeId.ImportTypeName === section);
            if (sectionData) {
                // Filter files by matching ImportTypeName
                const relevantFiles = outputFiles.filter(file => file.ImportTypeId.ImportTypeName === sectionData.ImportTypeId.ImportTypeName);

                // Add files to the list
                relevantFiles.forEach(file => {
                    const li = document.createElement('li');
                    li.className = 'file-item';

                    // File Name
                    const fileNameSpan = document.createElement('span');
                    fileNameSpan.textContent = file.FileName;
                    fileNameSpan.title = file.FileName; // Add title attribute for hover
                    fileNameSpan.style.marginRight = '15px';
                    fileNameSpan.style.maxWidth = '300px';
                    fileNameSpan.style.whiteSpace = 'nowrap';
                    fileNameSpan.style.overflow = 'hidden';
                    fileNameSpan.style.textOverflow = 'ellipsis';
                    fileNameSpan.style.cursor = 'help';
                    li.appendChild(fileNameSpan);

                    // Download Button with Icon
                    const downloadBtn = document.createElement('a');
                    downloadBtn.className = 'file-download';

                    // Add download icon
                    const downloadIcon = document.createElement('i');
                    downloadIcon.classList.add('fas', 'fa-download');
                    downloadBtn.appendChild(downloadIcon);

                    // Add text after icon
                    downloadBtn.appendChild(document.createTextNode(' Download'));

                    const bucketUUID = "{{ request.bucketUUID }}";
                    const downloadFileBaseURL = "{{request.downloadFileBaseURL}}";
                    // Prepare the API endpoint and query parameters
                    const apiUrl = `${downloadFileBaseURL}/api/external/file/downloadFile/?bucket_uuid=${bucketUUID}&file_uuid_name=${file.FileUUID}`;

                    // Add an event listener for the download button to trigger the API call
                    downloadBtn.addEventListener('click', (event) => {
                        event.preventDefault();  // Prevent default link behavior

                        // Call the external API to download the file
                        fetch(apiUrl)
                            .then(response => {
                                if (!response.ok) {
                                    throw new Error('Failed to download file');
                                }
                                return response.blob(); // Handle the file as a Blob
                            })
                            .then(blob => {
                                // Create a link to download the file
                                const downloadLink = document.createElement('a');
                                const fileURL = URL.createObjectURL(blob);
                                downloadLink.href = fileURL;
                                downloadLink.download = file.FileName;  // Set the filename from the file data
                                downloadLink.click();  // Trigger the download
                                URL.revokeObjectURL(fileURL); // Clean up
                            })
                            .catch(error => {
                                console.error('Error:', error);
                                alert('Error downloading the file. Please try again later.');
                            });
                    });

                    li.appendChild(downloadBtn);
                    filesList.appendChild(li);
                });
            }
        }

        function showCounts(section) {
            // Show loader
            const loaderOverlay = document.querySelector('.loader-overlay');
            if (loaderOverlay) {
                loaderOverlay.classList.add('visible');
            }

            // Add subtle animation
            document.querySelectorAll('.count-details').forEach(el => {
                if (el.classList.contains('active')) {
                    el.style.opacity = '0';
                    setTimeout(() => {
                        el.classList.remove('active');
                    }, 200);
                }
            });

            // Remove active class from all tabs
            document.querySelectorAll('.tab').forEach(el => el.classList.remove('active'));

            // Add active class to the selected tab
            document.querySelector(`button[onclick="showCounts('${section}')"]`).classList.add('active');

            // After a short delay, show the new section
            setTimeout(() => {
                // Add active class to the selected section
                const sectionEl = document.getElementById(section);
                sectionEl.classList.add('active');
                sectionEl.style.opacity = '1';

                // Find the section data from the request report
                let data = requestReport.find(r => r.ImportTypeId.ImportTypeName === section);
                updateCounts(section, data); // Update counts and files

                // Hide loader
                if (loaderOverlay) {
                    loaderOverlay.classList.remove('visible');
                }
            }, 250);
        }

        // Legacy function removed

        function fetchLogs() {
            // Show loader
            const loaderOverlay = document.querySelector('.loader-overlay');
            if (loaderOverlay) {
                loaderOverlay.classList.add('visible');
            }

            fetch(`{{ request_url.base_url }}api/get_log_by_id/{{ request._id }}`)
                .then(response => response.json())
                .then(response => {
                    allLogs = response.data || []; // Store logs globally
                    if (!Array.isArray(allLogs)) {
                        allLogs = [allLogs]; // Ensure array format
                    }

                    populateDropdown(allLogs); // Populate dropdown with unique ComponentNames
                    displayLogs(allLogs); // Display all logs initially

                    // Hide loader
                    if (loaderOverlay) {
                        loaderOverlay.classList.remove('visible');
                    }
                })
                .catch(error => {
                    console.error("Error fetching logs:", error);

                    // Hide loader on error too
                    if (loaderOverlay) {
                        loaderOverlay.classList.remove('visible');
                    }
                });
        }

        // Populate dropdown with unique ComponentNames
        function populateDropdown(logs) {
            const logFilter = document.getElementById("logFilter");
            logFilter.innerHTML = `<option value="all">All Components</option>`; // Reset dropdown

            const componentNames = [...new Set(logs.map(log => log.ComponentName))]; // Unique ComponentNames
            componentNames.forEach(name => {
                const option = document.createElement("option");
                option.value = name;
                option.textContent = name;
                logFilter.appendChild(option);
            });
        }

        // Filter logs based on selected component
        function filterLogs() {
            const selectedComponent = document.getElementById("logFilter").value;
            if (selectedComponent === "all") {
                displayLogs(allLogs); // Show all logs
            } else {
                const filteredLogs = allLogs.filter(log => log.ComponentName === selectedComponent);
                displayLogs(filteredLogs);
            }
        }

        // Display logs
        function displayLogs(logs) {
            const logContainer = document.getElementById("logContainer");
            logContainer.innerHTML = ""; // Clear previous logs

            if (logs.length === 0) {
                const emptyMessage = document.createElement('div');
                emptyMessage.className = 'log-card log-general';
                emptyMessage.innerHTML = '<div class="log-message">No logs available for this request.</div>';
                logContainer.appendChild(emptyMessage);
                return;
            }

            logs.reverse().forEach(log => { // Reverse and iterate over logs
                const logMessages = Array.isArray(log.Logs)
                    ? log.Logs
                    : [{ message: log.Logs || "No logs available" }];

                logMessages.forEach(logMsg => {
                    const logEntry = document.createElement("div");
                    logEntry.classList.add("log-card");
                    logEntry.classList.add(getLogClass(log.LogType));

                    logEntry.innerHTML = `
                        <div class="log-header">
                            <i class="fas ${getLogIcon(log.LogType)}"></i> ${log.LogType}
                        </div>
                        <div class="log-body">
                            <span class="log-component">${log.ComponentName}</span> |
                            <span class="log-request">#${log.ImportRequestID}</span> |
                            <span class="log-date">${formatLogDate(log.CreatedDateTime)}</span>
                        </div>
                        <div class="log-message">${formatMessage(logMsg.message)}</div>
                    `;

                    logContainer.appendChild(logEntry);
                });
            });
        }

        // Function to format the date into "dd-mm-yy hh:mm:ss"
        function formatLogDate(isoString) {
            const date = new Date(isoString);
            const day = String(date.getDate()).padStart(2, "0");
            const month = String(date.getMonth() + 1).padStart(2, "0"); // Months are 0-based
            const year = String(date.getFullYear()).slice(-2);
            const hours = String(date.getHours()).padStart(2, "0");
            const minutes = String(date.getMinutes()).padStart(2, "0");
            const seconds = String(date.getSeconds()).padStart(2, "0");

            return `${day}-${month}-${year} ${hours}:${minutes}:${seconds}`;
        }

        // Function to assign different colors for log types
        function getLogClass(logType) {
            switch (logType.toUpperCase()) {
                case "ERROR": return "log-error";
                case "INFO": return "log-info";
                case "RESULT": return "log-result";
                case "WARNING": return "log-warning";
                default: return "log-general";
            }
        }

        // Function to return icons based on log type
        function getLogIcon(logType) {
            switch (logType.toUpperCase()) {
                case "ERROR": return "fa-exclamation-circle";
                case "INFO": return "fa-info-circle";
                case "RESULT": return "fa-check-circle";
                case "WARNING": return "fa-exclamation-triangle";
                default: return "fa-file-alt";
            }
        }

        // Format message function
        function formatMessage(message) {
            if (!message) return "<i>No message available</i>";

            try {
                // Extract JSON part from message
                const jsonStartIndex = message.indexOf("{");
                const listStartIndex = message.indexOf("[");

                if (jsonStartIndex !== -1 || listStartIndex !== -1) {
                    const startIndex = (listStartIndex !== -1 && listStartIndex < jsonStartIndex)
                        ? listStartIndex
                        : jsonStartIndex;

                    message = message.substring(startIndex);
                }

                // Remove potential byte prefix (b' or b") and trailing characters
                message = message.trim().replace(/^b'|^b"/, "").replace(/'$/, "").replace(/"$/, "");

                // Convert single quotes to double quotes for improperly formatted JSON
                if (message.includes("'") && !message.includes('"')) {
                    message = message.replace(/'/g, '"');
                }

                // Parse JSON (handles both object `{}` and list `[]`)
                const jsonData = JSON.parse(message);

                // Return formatted JSON
                return `<pre>${JSON.stringify(jsonData, null, 4)}</pre>`;
            } catch (error) {
                // If parsing fails, return sanitized message as plain text
                return String(message)
                    .replace(/</g, "&lt;")
                    .replace(/>/g, "&gt;")
                    .replace(/&/g, "&amp;");
            }
        }


        // Approval and Rejection Functions moved to the enhanced versions below
        function showSuccessMessage(approved) {
            // Use the shared toast notification
            const message = approved ? 'Request approved successfully!' : 'Request rejected successfully!';
            showToastNotification(message, approved ? 'success' : 'danger');
        }

        async function updateRequestStatus(approved) {
            try {
                // Disable buttons during API call
                document.getElementById('approveBtn').disabled = true;
                document.getElementById('rejectBtn').disabled = true;

                // Call API to update request status
                const response = await fetch(`{{ request_url.base_url }}api/requests/{{ request._id }}/status`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        approved: approved
                    })
                });

                if (!response.ok) {
                    throw new Error(`Failed to ${approved ? 'approve' : 'reject'} request`);
                }

                const result = await response.json();

                // Show modern success message
                showSuccessMessage(approved);

                // Reload the page after a short delay to reflect the updated status
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } catch (error) {
                console.error(`Error ${approved ? 'approving' : 'rejecting'} request:`, error);
                alert(`Error ${approved ? 'approving' : 'rejecting'} request. Please try again.`);

                // Re-enable buttons
                document.getElementById('approveBtn').disabled = false;
                document.getElementById('rejectBtn').disabled = false;
            }
        }

        // Fetch approval reasons from logs
        async function fetchApprovalReasons() {
            // Only fetch if we're in the approval section
            const approvalSection = document.getElementById('approvalReasonsSection');
            if (!approvalSection) return;

            try {
                // Show a loading indicator
                approvalSection.innerHTML = `
                    <div class="approval-reasons-loading">
                        <i class="fas fa-spinner fa-spin"></i> Loading approval reasons...
                    </div>
                `;

                // Check if we already have logs data
                let logs = [];
                if (allLogs && allLogs.length > 0) {
                    console.log("Using existing logs data for approval reasons");
                    logs = allLogs;
                } else {
                    // Fetch logs for this request
                    console.log("Fetching logs data for approval reasons");
                    const response = await fetch(`{{ request_url.base_url }}api/get_log_by_id/{{ request._id }}`);
                    if (!response.ok) {
                        throw new Error('Failed to fetch logs');
                    }

                    const data = await response.json();
                    logs = data.data || [];

                    // Store logs globally for reuse
                    allLogs = logs;
                    if (!Array.isArray(allLogs)) {
                        allLogs = [allLogs]; // Ensure array format
                    }
                }

                // Filter logs for approval reasons with improved pattern matching
                const approvalLogs = logs.filter(log => {
                    if (log.LogType !== 'INFO') return false;

                    // Get the log text and convert to string if it's an array
                    let logText = '';
                    if (Array.isArray(log.Logs)) {
                        // If Logs is an array, join it
                        logText = log.Logs.map(item =>
                            typeof item === 'object' && item.message ? item.message : String(item)
                        ).join(' ');
                    } else if (typeof log.Logs === 'object' && log.Logs.message) {
                        // If Logs is an object with a message property
                        logText = log.Logs.message;
                    } else {
                        // Otherwise, convert to string
                        logText = String(log.Logs || '');
                    }

                    // Check for various approval-related patterns
                    const lowerLogText = logText.toLowerCase();
                    return (
                        logText.includes('Approval reason:') ||
                        logText.includes('approval reason:') ||
                        logText.includes('Approval required for the following reasons:') ||
                        logText.includes('approval required for the following reasons:') ||
                        lowerLogText.includes('requires approval') ||
                        lowerLogText.includes('approval required')
                    );
                });

                console.log("Found approval logs:", approvalLogs);

                if (approvalLogs.length === 0) {
                    // No approval reasons found
                    approvalSection.innerHTML = `
                        <div class="approval-reasons-title">
                            <i class="fas fa-info-circle"></i> No specific approval reasons found
                        </div>
                    `;
                    return;
                }

                // Extract reasons from logs with improved pattern matching
                const reasons = [];
                const categories = {
                    supplier: [],
                    catalog: [],
                    property: [],
                    warehouse: [],
                    unit: []
                };

                approvalLogs.forEach(log => {
                    // Get the log text and normalize it
                    let logText = '';
                    if (Array.isArray(log.Logs)) {
                        logText = log.Logs.map(item =>
                            typeof item === 'object' && item.message ? item.message : String(item)
                        ).join(' ');
                    } else if (typeof log.Logs === 'object' && log.Logs.message) {
                        logText = log.Logs.message;
                    } else {
                        logText = String(log.Logs || '');
                    }

                    console.log("Processing log text:", logText);

                    // Extract individual reasons with case-insensitive matching
                    if (logText.includes('Approval reason:') || logText.toLowerCase().includes('approval reason:')) {
                        let reason = '';
                        if (logText.includes('Approval reason:')) {
                            reason = logText.split('Approval reason:')[1].trim();
                        } else {
                            reason = logText.split('approval reason:')[1].trim();
                        }

                        if (reason) {
                            console.log("Found reason:", reason);
                            reasons.push(reason);

                            // Categorize the reason
                            if (reason.toLowerCase().includes('new supplier') || reason.toLowerCase().includes('supplier has no existing data')) {
                                categories.supplier.push(reason);
                            } else if (reason.toLowerCase().includes('catalog id match') || reason.toLowerCase().includes('match percentage')) {
                                categories.catalog.push(reason);
                            } else if (reason.toLowerCase().includes('unmapped property') || reason.toLowerCase().includes('unmapped columns')) {
                                categories.property.push(reason);
                            } else if (reason.toLowerCase().includes('invalid warehouse')) {
                                categories.warehouse.push(reason);
                            } else if (reason.toLowerCase().includes('invalid unit') || reason.toLowerCase().includes('invalid units')) {
                                categories.unit.push(reason);
                            }
                        }
                    }
                    // Extract combined reasons with case-insensitive matching
                    else if (logText.includes('Approval required for the following reasons:') ||
                             logText.toLowerCase().includes('approval required for the following reasons:')) {
                        let reasonsText = '';
                        if (logText.includes('Approval required for the following reasons:')) {
                            reasonsText = logText.split('Approval required for the following reasons:')[1].trim();
                        } else {
                            reasonsText = logText.split('approval required for the following reasons:')[1].trim();
                        }

                        console.log("Found combined reasons:", reasonsText);

                        // Split by semicolon or comma
                        reasonsText.replace(',', ';').split(';').forEach(reason => {
                            const cleanReason = reason.trim();
                            if (cleanReason) {
                                console.log("  - Reason:", cleanReason);
                                reasons.push(cleanReason);

                                // Categorize the reason
                                if (cleanReason.toLowerCase().includes('new supplier') ||
                                    cleanReason.toLowerCase().includes('supplier has no existing data')) {
                                    categories.supplier.push(cleanReason);
                                } else if (cleanReason.toLowerCase().includes('catalog id match') ||
                                           cleanReason.toLowerCase().includes('match percentage')) {
                                    categories.catalog.push(cleanReason);
                                } else if (cleanReason.toLowerCase().includes('unmapped property') ||
                                           cleanReason.toLowerCase().includes('unmapped columns')) {
                                    categories.property.push(cleanReason);
                                } else if (cleanReason.toLowerCase().includes('invalid warehouse')) {
                                    categories.warehouse.push(cleanReason);
                                } else if (cleanReason.toLowerCase().includes('invalid unit') ||
                                           cleanReason.toLowerCase().includes('invalid units')) {
                                    categories.unit.push(cleanReason);
                                }
                            }
                        });
                    }
                    // Check for other patterns that might contain approval reasons
                    else if (logText.toLowerCase().includes('requires approval') ||
                             logText.toLowerCase().includes('approval required')) {
                        console.log("Found requires approval text:", logText);

                        // Add the entire log as a reason if we can't extract a specific part
                        const cleanReason = logText.trim();
                        reasons.push(cleanReason);

                        // Categorize based on content
                        if (cleanReason.toLowerCase().includes('supplier')) {
                            categories.supplier.push(cleanReason);
                        } else if (cleanReason.toLowerCase().includes('catalog') ||
                                   cleanReason.toLowerCase().includes('match percentage')) {
                            categories.catalog.push(cleanReason);
                        } else if (cleanReason.toLowerCase().includes('property') ||
                                   cleanReason.toLowerCase().includes('column')) {
                            categories.property.push(cleanReason);
                        } else if (cleanReason.toLowerCase().includes('warehouse')) {
                            categories.warehouse.push(cleanReason);
                        } else if (cleanReason.toLowerCase().includes('unit')) {
                            categories.unit.push(cleanReason);
                        }
                    }
                });

                // Remove duplicates from each category
                for (const category in categories) {
                    categories[category] = [...new Set(categories[category])];
                }

                // Remove duplicates from the main list
                let uniqueReasons = [...new Set(reasons)];

                // Check if this is a multi-scenario case
                const activeCategories = Object.values(categories).filter(cat => cat.length > 0);
                const isMultiScenario = activeCategories.length > 1;

                console.log("Unique reasons:", uniqueReasons);
                console.log("Active categories:", activeCategories);
                console.log("Is multi-scenario:", isMultiScenario);

                // If no reasons were found, add a default reason
                if (uniqueReasons.length === 0) {
                    console.log("No specific reasons found, adding default reason");
                    const defaultReason = "Validation complete — general approval required to proceed";
                    uniqueReasons.push(defaultReason);

                    // Add to a default category
                    categories.supplier.push(defaultReason);
                }

                // Display reasons
                if (uniqueReasons.length > 0) {
                    let reasonsHTML = '';

                    if (isMultiScenario) {
                        // Multi-scenario case - show categorized view
                        reasonsHTML = `
                            <div class="approval-reasons-title">
                                <i class="fas fa-exclamation-triangle"></i> Multiple Approval Issues (${uniqueReasons.length})
                            </div>
                        `;

                        const categoryLabels = {
                            'supplier': 'Supplier Issues',
                            'catalog': 'Catalog ID Issues',
                            'property': 'Property Mapping Issues',
                            'warehouse': 'Warehouse Issues',
                            'unit': 'Unit Issues'
                        };

                        const categoryIcons = {
                            'supplier': 'fa-building',
                            'catalog': 'fa-percentage',
                            'property': 'fa-columns',
                            'warehouse': 'fa-warehouse',
                            'unit': 'fa-ruler'
                        };

                        // Create a section for each category that has reasons
                        for (const [category, categoryReasons] of Object.entries(categories)) {
                            if (categoryReasons.length > 0) {
                                const categoryLabel = categoryLabels[category] || 'Other Issues';
                                const categoryIcon = categoryIcons[category] || 'fa-info-circle';

                                reasonsHTML += `
                                    <div class="approval-reason-category">
                                        <div class="category-header ${category}-category">
                                            <i class="fas ${categoryIcon}"></i> ${categoryLabel} (${categoryReasons.length})
                                        </div>
                                        <div class="category-reasons">
                                `;

                                // Add each reason in this category
                                categoryReasons.forEach(reason => {
                                    reasonsHTML += `
                                        <div class="approval-reason-item ${category}-reason">
                                            <i class="fas ${categoryIcon}"></i>
                                            <span>${reason}</span>
                                        </div>
                                    `;
                                });

                                reasonsHTML += `
                                        </div>
                                    </div>
                                `;
                            }
                        }
                    } else {
                        // Single scenario case
                        reasonsHTML = `
                            <div class="approval-reasons-title">
                                <i class="fas fa-exclamation-triangle"></i> Approval Reasons (${uniqueReasons.length})
                            </div>
                        `;

                        uniqueReasons.forEach(reason => {
                            let reasonClass = 'approval-reason-item';
                            let icon = 'fa-info-circle';

                            // Determine reason type based on content
                            if (reason.includes('New supplier') || reason.includes('supplier has no existing data')) {
                                reasonClass += ' supplier-reason';
                                icon = 'fa-building';
                            } else if (reason.includes('catalog ID match') || reason.includes('match percentage')) {
                                reasonClass += ' catalog-reason';
                                icon = 'fa-percentage';
                            } else if (reason.includes('Unmapped property') || reason.includes('unmapped columns')) {
                                reasonClass += ' property-reason';
                                icon = 'fa-columns';
                            } else if (reason.includes('Invalid warehouse')) {
                                reasonClass += ' warehouse-reason';
                                icon = 'fa-warehouse';
                            } else if (reason.includes('Invalid unit') || reason.includes('invalid units')) {
                                reasonClass += ' unit-reason';
                                icon = 'fa-ruler';
                            }

                            reasonsHTML += `
                                <div class="${reasonClass}">
                                    <i class="fas ${icon}"></i>
                                    <span>${reason}</span>
                                </div>
                            `;
                        });
                    }

                    approvalSection.innerHTML = reasonsHTML;

                    // Store the reasons and categories for use in approval/rejection dialogs
                    window.approvalData = {
                        reasons: uniqueReasons,
                        categories: categories,
                        isMultiScenario: isMultiScenario
                    };
                } else {
                    approvalSection.innerHTML = `
                        <div class="approval-reasons-title">
                            <i class="fas fa-info-circle"></i> No specific approval reasons found
                        </div>
                    `;
                }

            } catch (error) {
                console.error('Error fetching approval reasons:', error);
                approvalSection.innerHTML = `
                    <div class="approval-reasons-title">
                        <i class="fas fa-exclamation-circle"></i> Error loading approval reasons
                    </div>
                `;
            }
        }

        // Update approval and rejection functions to include reasons
        function approveRequest() {
            // Get approval reasons if available
            let reasonsHTML = '';

            if (window.approvalData) {
                const { reasons, categories, isMultiScenario } = window.approvalData;

                if (reasons.length > 0) {
                    if (isMultiScenario) {
                        // Multi-scenario case - show categorized view
                        const categoryLabels = {
                            'supplier': 'Supplier Issues',
                            'catalog': 'Catalog ID Issues',
                            'property': 'Property Mapping Issues',
                            'warehouse': 'Warehouse Issues',
                            'unit': 'Unit Issues'
                        };

                        const categoryIcons = {
                            'supplier': 'fa-building',
                            'catalog': 'fa-percentage',
                            'property': 'fa-columns',
                            'warehouse': 'fa-warehouse',
                            'unit': 'fa-ruler'
                        };

                        let categorizedReasons = '';

                        // Create a section for each category that has reasons
                        for (const [category, categoryReasons] of Object.entries(categories)) {
                            if (categoryReasons.length > 0) {
                                const categoryLabel = categoryLabels[category] || 'Other Issues';
                                const categoryIcon = categoryIcons[category] || 'fa-info-circle';

                                categorizedReasons += `
                                    <li class="category-header">
                                        <strong><i class="fas ${categoryIcon}"></i> ${categoryLabel} (${categoryReasons.length})</strong>
                                    </li>
                                `;

                                // Add each reason in this category
                                categoryReasons.forEach(reason => {
                                    categorizedReasons += `
                                        <li><i class="fas ${categoryIcon}"></i> ${reason}</li>
                                    `;
                                });
                            }
                        }

                        reasonsHTML = `
                            <p>This request was flagged for <strong>multiple types of issues</strong>:</p>
                            <ul class="approval-reasons-list categorized">
                                ${categorizedReasons}
                            </ul>
                        `;
                    } else {
                        // Single scenario case
                        const reasonsList = reasons.map(reason => {
                            let icon = 'fa-info-circle';

                            // Determine icon based on reason content
                            if (reason.includes('New supplier') || reason.includes('supplier has no existing data')) {
                                icon = 'fa-building';
                            } else if (reason.includes('catalog ID match') || reason.includes('match percentage')) {
                                icon = 'fa-percentage';
                            } else if (reason.includes('Unmapped property') || reason.includes('unmapped columns')) {
                                icon = 'fa-columns';
                            } else if (reason.includes('Invalid warehouse')) {
                                icon = 'fa-warehouse';
                            } else if (reason.includes('Invalid unit') || reason.includes('invalid units')) {
                                icon = 'fa-ruler';
                            }

                            return `<li><i class="fas ${icon}"></i> ${reason}</li>`;
                        }).join('');

                        reasonsHTML = `
                            <p>This request was flagged for the following reasons:</p>
                            <ul class="approval-reasons-list">
                                ${reasonsList}
                            </ul>
                        `;
                    }
                }
            }

            // Use the shared modern confirmation dialog
            showModernConfirmDialog({
                title: 'Confirm Approval',
                message: `
                    <p>You are about to <strong>approve</strong> this request.</p>
                    ${reasonsHTML}
                    <p>This action will allow the data to be processed and cannot be undone.</p>
                `,
                requestId: '{{ request.requestTypeId }}',
                confirmText: 'Yes, Approve',
                type: 'success',
                onConfirm: () => updateRequestStatus(true)
            });
        }

        function rejectRequest() {
            // Get approval reasons if available
            let reasonsHTML = '';

            if (window.approvalData) {
                const { reasons, categories, isMultiScenario } = window.approvalData;

                if (reasons.length > 0) {
                    if (isMultiScenario) {
                        // Multi-scenario case - show categorized view
                        const categoryLabels = {
                            'supplier': 'Supplier Issues',
                            'catalog': 'Catalog ID Issues',
                            'property': 'Property Mapping Issues',
                            'warehouse': 'Warehouse Issues',
                            'unit': 'Unit Issues'
                        };

                        const categoryIcons = {
                            'supplier': 'fa-building',
                            'catalog': 'fa-percentage',
                            'property': 'fa-columns',
                            'warehouse': 'fa-warehouse',
                            'unit': 'fa-ruler'
                        };

                        let categorizedReasons = '';

                        // Create a section for each category that has reasons
                        for (const [category, categoryReasons] of Object.entries(categories)) {
                            if (categoryReasons.length > 0) {
                                const categoryLabel = categoryLabels[category] || 'Other Issues';
                                const categoryIcon = categoryIcons[category] || 'fa-info-circle';

                                categorizedReasons += `
                                    <li class="category-header">
                                        <strong><i class="fas ${categoryIcon}"></i> ${categoryLabel} (${categoryReasons.length})</strong>
                                    </li>
                                `;

                                // Add each reason in this category
                                categoryReasons.forEach(reason => {
                                    categorizedReasons += `
                                        <li><i class="fas ${categoryIcon}"></i> ${reason}</li>
                                    `;
                                });
                            }
                        }

                        reasonsHTML = `
                            <p>This request was flagged for <strong>multiple types of issues</strong>:</p>
                            <ul class="approval-reasons-list categorized">
                                ${categorizedReasons}
                            </ul>
                        `;
                    } else {
                        // Single scenario case
                        const reasonsList = reasons.map(reason => {
                            let icon = 'fa-info-circle';

                            // Determine icon based on reason content
                            if (reason.includes('New supplier') || reason.includes('supplier has no existing data')) {
                                icon = 'fa-building';
                            } else if (reason.includes('catalog ID match') || reason.includes('match percentage')) {
                                icon = 'fa-percentage';
                            } else if (reason.includes('Unmapped property') || reason.includes('unmapped columns')) {
                                icon = 'fa-columns';
                            } else if (reason.includes('Invalid warehouse')) {
                                icon = 'fa-warehouse';
                            } else if (reason.includes('Invalid unit') || reason.includes('invalid units')) {
                                icon = 'fa-ruler';
                            }

                            return `<li><i class="fas ${icon}"></i> ${reason}</li>`;
                        }).join('');

                        reasonsHTML = `
                            <p>This request was flagged for the following reasons:</p>
                            <ul class="approval-reasons-list">
                                ${reasonsList}
                            </ul>
                        `;
                    }
                }
            }

            // Use the shared modern confirmation dialog
            showModernConfirmDialog({
                title: 'Confirm Rejection',
                message: `
                    <p>You are about to <strong>reject</strong> this request.</p>
                    ${reasonsHTML}
                    <p>This action will prevent the data from being processed and cannot be undone.</p>
                `,
                requestId: '{{ request.requestTypeId }}',
                confirmText: 'Yes, Reject',
                type: 'danger',
                onConfirm: () => updateRequestStatus(false)
            });
        }

        // Initialize file download on page load
        document.addEventListener('DOMContentLoaded', function() {
            const fileUUID = "{{ request.FileUUID }}";
            const fileName = "{{ request.InputFileName }}";
            const bucketUUID = "{{ request.bucketUUID }}";
            const downloadFileBaseURL = "{{request.downloadFileBaseURL}}";

            // We don't need to fetch approval reasons here anymore since it's handled in the DOMContentLoaded event

            if (fileUUID && fileName) {
                const fileList = document.getElementById('fileDownloadList');

                // Create list item
                const li = document.createElement('li');
                li.className = 'file-item';

                // File Name Span
                const fileNameSpan = document.createElement('span');
                fileNameSpan.textContent = fileName;
                fileNameSpan.title = fileName; // Add title attribute for hover
                fileNameSpan.style.marginRight = '15px';
                fileNameSpan.style.padding = '0';
                fileNameSpan.style.display = 'inline-block';
                fileNameSpan.style.verticalAlign = 'middle';
                fileNameSpan.style.maxWidth = '300px';
                fileNameSpan.style.whiteSpace = 'nowrap';
                fileNameSpan.style.overflow = 'hidden';
                fileNameSpan.style.textOverflow = 'ellipsis';
                fileNameSpan.style.cursor = 'help';
                li.appendChild(fileNameSpan);

                // Download Button
                const downloadBtn = document.createElement('a');
                downloadBtn.className = 'file-download';

                // Add download icon
                const downloadIcon = document.createElement('i');
                downloadIcon.classList.add('fas', 'fa-download');
                downloadBtn.appendChild(downloadIcon);

                // Add text after icon
                downloadBtn.appendChild(document.createTextNode(' Download'));

                // API URL for downloading the file
                const apiUrl = `${downloadFileBaseURL}/api/external/file/downloadFile/?bucket_uuid=${bucketUUID}&file_uuid_name=${fileUUID}`;

                // Event listener for file download
                downloadBtn.addEventListener('click', (event) => {
                    event.preventDefault();

                    fetch(apiUrl)
                        .then(response => {
                            if (!response.ok) {
                                throw new Error('Failed to download file');
                            }
                            return response.blob();
                        })
                        .then(blob => {
                            // Create a link to download the file
                            const downloadLink = document.createElement('a');
                            const fileURL = URL.createObjectURL(blob);
                            downloadLink.href = fileURL;
                            downloadLink.download = fileName;
                            downloadLink.click();
                            URL.revokeObjectURL(fileURL); // Cleanup
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            alert('Error downloading the file. Please try again later.');
                        });
                });

                li.appendChild(downloadBtn);
                fileList.appendChild(li);
            }
        });


    </script>

    {% endblock %}
{% endblock %}