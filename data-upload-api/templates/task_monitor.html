{% extends "base.html" %}

{% block title %}Task Monitor - Data Import{% endblock %}

{% block extra_head %}
<style>
    /* Task Monitor Specific Styles */
    .task-monitor-container {
        background: white;
        border-radius: var(--border-radius-lg);
        box-shadow: var(--shadow);
        padding: var(--spacing-xl);
        margin-bottom: var(--spacing-xl);
        transition: box-shadow var(--transition);
    }

    .task-monitor-container:hover {
        box-shadow: var(--shadow-md);
    }

    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: var(--spacing-lg);
        padding-bottom: var(--spacing-md);
        border-bottom: 1px solid var(--gray-200);
    }

    .header-content {
        display: flex;
        flex-direction: column;
    }

    .page-header h2 {
        margin: 0;
        color: var(--gray-900);
        font-size: 1.75rem;
        display: flex;
        align-items: center;
    }

    .page-header h2 i {
        margin-right: var(--spacing-sm);
        color: var(--primary);
    }

    .page-description {
        color: var(--gray-600);
        font-size: 1rem;
        margin-top: var(--spacing-xs);
        margin-left: 28px; /* Align with the header text after the icon */
    }

    .refresh-controls {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
    }

    .auto-refresh-container {
        display: flex;
        align-items: center;
        padding: var(--spacing-xs) var(--spacing-sm);
        background: var(--gray-50);
        border-radius: var(--border-radius);
        border: 1px solid var(--gray-200);
        transition: all var(--transition-fast);
    }

    .auto-refresh-container:hover {
        background: var(--gray-100);
        border-color: var(--gray-300);
    }

    .auto-refresh-container.active {
        background: rgba(67, 97, 238, 0.1);
        border-color: var(--primary);
        color: var(--primary);
    }

    .auto-refresh-toggle {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        color: var(--gray-700);
        font-size: 0.9rem;
    }

    .toggle-switch {
        position: relative;
        width: 44px;
        height: 20px;
        background: var(--gray-300);
        border-radius: 10px;
        cursor: pointer;
        transition: all var(--transition);
        margin-right: var(--spacing-xs);
    }

    .toggle-switch.active {
        background: var(--success);
    }

    .toggle-switch::after {
        content: '';
        position: absolute;
        top: 2px;
        left: 2px;
        width: 16px;
        height: 16px;
        background: white;
        border-radius: 50%;
        transition: all var(--transition);
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    }

    .toggle-switch.active::after {
        transform: translateX(24px);
    }

    .auto-refresh-toggle label {
        margin: 0;
        font-weight: var(--font-weight-bold);
        cursor: pointer;
        user-select: none;
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
    }

    .auto-refresh-toggle input[type="checkbox"] {
        display: none;
    }

    .interval-selector {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        margin-left: var(--spacing-sm);
        padding-left: var(--spacing-sm);
        border-left: 1px solid var(--gray-300);
    }

    .interval-selector select {
        padding: var(--spacing-xs) var(--spacing-sm);
        border: 1px solid var(--gray-300);
        border-radius: var(--border-radius);
        font-size: 0.85rem;
        background: white;
        color: var(--gray-700);
        cursor: pointer;
        transition: all var(--transition-fast);
        margin-bottom: 0px;
    }

    .interval-selector select:focus {
        outline: none;
        border-color: var(--primary);
        box-shadow: 0 0 0 2px rgba(67, 97, 238, 0.15);
    }

    .interval-selector select:disabled {
        background: var(--gray-100);
        color: var(--gray-500);
        cursor: not-allowed;
    }

    .interval-label {
        font-size: 0.8rem;
        color: var(--gray-600);
        font-weight: normal;
    }

    .auto-refresh-container.active .interval-label {
        color: var(--primary);
    }

    .refresh-btn button {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        background-color: var(--gray-100);
        color: var(--gray-800);
        border: 1px solid var(--gray-300);
        padding: var(--spacing-sm) var(--spacing-md);
        border-radius: var(--border-radius);
        font-weight: var(--font-weight-bold);
        transition: all var(--transition-fast);
    }

    .refresh-btn button:hover {
        background-color: var(--gray-200);
        transform: translateY(-2px);
    }

    /* Stats Cards */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: var(--spacing-md);
        margin-bottom: var(--spacing-lg);
    }

    .stat-card {
        background-color: white;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-sm);
        padding: var(--spacing-md);
        display: flex;
        align-items: center;
        transition: all var(--transition-fast);
        border: 1px solid var(--gray-200);
    }

    .stat-card:hover {
        transform: translateY(-3px);
        box-shadow: var(--shadow-md);
    }

    .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: var(--spacing-md);
        font-size: 1.25rem;
        color: white;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .stat-icon.total {
        background-color: #4361ee;
    }

    .stat-icon.workers {
        background-color: #4caf50;
    }

    .stat-icon.active {
        background-color: #ffc107;
    }

    .stat-icon.failed {
        background-color: #f44336;
    }

    .stat-content {
        flex: 1;
    }

    .stat-value {
        font-size: 1.5rem;
        font-weight: var(--font-weight-bold);
        color: var(--gray-900);
        line-height: 1.2;
    }

    .stat-label {
        font-size: 0.875rem;
        color: var(--gray-600);
        margin-top: 2px;
    }

    /* Tabs */
    .monitor-tabs {
        display: flex;
        margin-bottom: var(--spacing-lg);
        gap: var(--spacing-md);
        border-bottom: 1px solid var(--gray-200);
        padding-bottom: var(--spacing-md);
    }

    .tab-btn {
        padding: var(--spacing-sm) var(--spacing-md);
        background: transparent;
        border: 1px solid var(--gray-300);
        cursor: pointer;
        transition: all var(--transition-fast);
        font-weight: var(--font-weight-bold);
        color: var(--gray-600);
        border-radius: var(--border-radius);
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        font-size: 0.9rem;
    }

    .tab-btn i {
        font-size: 1rem;
    }

    .tab-btn.active {
        background: var(--primary);
        color: white;
        border-color: var(--primary);
    }

    .tab-btn:hover:not(.active) {
        background: var(--gray-100);
        color: var(--gray-800);
    }

    /* Content Panels */
    .tab-content {
        display: none;
    }

    .tab-content.active {
        display: block;
    }

    /* Task Table */
    .task-table-container {
        background: white;
        border-radius: var(--border-radius-lg);
        box-shadow: var(--shadow);
        overflow: hidden;
    }

    .table-header {
        padding: var(--spacing-lg);
        background: var(--gray-100);
        border-bottom: 1px solid var(--gray-200);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .table-title {
        font-weight: var(--font-weight-bold);
        color: var(--gray-900);
        margin: 0;
        font-size: 1.1rem;
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
    }

    .table-title i {
        color: var(--primary);
        font-size: 1rem;
    }

    .table-filters {
        display: flex;
        gap: var(--spacing-sm);
        align-items: center;
    }

    .filter-select {
        padding: var(--spacing-xs) var(--spacing-sm);
        border: 1px solid var(--gray-300);
        border-radius: var(--border-radius);
        font-size: 0.85rem;
        background: white;
        transition: all var(--transition-fast);
    }

    .filter-select:focus {
        outline: none;
        border-color: var(--primary);
        box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
    }

    /* Enhanced Table Styles */
    .table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
    }

    .table th {
        background-color: var(--gray-100);
        color: var(--gray-800);
        font-weight: var(--font-weight-bold);
        padding: var(--spacing-md) var(--spacing-md);
        border-bottom: 2px solid var(--gray-300);
        text-align: left;
        vertical-align: middle;
        position: sticky;
        top: 0;
        z-index: 10;
    }

    .table td {
        padding: var(--spacing-md);
        vertical-align: middle;
        border-bottom: 1px solid var(--gray-200);
    }

    .table tbody tr:last-child td {
        border-bottom: none;
    }

    .table tbody tr:hover {
        background-color: rgba(67, 97, 238, 0.03);
    }

    /* Task Status Badges */
    .status-badge {
        display: inline-block;
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--border-radius);
        font-size: 0.75rem;
        font-weight: var(--font-weight-bold);
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .status-pending {
        background-color: var(--warning);
        color: var(--gray-900);
    }
    .status-started {
        background-color: var(--secondary);
        color: var(--gray-900);
    }
    .status-success {
        background-color: var(--success);
        color: white;
    }
    .status-failure {
        background-color: var(--danger);
        color: white;
    }
    .status-revoked {
        background-color: var(--gray-500);
        color: white;
    }

    /* Action Buttons */
    .action-buttons {
        display: flex;
        gap: var(--spacing-xs);
    }

    .action-btn {
        padding: var(--spacing-xs) var(--spacing-sm);
        border: 1px solid var(--gray-300);
        border-radius: var(--border-radius);
        cursor: pointer;
        font-size: 0.75rem;
        font-weight: var(--font-weight-bold);
        transition: all var(--transition-fast);
        display: inline-flex;
        align-items: center;
        gap: var(--spacing-xs);
        background: white;
        color: var(--gray-700);
    }

    .action-btn:hover {
        background: var(--gray-100);
        transform: translateY(-1px);
    }

    .action-btn.revoke {
        background: var(--danger);
        color: white;
        border-color: var(--danger);
    }

    .action-btn.revoke:hover {
        background: #d32f2f;
    }

    .action-btn.details {
        background: linear-gradient(135deg, var(--primary), var(--primary-dark));
        color: white;
        box-shadow: 0 4px 12px rgba(67, 97, 238, 0.3);
    }

    .action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
    }

    .action-btn.revoke:hover {
        box-shadow: 0 8px 20px rgba(244, 67, 54, 0.4);
    }

    .action-btn.details:hover {
        box-shadow: 0 8px 20px rgba(67, 97, 238, 0.4);
    }

    /* Loading States */
    .loading {
        text-align: center;
        padding: var(--spacing-xl);
        color: var(--gray-600);
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-lg);
    }

    .loading-spinner {
        width: 60px;
        height: 60px;
        border: 4px solid var(--gray-200);
        border-top: 4px solid var(--primary);
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: var(--spacing-md);
    }

    .loading-text {
        font-size: 1.1rem;
        font-weight: var(--font-weight-medium);
        color: var(--gray-600);
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* Empty State */
    .empty-state {
        text-align: center;
        padding: var(--spacing-xl);
        color: var(--gray-500);
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-lg);
    }

    .empty-state i {
        font-size: 4rem;
        color: var(--gray-300);
        margin-bottom: var(--spacing-md);
    }

    .empty-state-title {
        font-size: 1.3rem;
        font-weight: var(--font-weight-bold);
        color: var(--gray-600);
        margin-bottom: var(--spacing-sm);
    }

    .empty-state-text {
        font-size: 1rem;
        color: var(--gray-500);
        max-width: 400px;
        line-height: 1.5;
    }

    /* Responsive Design */
    @media (max-width: 1200px) {
        .stats-grid {
            grid-template-columns: repeat(2, 1fr);
        }
    }

    @media (max-width: 992px) {
        .task-monitor-container {
            padding: var(--spacing-md);
        }

        .tab-btn {
            padding: var(--spacing-sm) var(--spacing-md);
            font-size: 0.9rem;
        }

        .filter-select {
            min-width: 100px;
        }
    }

    @media (max-width: 768px) {
        .page-header {
            flex-direction: column;
            align-items: flex-start;
            gap: var(--spacing-md);
        }

        .refresh-controls {
            align-self: flex-end;
            margin-top: var(--spacing-md);
            flex-direction: column;
            gap: var(--spacing-sm);
        }

        .auto-refresh-container {
            flex-direction: column;
            align-items: flex-start;
            gap: var(--spacing-sm);
        }

        .interval-selector {
            border-left: none;
            border-top: 1px solid var(--gray-300);
            padding-left: 0;
            padding-top: var(--spacing-sm);
            margin-left: 0;
        }

        .monitor-tabs {
            flex-wrap: wrap;
        }

        .stats-grid {
            grid-template-columns: 1fr;
        }

        .page-description {
            margin-left: 0;
        }
    }

    @media (max-width: 480px) {
        .refresh-controls {
            width: 100%;
        }

        .auto-refresh-container {
            width: 100%;
        }

        .interval-selector select {
            min-width: 70px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="task-monitor-container">
    <div class="page-header">
        <div class="header-content">
            <h2>
                <i class="fas fa-tasks"></i>
                Task Monitor
            </h2>
            <p class="page-description">Monitor and manage Celery task execution</p>
        </div>
        <div class="refresh-controls">
            <div class="auto-refresh-container active" id="autoRefreshContainer">
                <div class="auto-refresh-toggle">
                    <div class="toggle-switch active" id="toggleSwitch" onclick="toggleAutoRefresh()"></div>
                    <label onclick="toggleAutoRefresh()">
                        <i class="fas fa-clock"></i>
                        Auto Refresh
                    </label>
                    <input type="checkbox" id="autoRefresh" checked style="display: none;">
                </div>
                <div class="interval-selector">
                    <span class="interval-label">Every</span>
                    <select id="refreshInterval" onchange="updateRefreshInterval()">
                        <option value="30">30s</option>
                        <option value="60">1m</option>
                        <option value="120">2m</option>
                        <option value="180">3m</option>
                        <option value="300">5m</option>
                    </select>
                </div>
            </div>
            <div class="refresh-btn">
                <button onclick="refreshData()">
                    <i class="fas fa-sync-alt"></i> Refresh Data
                </button>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-icon total">
                <i class="fas fa-list"></i>
            </div>
            <div class="stat-content">
                <div class="stat-value" id="totalTasks">0</div>
                <div class="stat-label">Total Tasks</div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon workers">
                <i class="fas fa-server"></i>
            </div>
            <div class="stat-content">
                <div class="stat-value" id="onlineWorkers">0</div>
                <div class="stat-label">Online Workers</div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon active">
                <i class="fas fa-play"></i>
            </div>
            <div class="stat-content">
                <div class="stat-value" id="activeTasks">0</div>
                <div class="stat-label">Active Tasks</div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon failed">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="stat-content">
                <div class="stat-value" id="failedTasks">0</div>
                <div class="stat-label">Failed Tasks</div>
            </div>
        </div>
    </div>

<!-- Tabs -->
<div class="monitor-tabs">
    <button class="tab-btn active" onclick="switchTab('tasks')">
        <i class="fas fa-list"></i>
        <span>Tasks</span>
    </button>
    <button class="tab-btn" onclick="switchTab('workers')">
        <i class="fas fa-server"></i>
        <span>Workers</span>
    </button>
</div>

<!-- Tasks Tab -->
<div id="tasksTab" class="tab-content active">
    <div class="task-table-container">
        <div class="table-header">
            <h3 class="table-title">
                <i class="fas fa-tasks"></i>
                Active Tasks
            </h3>
            <div class="table-filters">
                <select class="filter-select" id="stateFilter" onchange="filterTasks()">
                    <option value="">All States</option>
                    <option value="PENDING">Pending</option>
                    <option value="STARTED">Started</option>
                    <option value="SUCCESS">Success</option>
                    <option value="FAILURE">Failure</option>
                    <option value="REVOKED">Revoked</option>
                </select>
            </div>
        </div>
        <div id="tasksTableContainer">
            <div class="loading">
                <div class="loading-spinner"></div>
                <div class="loading-text">Loading tasks...</div>
            </div>
        </div>
    </div>
</div>

<!-- Workers Tab -->
<div id="workersTab" class="tab-content">
    <div class="task-table-container">
        <div class="table-header">
            <h3 class="table-title">
                <i class="fas fa-server"></i>
                Workers Status
            </h3>
        </div>
        <div id="workersTableContainer">
            <div class="loading">
                <div class="loading-spinner"></div>
                <div class="loading-text">Loading workers...</div>
            </div>
        </div>
    </div>
</div>
</div>

<script src="/static/js/task_monitor.js"></script>
{% endblock %}
