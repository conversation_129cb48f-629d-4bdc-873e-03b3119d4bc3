{% extends "base.html" %}
{% block title %}Dashboard{% endblock %}

{% block content %}
<div class="dashboard-container">
    <div class="dashboard-header">
        <h1 class="dashboard-title">Dashboard</h1>
        <div class="dashboard-controls">
            <button id="theme-toggle" class="theme-toggle" title="Toggle dark/light mode">
                <i class="fas fa-moon"></i>
            </button>
            <div class="date-display">
                <i class="far fa-calendar-alt"></i>
                <span id="current-date"></span>
            </div>
        </div>
    </div>

    <!-- Summary Cards Row -->
    <div class="dashboard-summary">
        <!-- Files Uploaded Card -->
        <div class="summary-card gradient-blue">
            <div class="summary-icon"><i class="fas fa-file-upload"></i></div>
            <div class="summary-content">
                <div class="summary-header">
                    <h2>{{ summary.files_uploaded }}</h2>
                    {% if summary.today is defined and summary.today > 0 %}
                    <div class="trend-indicator up">
                        <i class="fas fa-arrow-up"></i>
                        <span>{{ summary.today }}</span>
                    </div>
                    {% else %}
                    <div class="trend-indicator neutral">
                        <i class="fas fa-minus"></i>
                        <span>0</span>
                    </div>
                    {% endif %}
                </div>
                <p>Files Uploaded</p>
            </div>
        </div>

        <!-- Pending Approvals Card -->
        <div class="summary-card gradient-orange">
            <div class="summary-icon"><i class="fas fa-hourglass-half"></i></div>
            <div class="summary-content">
                <div class="summary-header">
                    <h2>{{ summary.pending_approvals }}</h2>
                    <div class="trend-indicator {% if summary.pending_approvals > 5 %}warning{% else %}neutral{% endif %}">
                        <i class="fas {% if summary.pending_approvals > 5 %}fa-exclamation-triangle{% else %}fa-check{% endif %}"></i>
                    </div>
                </div>
                <p>Pending Approvals</p>
            </div>
        </div>

        <!-- Total Requests Card -->
        <div class="summary-card gradient-green">
            <div class="summary-icon"><i class="fas fa-tasks"></i></div>
            <div class="summary-content">
                <div class="summary-header">
                    <h2>{{ summary.requests }}</h2>
                    <div class="trend-indicator neutral">
                        <i class="fas fa-chart-line"></i>
                    </div>
                </div>
                <p>Total Requests</p>
            </div>
        </div>

        <!-- Suppliers Card -->
        <div class="summary-card gradient-purple">
            <div class="summary-icon"><i class="fas fa-industry"></i></div>
            <div class="summary-content">
                <div class="summary-header">
                    <h2>{{ summary.suppliers }}</h2>
                    <div class="trend-indicator neutral">
                        <i class="fas fa-database"></i>
                    </div>
                </div>
                <p>Suppliers</p>
            </div>
        </div>
    </div>

    <!-- Main Content Grid -->
    <div class="dashboard-grid">
        <!-- Activity Chart Card -->
        <div class="dashboard-card activity-chart-card">
            <div class="card-header">
                <h3><i class="fas fa-chart-line"></i> Activity Overview</h3>
                <div class="card-actions">
                    <button class="btn-icon" id="refresh-chart" title="Refresh data">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                    <div class="chart-period-selector">
                        <button class="period-btn active" data-period="week">Week</button>
                        <button class="period-btn" data-period="month">Month</button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <canvas id="activityChart" height="100"></canvas>
            </div>
        </div>

        <!-- Two Column Layout for Second Row -->
        <div class="dashboard-row">
            <!-- Status Chart Card -->
            <div class="dashboard-card status-chart-card">
                <div class="card-header">
                    <h3><i class="fas fa-chart-pie"></i> Status Distribution</h3>
                </div>
                <div class="card-body">
                    <div class="status-chart-container">
                        <canvas id="statusChart" height="180"></canvas>
                    </div>
                    <div class="status-legend">
                        <div class="status-legend-item">
                            <span class="status-dot completed"></span>
                            <span>Completed ({{ summary.completed|default(0) }})</span>
                        </div>
                        <div class="status-legend-item">
                            <span class="status-dot pending"></span>
                            <span>Pending ({{ summary.pending|default(0) }})</span>
                        </div>
                        <div class="status-legend-item">
                            <span class="status-dot other"></span>
                            <span>Other ({{ summary.requests - (summary.completed|default(0)) - (summary.pending|default(0)) }})</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions Card -->
            <div class="dashboard-card quick-actions-card">
                <div class="card-header">
                    <h3><i class="fas fa-bolt"></i> Quick Actions</h3>
                </div>
                <div class="card-body">
                    <div class="quick-actions-grid">
                        <a href="/" class="quick-action-btn">
                            <i class="fas fa-file-upload"></i>
                            <span>Upload Data</span>
                        </a>
                        <a href="/view_requests" class="quick-action-btn">
                            <i class="fas fa-list"></i>
                            <span>View Requests</span>
                        </a>
                        <a href="/approval" class="quick-action-btn">
                            <i class="fas fa-check-circle"></i>
                            <span>Approvals</span>
                        </a>
                        <a href="/api/files" class="quick-action-btn">
                            <i class="fas fa-folder-open"></i>
                            <span>Files</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity Card -->
        <div class="dashboard-card recent-activity-card">
            <div class="card-header">
                <h3><i class="fas fa-history"></i> Recent Activity</h3>
                <div class="card-actions">
                    <button class="btn-icon" id="refresh-activity" title="Refresh activity">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                {% if recent_activity %}
                    <ul class="activity-list">
                        {% for activity in recent_activity %}
                            <li class="activity-item">
                                <div class="activity-icon">
                                    <i class="fas fa-file-alt"></i>
                                </div>
                                <div class="activity-content">
                                    <p>{{ activity }}</p>
                                    <span class="activity-time">{{ loop.index0 * 2 + 1 }} hour{% if loop.index0 > 0 %}s{% endif %} ago</span>
                                </div>
                            </li>
                        {% endfor %}
                    </ul>
                {% else %}
                    <div class="empty-state">
                        <i class="far fa-calendar-times"></i>
                        <p>No recent activity</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Insights Section -->
    <div class="dashboard-insights">
        <div class="insights-header">
            <h3><i class="fas fa-lightbulb"></i> Insights & Recommendations</h3>
        </div>
        <div class="insights-body">
            <div class="insight-card warning">
                <div class="insight-icon warning">
                    <i class="fas fa-exclamation-circle"></i>
                </div>
                <div class="insight-content">
                    <h4>Pending Approvals</h4>
                    <p>You have {{ summary.pending_approvals }} pending approval{% if summary.pending_approvals != 1 %}s{% endif %} that require attention.</p>
                    <a href="/approval" class="insight-action">Review Now</a>
                </div>
            </div>
            <div class="insight-card info">
                <div class="insight-icon info">
                    <i class="fas fa-info-circle"></i>
                </div>
                <div class="insight-content">
                    <h4>Upload Activity</h4>
                    <p>{{ summary.today|default(0) }} new file{% if summary.today|default(0) != 1 %}s{% endif %} uploaded today. {{ summary.completed|default(0) }} request{% if summary.completed|default(0) != 1 %}s{% endif %} completed successfully.</p>
                </div>
            </div>
            <div class="insight-card success">
                <div class="insight-icon success">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="insight-content">
                    <h4>System Status</h4>
                    <p>All systems operational. Data processing is running efficiently.</p>
                    <a href="/api" class="insight-action">View Details</a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_head %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<style>
/* Dashboard Layout */
.dashboard-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1.5rem 1rem 2.5rem;
    font-family: var(--font-family);
}

/* Dashboard Header */
.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.dashboard-title {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--gray-900);
    margin: 0;
    position: relative;
}

.dashboard-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 40px;
    height: 3px;
    background: var(--primary);
    border-radius: 2px;
}

.dashboard-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.theme-toggle {
    background: none;
    border: none;
    color: var(--gray-600);
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
}

.theme-toggle:hover {
    background: var(--gray-100);
    color: var(--primary);
}

.date-display {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: var(--gray-100);
    border-radius: 20px;
    font-size: 0.9rem;
    color: var(--gray-700);
}

/* Summary Cards */
.dashboard-summary {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1.2rem;
    margin-bottom: 1.5rem;
}

.summary-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    border-radius: 12px;
    padding: 1.2rem;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transition: all 0.3s;
    position: relative;
    overflow: hidden;
    color: white;
}

.summary-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0) 100%);
    z-index: 1;
}

.summary-card::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
    z-index: 1;
}

.summary-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 24px rgba(0,0,0,0.15);
}

.summary-icon {
    font-size: 1.8rem;
    color: #fff;
    background: rgba(255,255,255,0.2);
    border-radius: 12px;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    z-index: 2;
}

.summary-content {
    flex: 1;
    z-index: 2;
}

.summary-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.3rem;
}

.summary-card h2 {
    margin: 0;
    font-size: 1.8rem;
    color: #fff;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.summary-card p {
    margin: 0;
    color: rgba(255,255,255,0.9);
    font-weight: 500;
    font-size: 0.9rem;
}

.trend-indicator {
    display: flex;
    align-items: center;
    gap: 0.3rem;
    font-size: 0.8rem;
    font-weight: 600;
    padding: 0.3rem 0.5rem;
    border-radius: 12px;
    background: rgba(255,255,255,0.2);
}

.trend-indicator.up {
    color: #e6ffea;
    background: rgba(76, 175, 80, 0.3);
}

.trend-indicator.down {
    color: #ffe6e6;
    background: rgba(244, 67, 54, 0.3);
}

.trend-indicator.warning {
    color: #fff8e1;
    background: rgba(255, 152, 0, 0.3);
}

.trend-indicator.neutral {
    color: rgba(255,255,255,0.9);
}

/* Gradient Backgrounds */
.gradient-blue {
    background: linear-gradient(135deg, #4361ee 0%, #4895ef 100%);
    box-shadow: 0 4px 20px rgba(67, 97, 238, 0.3);
    border: 1px solid rgba(67, 97, 238, 0.1);
}

.gradient-orange {
    background: linear-gradient(135deg, #f9a826 0%, #ffbb4c 100%);
    box-shadow: 0 4px 20px rgba(249, 168, 38, 0.3);
    border: 1px solid rgba(249, 168, 38, 0.1);
}

.gradient-green {
    background: linear-gradient(135deg, #2ecc71 0%, #4cd97b 100%);
    box-shadow: 0 4px 20px rgba(46, 204, 113, 0.3);
    border: 1px solid rgba(46, 204, 113, 0.1);
}

.gradient-purple {
    background: linear-gradient(135deg, #8e44ad 0%, #a55eea 100%);
    box-shadow: 0 4px 20px rgba(142, 68, 173, 0.3);
    border: 1px solid rgba(142, 68, 173, 0.1);
}

/* Dashboard Grid Layout */
.dashboard-grid {
    display: flex;
    flex-direction: column;
    gap: 1.2rem;
    margin-bottom: 1.5rem;
}

.dashboard-row {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.2rem;
}

.dashboard-card {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.05);
    overflow: hidden;
    transition: all 0.3s;
    border: 1px solid rgba(0,0,0,0.05);
}

.dashboard-card:hover {
    box-shadow: 0 8px 24px rgba(0,0,0,0.08);
    border-color: rgba(67, 97, 238, 0.1);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.2rem;
    border-bottom: 1px solid var(--gray-200);
    background: linear-gradient(to right, rgba(249, 250, 251, 0.5), rgba(255, 255, 255, 0.5));
}

.card-header h3 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--gray-800);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.card-header h3 i {
    color: var(--primary);
    font-size: 1.2rem;
}

.card-actions {
    display: flex;
    align-items: center;
    gap: 0.8rem;
}

.btn-icon {
    background: none;
    border: none;
    color: var(--gray-600);
    font-size: 0.9rem;
    cursor: pointer;
    padding: 0.4rem;
    border-radius: 6px;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-icon:hover {
    background: rgba(67, 97, 238, 0.1);
    color: var(--primary);
}

.card-body {
    padding: 1.2rem;
    background: white;
}

/* Activity Chart Card */
.activity-chart-card {
    grid-column: span 2;
}

.chart-period-selector {
    display: flex;
    align-items: center;
    background: var(--gray-100);
    border-radius: 6px;
    padding: 0.2rem;
}

.period-btn {
    background: none;
    border: none;
    padding: 0.3rem 0.8rem;
    font-size: 0.8rem;
    font-weight: 600;
    color: var(--gray-600);
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.2s;
}

.period-btn.active {
    background: #fff;
    color: var(--primary);
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

/* Status Chart */
.status-chart-container {
    display: flex;
    justify-content: center;
    margin-bottom: 1rem;
}

.status-legend {
    display: flex;
    justify-content: center;
    gap: 1.2rem;
    flex-wrap: wrap;
}

.status-legend-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: var(--gray-700);
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.status-dot.completed {
    background: #2ecc71;
}

.status-dot.pending {
    background: #f9a826;
}

.status-dot.other {
    background: #95a5a6;
}

/* Quick Actions */
.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.8rem;
}

.quick-action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 1.2rem 0.8rem;
    background: var(--gray-100);
    border-radius: 10px;
    text-decoration: none;
    color: var(--gray-700);
    transition: all 0.3s;
    text-align: center;
    border: 1px solid rgba(0,0,0,0.05);
    box-shadow: 0 2px 8px rgba(0,0,0,0.03);
    position: relative;
    overflow: hidden;
}

.quick-action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(67, 97, 238, 0.05) 0%, rgba(255,255,255,0) 60%);
    z-index: 1;
}

.quick-action-btn i {
    font-size: 1.8rem;
    color: var(--primary);
    transition: all 0.3s;
    position: relative;
    z-index: 2;
}

.quick-action-btn span {
    font-size: 0.9rem;
    font-weight: 600;
    position: relative;
    z-index: 2;
}

.quick-action-btn:hover {
    background: var(--primary-light);
    color: var(--primary-dark);
    transform: translateY(-3px);
    box-shadow: 0 6px 16px rgba(67, 97, 238, 0.15);
    border-color: rgba(67, 97, 238, 0.1);
}

.quick-action-btn:hover i {
    transform: scale(1.1);
    color: var(--primary-dark);
}

/* Recent Activity */
.activity-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: 0.8rem;
    padding: 0.8rem 0;
    border-bottom: 1px solid var(--gray-200);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    background: var(--primary-light);
    color: var(--primary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}

.activity-content {
    flex: 1;
}

.activity-content p {
    margin: 0 0 0.3rem;
    font-size: 0.9rem;
    color: var(--gray-800);
    line-height: 1.4;
}

.activity-time {
    font-size: 0.8rem;
    color: var(--gray-500);
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem 0;
    color: var(--gray-500);
}

.empty-state i {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state p {
    margin: 0;
    font-size: 0.9rem;
}

/* Insights Section */
.dashboard-insights {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.05);
    overflow: hidden;
    border: 1px solid rgba(0,0,0,0.05);
}

.insights-header {
    padding: 1.2rem 1.5rem;
    border-bottom: 1px solid var(--gray-200);
    background: linear-gradient(to right, rgba(249, 250, 251, 0.8), rgba(255, 255, 255, 0.8));
}

.insights-header h3 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--gray-800);
    display: flex;
    align-items: center;
    gap: 0.7rem;
}

.insights-header h3 i {
    color: #f9a826;
    font-size: 1.3rem;
}

.insights-body {
    padding: 1.5rem;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.2rem;
}

.insight-card {
    display: flex;
    gap: 1rem;
    padding: 1.2rem;
    background: var(--gray-50);
    border-radius: 10px;
    transition: all 0.3s;
    border: 1px solid rgba(0,0,0,0.03);
    box-shadow: 0 2px 8px rgba(0,0,0,0.02);
    position: relative;
    overflow: hidden;
}

.insight-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    z-index: 1;
}

.insight-card.warning::before {
    background: #f9a826;
}

.insight-card.info::before {
    background: var(--primary);
}

.insight-card.success::before {
    background: #2ecc71;
}

.insight-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 16px rgba(0,0,0,0.06);
}

.insight-icon {
    width: 46px;
    height: 46px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.4rem;
    box-shadow: 0 4px 8px rgba(0,0,0,0.05);
}

.insight-icon.warning {
    background: rgba(249, 168, 38, 0.15);
    color: #f9a826;
}

.insight-icon.info {
    background: rgba(67, 97, 238, 0.15);
    color: var(--primary);
}

.insight-icon.success {
    background: rgba(46, 204, 113, 0.15);
    color: #2ecc71;
}

.insight-content {
    flex: 1;
}

.insight-content h4 {
    margin: 0 0 0.6rem;
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-800);
}

.insight-content p {
    margin: 0 0 0.8rem;
    font-size: 0.9rem;
    color: var(--gray-600);
    line-height: 1.5;
}

.insight-action {
    display: inline-flex;
    align-items: center;
    gap: 0.4rem;
    padding: 0.4rem 0.9rem;
    background: var(--primary-light);
    color: var(--primary);
    border-radius: 6px;
    font-size: 0.85rem;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s;
    box-shadow: 0 2px 4px rgba(67, 97, 238, 0.1);
}

.insight-action:hover {
    background: var(--primary);
    color: white;
    box-shadow: 0 4px 8px rgba(67, 97, 238, 0.2);
}

.insight-action::after {
    content: '→';
    font-size: 0.9rem;
    transition: transform 0.3s;
    display: inline-block;
}

.insight-action:hover::after {
    transform: translateX(3px);
}

/* Dark Mode Styles */
body.dark-mode {
    background-color: #1a1a2e;
    color: #f1f1f1;
}

/* Dark Mode - Header */
body.dark-mode .dashboard-title {
    color: #f1f1f1;
}

body.dark-mode .theme-toggle {
    color: #f1f1f1;
}

body.dark-mode .theme-toggle:hover {
    background: #2a2a42;
}

body.dark-mode .date-display {
    background: #2a2a42;
    color: #f1f1f1;
}

/* Dark Mode - Cards and Containers */
body.dark-mode .dashboard-card,
body.dark-mode .dashboard-insights {
    background: #16213e;
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    border-color: #2a2a42;
}

body.dark-mode .card-header,
body.dark-mode .insights-header {
    border-bottom: 1px solid #2a2a42;
    background: linear-gradient(to right, rgba(26, 26, 46, 0.8), rgba(22, 33, 62, 0.8));
}

body.dark-mode .card-body {
    background: #16213e;
}

body.dark-mode .card-header h3,
body.dark-mode .insights-header h3 {
    color: #f1f1f1;
}

body.dark-mode .card-header h3 i,
body.dark-mode .insights-header h3 i {
    color: #4cc9f0;
}

/* Dark Mode - Summary Cards */
body.dark-mode .summary-card {
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    border-color: rgba(255,255,255,0.05);
}

body.dark-mode .summary-card.gradient-blue {
    background: linear-gradient(135deg, #1a237e 0%, #283593 100%);
    box-shadow: 0 4px 20px rgba(26, 35, 126, 0.4);
    border: 1px solid rgba(26, 35, 126, 0.2);
}

body.dark-mode .summary-card.gradient-orange {
    background: linear-gradient(135deg, #e65100 0%, #ef6c00 100%);
    box-shadow: 0 4px 20px rgba(230, 81, 0, 0.4);
    border: 1px solid rgba(230, 81, 0, 0.2);
}

body.dark-mode .summary-card.gradient-green {
    background: linear-gradient(135deg, #1b5e20 0%, #2e7d32 100%);
    box-shadow: 0 4px 20px rgba(27, 94, 32, 0.4);
    border: 1px solid rgba(27, 94, 32, 0.2);
}

body.dark-mode .summary-card.gradient-purple {
    background: linear-gradient(135deg, #4a148c 0%, #6a1b9a 100%);
    box-shadow: 0 4px 20px rgba(74, 20, 140, 0.4);
    border: 1px solid rgba(74, 20, 140, 0.2);
}

body.dark-mode .summary-card::before {
    background: linear-gradient(135deg, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0) 100%);
}

body.dark-mode .summary-icon {
    background: rgba(255,255,255,0.2);
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

body.dark-mode .trend-indicator {
    background: rgba(255,255,255,0.15);
}

/* Dark Mode - Buttons and Controls */
body.dark-mode .btn-icon {
    color: #f1f1f1;
}

body.dark-mode .btn-icon:hover {
    background: #2a2a42;
    color: #4cc9f0;
}

body.dark-mode .chart-period-selector {
    background: #2a2a42;
}

body.dark-mode .period-btn {
    color: #f1f1f1;
}

body.dark-mode .period-btn.active {
    background: #16213e;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    color: #4cc9f0;
}

/* Dark Mode - Charts */
body.dark-mode .status-legend-item {
    color: #f1f1f1;
}

body.dark-mode .status-dot {
    box-shadow: 0 0 5px rgba(0,0,0,0.3);
}

/* Dark Mode - Quick Actions */
body.dark-mode .quick-action-btn {
    background: #2a2a42;
    color: #f1f1f1;
    border-color: #3a3a62;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

body.dark-mode .quick-action-btn i {
    color: #4cc9f0;
}

body.dark-mode .quick-action-btn:hover {
    background: #3a3a72;
    border-color: #4cc9f0;
    box-shadow: 0 6px 16px rgba(0,0,0,0.3);
}

body.dark-mode .quick-action-btn:hover i {
    color: #4cc9f0;
}

/* Dark Mode - Activity List */
body.dark-mode .activity-item {
    border-bottom: 1px solid #2a2a42;
}

body.dark-mode .activity-icon {
    background: #2a2a42;
    color: #4cc9f0;
}

body.dark-mode .activity-content p {
    color: #f1f1f1;
}

body.dark-mode .activity-time {
    color: #a0a0a0;
}

body.dark-mode .empty-state {
    color: #a0a0a0;
}

/* Dark Mode - Insights */
body.dark-mode .insight-card {
    background: #2a2a42;
    border-color: #3a3a62;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

body.dark-mode .insight-card:hover {
    background: #3a3a72;
    box-shadow: 0 6px 16px rgba(0,0,0,0.3);
}

body.dark-mode .insight-icon {
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

body.dark-mode .insight-icon.warning {
    background: rgba(249, 168, 38, 0.2);
}

body.dark-mode .insight-icon.info {
    background: rgba(76, 201, 240, 0.2);
    color: #4cc9f0;
}

body.dark-mode .insight-icon.success {
    background: rgba(46, 204, 113, 0.2);
}

body.dark-mode .insight-content h4 {
    color: #f1f1f1;
}

body.dark-mode .insight-content p {
    color: #d0d0d0;
}

body.dark-mode .insight-action {
    background: rgba(76, 201, 240, 0.2);
    color: #4cc9f0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

body.dark-mode .insight-action:hover {
    background: #4cc9f0;
    color: #16213e;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .dashboard-summary {
        grid-template-columns: repeat(2, 1fr);
    }

    .dashboard-row {
        grid-template-columns: 1fr;
    }

    .activity-chart-card {
        grid-column: span 1;
    }

    .insights-body {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .dashboard-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .dashboard-controls {
        width: 100%;
        justify-content: space-between;
    }

    .quick-actions-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .status-legend {
        flex-direction: column;
        align-items: flex-start;
    }

    .card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.8rem;
    }

    .card-actions {
        width: 100%;
        justify-content: flex-end;
    }
}

@media (max-width: 576px) {
    .dashboard-summary {
        grid-template-columns: 1fr;
    }

    .quick-actions-grid {
        grid-template-columns: 1fr;
    }

    .activity-item {
        flex-direction: column;
        align-items: flex-start;
    }

    .activity-icon {
        margin-bottom: 0.5rem;
    }

    .insight-card {
        flex-direction: column;
    }

    .insight-icon {
        margin-bottom: 0.8rem;
    }
}
</style>
{% endblock %}

{% block extra_scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Set current date in the header
document.addEventListener('DOMContentLoaded', function() {
    // Display current date
    const currentDateElement = document.getElementById('current-date');
    const now = new Date();
    const options = { weekday: 'long', year: 'numeric', month: 'short', day: 'numeric' };
    currentDateElement.textContent = now.toLocaleDateString('en-US', options);

    // Initialize charts
    initActivityChart();
    initStatusChart();

    // Set up event listeners
    setupEventListeners();

    // Check for saved theme preference
    checkThemePreference();
});

// Initialize the activity chart
function initActivityChart() {
    try {
        // Weekly data (default view)
        const weeklyLabels = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"];
        const weeklyData = [12, 19, 7, 15, 10, 5, 8];

        // Monthly data
        const monthlyLabels = ["Week 1", "Week 2", "Week 3", "Week 4"];
        const monthlyData = [42, 38, 55, 29];

        const ctx = document.getElementById('activityChart');
        if (!ctx) {
            console.error('Activity chart canvas element not found');
            return;
        }

        // Check if Chart.js is loaded
        if (typeof Chart === 'undefined') {
            console.error('Chart.js is not loaded');
            return;
        }

        // Create gradient background
        const ctxObj = ctx.getContext('2d');
        const gradient = ctxObj.createLinearGradient(0, 0, 0, 300);
        gradient.addColorStop(0, 'rgba(67, 97, 238, 0.3)');
        gradient.addColorStop(1, 'rgba(67, 97, 238, 0.02)');

        window.activityChart = new Chart(ctxObj, {
            type: 'line',
            data: {
                labels: weeklyLabels,
                datasets: [{
                    label: 'Files Uploaded',
                    data: weeklyData,
                    backgroundColor: gradient,
                    borderColor: '#4361ee',
                    borderWidth: 3,
                    pointBackgroundColor: '#fff',
                    pointBorderColor: '#4361ee',
                    pointRadius: 5,
                    pointHoverRadius: 8,
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { display: false },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        backgroundColor: 'rgba(255, 255, 255, 0.9)',
                        titleColor: '#333',
                        bodyColor: '#666',
                        borderColor: '#ddd',
                        borderWidth: 1,
                        padding: 10,
                        cornerRadius: 8,
                        titleFont: {
                            weight: 'bold'
                        },
                        callbacks: {
                            label: function(context) {
                                return `Files: ${context.raw}`;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        grid: { display: false },
                        ticks: {
                            color: '#888',
                            font: { weight: 600 }
                        }
                    },
                    y: {
                        grid: {
                            color: 'rgba(0,0,0,0.05)'
                        },
                        beginAtZero: true,
                        ticks: {
                            color: '#888',
                            font: { weight: 600 },
                            precision: 0
                        }
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                elements: {
                    line: {
                        borderJoinStyle: 'round'
                    }
                }
            }
        });

        // Store data for period switching
        window.chartData = {
            weekly: {
                labels: weeklyLabels,
                data: weeklyData
            },
            monthly: {
                labels: monthlyLabels,
                data: monthlyData
            }
        };
    } catch (error) {
        console.error('Error initializing activity chart:', error);
    }
}

// Initialize the status distribution chart
function initStatusChart() {
    try {
        const statusCanvas = document.getElementById('statusChart');
        if (!statusCanvas) {
            console.error('Status chart canvas element not found');
            return;
        }

        const statusCtx = statusCanvas.getContext('2d');

        // Check if Chart.js is loaded
        if (typeof Chart === 'undefined') {
            console.error('Chart.js is not loaded');
            return;
        }

        // Get values from the summary data
        const completedCount = parseInt("{{ summary.completed|default(0) }}") || 0;
        const pendingCount = parseInt("{{ summary.pending|default(0) }}") || 0;
        const totalCount = parseInt("{{ summary.requests|default(0) }}") || 0;
        const otherCount = Math.max(0, totalCount - completedCount - pendingCount);

        // If all values are 0, show a placeholder chart
        const hasData = completedCount > 0 || pendingCount > 0 || otherCount > 0;

        const chartData = hasData ?
            [completedCount, pendingCount, otherCount] :
            [1, 1, 1]; // Placeholder data if no real data

        const chartColors = [
            '#2ecc71',  // Green for completed
            '#f9a826',  // Orange for pending
            '#95a5a6'   // Gray for other
        ];

        // Add subtle patterns to the chart segments
        const patternColors = chartColors.map(color => {
            const colorWithOpacity = color.replace(')', ', 0.8)').replace('rgb', 'rgba');
            return colorWithOpacity;
        });

        window.statusChart = new Chart(statusCtx, {
            type: 'doughnut',
            data: {
                labels: ['Completed', 'Pending', 'Other'],
                datasets: [{
                    data: chartData,
                    backgroundColor: patternColors,
                    borderColor: '#fff',
                    borderWidth: 2,
                    hoverOffset: 10,
                    hoverBorderWidth: 3,
                    hoverBorderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                cutout: '70%',
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        enabled: hasData, // Disable tooltips for placeholder data
                        backgroundColor: 'rgba(255, 255, 255, 0.9)',
                        titleColor: '#333',
                        bodyColor: '#666',
                        borderColor: '#ddd',
                        borderWidth: 1,
                        padding: 10,
                        cornerRadius: 8,
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw || 0;
                                const total = context.dataset.data.reduce((acc, val) => acc + val, 0);
                                const percentage = Math.round((value / total) * 100);
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                },
                animation: {
                    animateRotate: true,
                    animateScale: true,
                    duration: 1000,
                    easing: 'easeOutQuart'
                }
            }
        });

        // Add "No data" text if using placeholder data
        if (!hasData) {
            const width = statusCanvas.width;
            const height = statusCanvas.height;
            statusCtx.font = '14px Arial';
            statusCtx.fillStyle = '#888';
            statusCtx.textAlign = 'center';
            statusCtx.textBaseline = 'middle';
            statusCtx.fillText('No data available', width / 2, height / 2);
        }
    } catch (error) {
        console.error('Error initializing status chart:', error);
    }
}

// Set up event listeners for interactive elements
function setupEventListeners() {
    // Theme toggle
    const themeToggle = document.getElementById('theme-toggle');
    themeToggle.addEventListener('click', toggleTheme);

    // Chart period selector
    const periodButtons = document.querySelectorAll('.period-btn');
    periodButtons.forEach(button => {
        button.addEventListener('click', function() {
            const period = this.getAttribute('data-period');
            updateChartPeriod(period);

            // Update active state
            periodButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
        });
    });

    // Refresh buttons
    const refreshChartBtn = document.getElementById('refresh-chart');
    if (refreshChartBtn) {
        refreshChartBtn.addEventListener('click', function() {
            refreshChart();
            this.classList.add('rotating');
            setTimeout(() => {
                this.classList.remove('rotating');
            }, 1000);
        });
    }

    const refreshActivityBtn = document.getElementById('refresh-activity');
    if (refreshActivityBtn) {
        refreshActivityBtn.addEventListener('click', function() {
            // In a real app, this would fetch new activity data from the server
            this.classList.add('rotating');
            setTimeout(() => {
                this.classList.remove('rotating');
            }, 1000);
        });
    }
}

// Toggle between light and dark mode
function toggleTheme() {
    try {
        const body = document.body;
        const themeIcon = document.querySelector('.theme-toggle i');

        if (body.classList.contains('dark-mode')) {
            body.classList.remove('dark-mode');
            themeIcon.classList.remove('fa-sun');
            themeIcon.classList.add('fa-moon');
            localStorage.setItem('theme', 'light');
        } else {
            body.classList.add('dark-mode');
            themeIcon.classList.remove('fa-moon');
            themeIcon.classList.add('fa-sun');
            localStorage.setItem('theme', 'dark');
        }

        // Update charts for the new theme
        updateChartsForTheme();
    } catch (error) {
        console.error('Error toggling theme:', error);
    }
}

// Check for saved theme preference
function checkThemePreference() {
    try {
        const savedTheme = localStorage.getItem('theme');
        const themeIcon = document.querySelector('.theme-toggle i');

        if (savedTheme === 'dark') {
            document.body.classList.add('dark-mode');
            if (themeIcon) {
                themeIcon.classList.remove('fa-moon');
                themeIcon.classList.add('fa-sun');
            }

            // We need to wait for charts to initialize before updating them
            setTimeout(() => {
                updateChartsForTheme();
            }, 500);
        }
    } catch (error) {
        console.error('Error checking theme preference:', error);
    }
}

// Update chart colors based on theme
function updateChartsForTheme() {
    try {
        const isDarkMode = document.body.classList.contains('dark-mode');

        // Update activity chart if it exists
        if (window.activityChart) {
            // Update grid lines
            const gridColor = isDarkMode ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)';
            const tickColor = isDarkMode ? '#aaa' : '#888';

            window.activityChart.options.scales.x.grid.color = gridColor;
            window.activityChart.options.scales.y.grid.color = gridColor;

            // Update tick colors
            window.activityChart.options.scales.x.ticks.color = tickColor;
            window.activityChart.options.scales.y.ticks.color = tickColor;

            // Update tooltip styles
            window.activityChart.options.plugins.tooltip.backgroundColor = isDarkMode ? 'rgba(40, 44, 52, 0.9)' : 'rgba(255, 255, 255, 0.9)';
            window.activityChart.options.plugins.tooltip.titleColor = isDarkMode ? '#f1f1f1' : '#333';
            window.activityChart.options.plugins.tooltip.bodyColor = isDarkMode ? '#d0d0d0' : '#666';
            window.activityChart.options.plugins.tooltip.borderColor = isDarkMode ? '#3a3a62' : '#ddd';

            window.activityChart.update();
        }

        // Update status chart if it exists
        if (window.statusChart) {
            // Update tooltip styles
            window.statusChart.options.plugins.tooltip.backgroundColor = isDarkMode ? 'rgba(40, 44, 52, 0.9)' : 'rgba(255, 255, 255, 0.9)';
            window.statusChart.options.plugins.tooltip.titleColor = isDarkMode ? '#f1f1f1' : '#333';
            window.statusChart.options.plugins.tooltip.bodyColor = isDarkMode ? '#d0d0d0' : '#666';
            window.statusChart.options.plugins.tooltip.borderColor = isDarkMode ? '#3a3a62' : '#ddd';

            // Update border color
            window.statusChart.data.datasets[0].borderColor = isDarkMode ? '#2a2a42' : '#fff';

            window.statusChart.update();
        }
    } catch (error) {
        console.error('Error updating charts for theme change:', error);
    }
}

// Update chart data based on selected period
function updateChartPeriod(period) {
    if (!window.activityChart || !window.chartData) return;

    const chartData = window.chartData[period];
    if (!chartData) return;

    window.activityChart.data.labels = chartData.labels;
    window.activityChart.data.datasets[0].data = chartData.data;
    window.activityChart.update();
}

// Simulate refreshing chart data
function refreshChart() {
    if (!window.activityChart) return;

    // Generate random data for demonstration
    const newData = Array.from({length: window.activityChart.data.labels.length}, () =>
        Math.floor(Math.random() * 20) + 5
    );

    // Animate the transition to new data
    window.activityChart.data.datasets[0].data = newData;
    window.activityChart.update();
}

// Add rotating animation for refresh buttons
document.head.insertAdjacentHTML('beforeend', `
<style>
@keyframes rotating {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}
.rotating {
    animation: rotating 1s linear;
}
</style>
`);
</script>
{% endblock %}
