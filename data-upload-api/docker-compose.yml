services:
  app:
    build: .
    ports:
      - "8003:8000"
    command: fastapi run --host 0.0.0.0 --port 8000 --reload
    volumes:
      - .:/app
      - ./uploaded_files:/app/uploaded_files
    restart: unless-stopped

  celery:
    build: .
    command: celery -A app.tasks.worker.celery_app worker --loglevel=info -Q excel_queue
    volumes:
      - .:/app
      - ./uploaded_files:/app/uploaded_files
    restart: unless-stopped
