// Shared Modern Confirmation Dialog
// This file contains reusable code for the modern confirmation dialog
// that can be used across different pages in the application

// Define CSS variables if they don't exist in the page
function ensureCssVariables() {
    // Check if CSS variables are defined
    const style = getComputedStyle(document.documentElement);
    const primaryColor = style.getPropertyValue('--primary-color').trim();

    // If primary-color is not defined, add our own CSS variables
    if (!primaryColor) {
        const styleElement = document.createElement('style');
        styleElement.textContent = `
            :root {
                --primary-color: #4361ee;
                --primary-light: rgba(67, 97, 238, 0.1);
                --primary-dark: #3a56d4;
                --secondary-color: #6c5ce7;
                --secondary-light: rgba(108, 92, 231, 0.1);
                --success-color: #2ecc71;
                --success-light: rgba(46, 204, 113, 0.1);
                --warning-color: #f39c12;
                --warning-light: rgba(243, 156, 18, 0.1);
                --danger-color: #e74c3c;
                --danger-light: rgba(231, 76, 60, 0.1);
                --info-color: #3498db;
                --info-light: rgba(52, 152, 219, 0.1);
                --gray-100: #f8f9fa;
                --gray-200: #e9ecef;
                --gray-300: #dee2e6;
                --gray-400: #ced4da;
                --gray-500: #adb5bd;
                --gray-600: #6c757d;
                --gray-700: #495057;
                --gray-800: #343a40;
                --gray-900: #212529;
                --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
                --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                --shadow-md: 0 6px 12px rgba(0, 0, 0, 0.08);
                --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
                --border-radius-sm: 4px;
                --border-radius: 8px;
                --border-radius-lg: 12px;
                --border-radius-xl: 16px;
                --border-radius-pill: 50px;
                --transition-fast: 0.2s ease;
                --transition: 0.3s ease;
                --spacing-xs: 4px;
                --spacing-sm: 8px;
                --spacing-md: 16px;
                --spacing-lg: 24px;
                --spacing-xl: 32px;
                --font-weight-normal: 400;
                --font-weight-medium: 500;
                --font-weight-bold: 600;
                --font-weight-bolder: 700;
            }
        `;
        document.head.appendChild(styleElement);
    }
}

// Create and show a modern confirmation dialog
function showModernConfirmDialog(options) {
    // Ensure CSS variables are defined
    ensureCssVariables();

    // Default options
    const defaults = {
        title: 'Confirm Action',
        message: 'Are you sure you want to proceed with this action?',
        confirmText: 'Confirm',
        cancelText: 'Cancel',
        type: 'primary', // primary, success, danger
        requestId: '',
        onConfirm: () => {},
        onCancel: () => closeModernConfirmDialog()
    };

    // Merge defaults with provided options
    const settings = { ...defaults, ...options };

    // Determine colors based on type
    let headerBackground, buttonBackground, buttonShadow;
    let icon = 'fa-question-circle';

    switch (settings.type) {
        case 'success':
            headerBackground = 'linear-gradient(135deg, #34d399 0%, #2ecc71 100%)';
            buttonBackground = 'linear-gradient(to right, #2ecc71, #34d399)';
            buttonShadow = '0 4px 10px rgba(52, 211, 153, 0.3)';
            icon = 'fa-check-circle';
            break;
        case 'danger':
            headerBackground = 'linear-gradient(135deg, #f87171 0%, #e74c3c 100%)';
            buttonBackground = 'linear-gradient(to right, #e74c3c, #f87171)';
            buttonShadow = '0 4px 10px rgba(220, 53, 69, 0.3)';
            icon = 'fa-times-circle';
            break;
        default: // primary
            headerBackground = 'linear-gradient(135deg, rgba(67, 97, 238, 0.1) 0%, #4361ee 100%)';
            buttonBackground = 'linear-gradient(to right, #4361ee, #4895ef)';
            buttonShadow = '0 4px 10px rgba(67, 97, 238, 0.3)';
            icon = 'fa-info-circle';
    }

    // Create the dialog element
    const confirmDialog = document.createElement('div');
    confirmDialog.className = 'modern-confirm-dialog';
    confirmDialog.innerHTML = `
        <div class="modern-confirm-content">
            <div class="modern-confirm-header" style="background: ${headerBackground};">
                <i class="fas ${icon}"></i>
                <h3>${settings.title}</h3>
            </div>
            <div class="modern-confirm-body">
                ${settings.message}
                ${settings.requestId ? `<p>Request ID: <strong>${settings.requestId}</strong></p>` : ''}
            </div>
            <div class="modern-confirm-actions">
                <button class="modern-confirm-cancel" id="modern-confirm-cancel" style="background-color: #f1f5f9; color: #495057;">
                    <i class="fas fa-times"></i> ${settings.cancelText}
                </button>
                <button class="modern-confirm-proceed" id="modern-confirm-proceed"
                        style="background: ${buttonBackground}; box-shadow: ${buttonShadow}; color: white;">
                    <i class="fas ${icon}"></i> ${settings.confirmText}
                </button>
            </div>
        </div>
    `;

    // Add styles for the dialog if they don't exist yet
    if (!document.querySelector('#modern-confirm-dialog-styles')) {
        const style = document.createElement('style');
        style.id = 'modern-confirm-dialog-styles';
        style.textContent = `
            .modern-confirm-dialog {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.6);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 9999;
                animation: fadeIn 0.3s ease;
                backdrop-filter: blur(5px);
            }

            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }

            .modern-confirm-content {
                background-color: white;
                border-radius: 12px;
                width: 450px;
                max-width: 90%;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25);
                animation: slideIn 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
                overflow: hidden;
                border: 1px solid rgba(255, 255, 255, 0.1);
            }

            @keyframes slideIn {
                from { transform: translateY(-70px) scale(0.95); opacity: 0; }
                to { transform: translateY(0) scale(1); opacity: 1; }
            }

            .modern-confirm-header {
                padding: 25px 30px;
                color: white;
                display: flex;
                align-items: center;
                gap: 15px;
                position: relative;
                overflow: hidden;
            }

            .modern-confirm-header::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 70%);
            }

            .modern-confirm-header i {
                font-size: 32px;
                z-index: 1;
                text-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
                background: rgba(255, 255, 255, 0.2);
                width: 50px;
                height: 50px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
            }

            .modern-confirm-header h3 {
                margin: 0;
                font-size: 22px;
                font-weight: 600;
                z-index: 1;
                text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
                color: white;
            }

            .modern-confirm-body {
                padding: 30px;
                font-size: 16px;
                color: #495057;
                line-height: 1.6;
                background-color: #f9fafc;
                border-bottom: 1px solid #eef1f6;
                max-height: 400px;
                overflow-y: auto;
            }

            .modern-confirm-body p {
                margin: 0 0 10px 0;
            }

            .modern-confirm-body strong {
                font-weight: 600;
                color: #212529;
            }

            .approval-reasons-list {
                list-style: none;
                padding: 0;
                margin: 15px 0;
                background-color: #f1f5f9;
                border-radius: 8px;
                border-left: 3px solid #4361ee;
                overflow: hidden;
            }

            .approval-reasons-list li {
                padding: 10px 15px;
                border-bottom: 1px solid #e2e8f0;
                display: flex;
                align-items: center;
                font-size: 14px;
            }

            .approval-reasons-list li:last-child {
                border-bottom: none;
            }

            .approval-reasons-list li i {
                margin-right: 10px;
                font-size: 16px;
                color: #4361ee;
            }

            .approval-reasons-list li i.fa-building,
            .approval-reasons-list li i.fa-warehouse {
                color: #f39c12;
            }

            .approval-reasons-list li i.fa-percentage {
                color: #e74c3c;
            }

            .approval-reasons-list li i.fa-columns {
                color: #3498db;
            }

            .approval-reasons-list li i.fa-ruler {
                color: #f39c12;
            }

            /* Multi-scenario categorized reasons */
            .approval-reasons-list.categorized {
                border: none;
                background: none;
                padding: 0;
            }

            .approval-reasons-list.categorized li.category-header {
                margin-top: 10px;
                padding: 8px 12px;
                background-color: #f1f5f9;
                border-radius: 8px 8px 0 0;
                border-bottom: 1px solid #e2e8f0;
            }

            .approval-reasons-list.categorized li.category-header strong {
                display: flex;
                align-items: center;
            }

            .approval-reasons-list.categorized li.category-header strong i {
                margin-right: 8px;
            }

            .approval-reasons-list.categorized li.category-header + li {
                border-top: none;
                border-radius: 0;
            }

            .approval-reasons-list.categorized li:not(.category-header) {
                background-color: white;
                border-left: 1px solid #e2e8f0;
                border-right: 1px solid #e2e8f0;
            }

            .approval-reasons-list.categorized li:not(.category-header):last-of-type {
                border-radius: 0 0 8px 8px;
                border-bottom: 1px solid #e2e8f0;
            }

            .modern-confirm-actions {
                padding: 20px 30px;
                display: flex;
                justify-content: flex-end;
                gap: 15px;
                background-color: white;
            }

            .modern-confirm-cancel {
                padding: 12px 20px;
                background-color: #f1f5f9;
                border: none;
                border-radius: 8px;
                cursor: pointer;
                font-weight: 600;
                font-size: 14px;
                color: #495057;
                transition: all 0.2s;
                box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
                display: flex;
                align-items: center;
                gap: 5px;
            }

            .modern-confirm-cancel:hover {
                background-color: #e2e8f0;
                transform: translateY(-2px);
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            }

            .modern-confirm-proceed {
                padding: 12px 24px;
                color: white;
                border: none;
                border-radius: 8px;
                cursor: pointer;
                font-weight: 600;
                font-size: 14px;
                transition: all 0.2s;
                position: relative;
                overflow: hidden;
                display: flex;
                align-items: center;
                gap: 5px;
            }

            .modern-confirm-proceed::after {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg,
                    rgba(255, 255, 255, 0) 0%,
                    rgba(255, 255, 255, 0.3) 50%,
                    rgba(255, 255, 255, 0) 100%);
                transition: all 0.8s ease;
            }

            .modern-confirm-proceed:hover {
                transform: translateY(-2px);
                box-shadow: 0 6px 15px rgba(0, 0, 0, 0.4);
            }

            .modern-confirm-proceed:hover::after {
                left: 100%;
            }

            @keyframes fadeOut {
                from { opacity: 1; }
                to { opacity: 0; }
            }

            @keyframes scaleDown {
                from {
                    transform: scale(1);
                    opacity: 1;
                }
                to {
                    transform: scale(0.95);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    }

    // Add the dialog to the document
    document.body.appendChild(confirmDialog);

    // Add event listeners
    const cancelButton = document.getElementById('modern-confirm-cancel');
    const confirmButton = document.getElementById('modern-confirm-proceed');

    cancelButton.addEventListener('click', () => {
        closeModernConfirmDialog();
        if (settings.onCancel) settings.onCancel();
    });

    confirmButton.addEventListener('click', () => {
        closeModernConfirmDialog();
        if (settings.onConfirm) settings.onConfirm();
    });

    // Return the dialog element
    return confirmDialog;
}

// Close the modern confirmation dialog with animation
function closeModernConfirmDialog() {
    const dialog = document.querySelector('.modern-confirm-dialog');
    if (dialog) {
        // Add fade out animation
        dialog.style.animation = 'fadeOut 0.3s ease forwards';

        // Add scale down animation to the content
        const content = dialog.querySelector('.modern-confirm-content');
        if (content) {
            content.style.animation = 'scaleDown 0.3s ease forwards';
        }

        // Remove the dialog after animation completes
        setTimeout(() => {
            dialog.remove();
        }, 300);
    }
}

// Show a success or error toast notification
function showToastNotification(message, type = 'success') {
    // Create a modern toast notification
    const toast = document.createElement('div');
    toast.className = 'modern-toast';

    const icon = type === 'success' ? 'fa-check-circle' : 'fa-times-circle';
    const color = type === 'success' ? 'var(--success-color)' : 'var(--danger-color)';

    toast.innerHTML = `
        <div class="modern-toast-icon">
            <i class="fas ${icon}" style="color: ${color};"></i>
        </div>
        <div class="modern-toast-message">${message}</div>
    `;

    // Add styles for the toast if they don't exist yet
    if (!document.querySelector('#modern-toast-styles')) {
        const style = document.createElement('style');
        style.id = 'modern-toast-styles';
        style.textContent = `
            .modern-toast {
                position: fixed;
                top: 20px;
                right: 20px;
                background-color: white;
                border-radius: 8px;
                padding: 16px 20px;
                display: flex;
                align-items: center;
                gap: 12px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                z-index: 9999;
                animation: slideInRight 0.3s ease, fadeOut 0.5s ease 3s forwards;
                max-width: 300px;
            }

            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }

            .modern-toast-icon i {
                font-size: 24px;
            }

            .modern-toast-message {
                font-size: 16px;
                font-weight: 500;
                color: var(--gray-800);
            }
        `;
        document.head.appendChild(style);
    }

    document.body.appendChild(toast);

    // Remove the toast after animation completes
    setTimeout(() => {
        toast.remove();
    }, 3500);
}
