// Global variables
let pendingRequests = [];
let approvedRequests = [];
let rejectedRequests = [];
let currentRequestId = null;
let currentAction = null;

// Document ready function
document.addEventListener('DOMContentLoaded', function() {
    // Initialize the page
    initializePage();

    // Add event listeners for tab switching
    document.querySelectorAll('.approval-tab').forEach(tab => {
        tab.addEventListener('click', function() {
            switchTab(this.getAttribute('data-tab'));
            // Reposition tooltips after tab switch (with a slight delay to allow rendering)
            setTimeout(positionTooltips, 100);
        });
    });

    // Add window resize event listener to reposition tooltips
    window.addEventListener('resize', function() {
        // Debounce the resize event
        if (this.resizeTimeout) clearTimeout(this.resizeTimeout);
        this.resizeTimeout = setTimeout(function() {
            positionTooltips();
        }, 200);
    });
});

// Initialize the page
function initializePage() {
    showLoader();

    // Load pending requests
    fetchPendingRequests()
        .then(() => {
            // Load approved requests
            return fetchApprovedRequests();
        })
        .then(() => {
            // Load rejected requests
            return fetchRejectedRequests();
        })
        .then(() => {
            // Update tab counters
            updateTabCounters();

            // Initialize tooltips
            setTimeout(initializeTooltips, 100);

            hideLoader();
        })
        .catch(error => {
            console.error('Error initializing page:', error);
            hideLoader();
            showNotification('Error loading data. Please try again.', 'error');
        });
}

// Update the count badges in tabs
function updateTabCounters() {
    // Update pending count
    document.querySelector('.approval-tab[data-tab="pending"] .count').textContent = pendingRequests.length;

    // Update approved count
    document.querySelector('.approval-tab[data-tab="approved"] .count').textContent = approvedRequests.length;

    // Update rejected count
    document.querySelector('.approval-tab[data-tab="rejected"] .count').textContent = rejectedRequests.length;
}

// Refresh data
function refreshData() {
    initializePage();
}

// Switch between tabs with animation
function switchTab(tabName) {
    // Update active tab
    document.querySelectorAll('.approval-tab').forEach(tab => {
        if (tab.getAttribute('data-tab') === tabName) {
            tab.classList.add('active');
        } else {
            tab.classList.remove('active');
        }
    });

    // Hide all tab content first
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
        content.style.display = 'none';
    });

    // Show the selected tab content with animation
    const selectedTab = document.getElementById(`${tabName}-tab`);
    selectedTab.style.display = 'block';

    // Trigger reflow to ensure animation works
    void selectedTab.offsetWidth;

    // Add active class to start animation
    setTimeout(() => {
        selectedTab.classList.add('active');
    }, 10);
}

// Fetch pending requests
async function fetchPendingRequests() {
    try {
        const response = await fetch('/api/requests/pending/');
        if (!response.ok) {
            throw new Error('Failed to fetch pending requests');
        }

        const data = await response.json();
        pendingRequests = data;

        // Render pending requests
        renderPendingRequests();
    } catch (error) {
        console.error('Error fetching pending requests:', error);
        showNotification('Error fetching pending requests', 'error');
    }
}

// Fetch approved requests
async function fetchApprovedRequests() {
    try {
        const response = await fetch('/api/requests/approved/');
        if (!response.ok) {
            throw new Error('Failed to fetch approved requests');
        }

        const data = await response.json();
        approvedRequests = data;

        // Render approved requests
        renderApprovedRequests();
    } catch (error) {
        console.error('Error fetching approved requests:', error);
        showNotification('Error fetching approved requests', 'error');
    }
}

// Fetch rejected requests
async function fetchRejectedRequests() {
    try {
        const response = await fetch('/api/requests/rejected/');
        if (!response.ok) {
            throw new Error('Failed to fetch rejected requests');
        }

        const data = await response.json();
        rejectedRequests = data;

        // Render rejected requests
        renderRejectedRequests();
    } catch (error) {
        console.error('Error fetching rejected requests:', error);
        showNotification('Error fetching rejected requests', 'error');
    }
}

// Render pending requests
function renderPendingRequests() {
    const table = $('#pending-table');
    const tableBody = document.getElementById('pending-requests-body');
    const emptyState = document.getElementById('pending-empty');

    
    // If DataTable already initialized, destroy it before re-rendering rows
    if ($.fn.DataTable.isDataTable(table)) {
        table.DataTable().clear().destroy();
    }

    // Clear existing rows
    tableBody.innerHTML = '';

    if (pendingRequests.length === 0) {
        // Show empty state
        emptyState.style.display = 'flex';
        return;
    }

    // Hide empty state
    emptyState.style.display = 'none';

    // Add rows for each pending request
    pendingRequests.forEach(request => {
        const row = document.createElement('tr');

        // Format date
        const createdDate = new Date(request.created_date || Date.now()).toLocaleString();

        // Get validation process details if available
        let validationDetails = '';
        if (request.validation_report) {
            const report = request.validation_report;
            // Use a data attribute to mark this row for tooltip positioning
            validationDetails = `
                <div class="validation-tooltip tooltip-bottom">
                    <strong>Validation Details</strong>
                    <div class="validation-details">
                        <div data-label="Input:">${report.InputDataCount || 0}</div>
                        <div data-label="New:">${report.NewDataCount || 0}</div>
                        <div data-label="Exists:">${report.ExistsDataCount || 0}</div>
                        <div data-label="Error:">${report.ErrorDataCount || 0}</div>
                    </div>
                </div>
            `;
        }

        // Get approval reasons if available
        let approvalReasonsHtml = '';
        if (request.approval_reasons && request.approval_reasons.length > 0) {
            const reasonsCount = request.approval_reasons.length;
            let reasonsList = '';

            // Check if this is a multi-scenario case
            if (request.is_multi_scenario) {
                // Create a categorized view for multi-scenario cases
                const categories = request.approval_categories || {};
                const categoryLabels = {
                    'supplier': 'Supplier Issues',
                    'catalog': 'Catalog ID Issues',
                    'property': 'Property Mapping Issues',
                    'warehouse': 'Warehouse Issues',
                    'unit': 'Unit Issues'
                };

                const categoryIcons = {
                    'supplier': 'fa-building',
                    'catalog': 'fa-percentage',
                    'property': 'fa-columns',
                    'warehouse': 'fa-warehouse',
                    'unit': 'fa-ruler'
                };

                // Create a section for each category that has reasons
                for (const [category, reasons] of Object.entries(categories)) {
                    if (reasons && reasons.length > 0) {
                        const categoryLabel = categoryLabels[category] || 'Other Issues';
                        const categoryIcon = categoryIcons[category] || 'fa-info-circle';

                        reasonsList += `
                            <div class="approval-reason-category">
                                <div class="category-header ${category}-category">
                                    <i class="fas ${categoryIcon}"></i> ${categoryLabel} (${reasons.length})
                                </div>
                                <div class="category-reasons">
                        `;

                        // Add each reason in this category
                        reasons.forEach(reason => {
                            reasonsList += `
                                <div class="approval-reason ${category}-reason">
                                    <i class="fas ${categoryIcon}"></i> ${reason}
                                </div>
                            `;
                        });

                        reasonsList += `
                                </div>
                            </div>
                        `;
                    }
                }

                approvalReasonsHtml = `
                    <div class="approval-reasons multi-scenario">
                        <div class="approval-reasons-toggle" onclick="toggleApprovalReasons(this, event)">
                            <i class="fas fa-exclamation-triangle"></i> Multi-scenario approval (${reasonsCount} reasons)
                        </div>
                        <div class="approval-reasons-container">
                            ${reasonsList}
                        </div>
                    </div>
                `;
            } else {
                // Single scenario case - use the original approach
                reasonsList = request.approval_reasons.map(reason => {
                    let reasonClass = 'approval-reason';
                    let icon = 'fa-info-circle';

                    // Determine reason type based on content
                    if (reason.includes('New supplier') || reason.includes('supplier has no existing data')) {
                        reasonClass += ' supplier-reason';
                        icon = 'fa-building';
                    } else if (reason.includes('catalog ID match') || reason.includes('match percentage')) {
                        reasonClass += ' catalog-reason';
                        icon = 'fa-percentage';
                    } else if (reason.includes('Unmapped property') || reason.includes('unmapped columns')) {
                        reasonClass += ' property-reason';
                        icon = 'fa-columns';
                    } else if (reason.includes('Invalid warehouse')) {
                        reasonClass += ' warehouse-reason';
                        icon = 'fa-warehouse';
                    } else if (reason.includes('Invalid unit') || reason.includes('invalid units')) {
                        reasonClass += ' unit-reason';
                        icon = 'fa-ruler';
                    }

                    return `<div class="${reasonClass}"><i class="fas ${icon}"></i> ${reason}</div>`;
                }).join('');

                approvalReasonsHtml = `
                    <div class="approval-reasons">
                        <div class="approval-reasons-toggle" onclick="toggleApprovalReasons(this, event)">
                            <i class="fas fa-exclamation-triangle"></i> ${reasonsCount} approval ${reasonsCount > 1 ? 'reasons' : 'reason'}
                        </div>
                        <div class="approval-reasons-container">
                            ${reasonsList}
                        </div>
                    </div>
                `;
            }
        } else {
            // If no approval reasons are available, fetch them from logs
            approvalReasonsHtml = `
                <div class="approval-reasons">
                    <div class="approval-reasons-toggle" onclick="fetchApprovalReasonsForRequest(${request.request_id}, this, event)">
                        <i class="fas fa-sync-alt"></i> Load approval reasons
                    </div>
                    <div class="approval-reasons-container"></div>
                </div>
            `;
        }

        // Get file name with title attribute for hover
        const fileName = request.input_file_name || 'N/A';
        const fileNameCell = `<td title="${fileName}">${fileName}</td>`;

        row.innerHTML = `
            <td>
                <input type="checkbox" class="row-checkbox" data-id="${String(request.request_id).replace(/<[^>]*>?/gm, '')}" />
            </td>
            <td><strong>${request.request_id}</strong></td>
            ${fileNameCell}
            <td>${request.request_type || 'N/A'}</td>
            <td>${createStatusBadge(request.status)}</td>
            <td>
                ${validationDetails ? `
                <span class="reason-badge" title="Validation details">
                    <i class="fas fa-info-circle"></i> Validation Details
                    ${validationDetails}
                </span>` : ''}
                ${approvalReasonsHtml}
            </td>
            <td>${createdDate}</td>
            <td class="action-buttons">
                <button class="view-btn" onclick="viewRequest(${request.request_id})">
                    <i class="fas fa-eye"></i> View
                </button>
                <button class="approve-btn" onclick="approveRequest(${request.request_id})">
                    <i class="fas fa-check"></i> Approve
                </button>
                <button class="reject-btn" onclick="rejectRequest(${request.request_id})">
                    <i class="fas fa-times"></i> Reject
                </button>
            </td>
        `;

        tableBody.appendChild(row);
    });

    // Now initialize DataTable
    table.DataTable({
        paging: true,      // Enable pagination
        searching: true,   // Enable search box
        ordering: true,    // Enable column sorting
        info: true,        // Show info (e.g. "Showing 1 to 10 of 50 entries")
        lengthChange: false, // Disable changing number of rows shown (optional)
        columnDefs: [
            { orderable: false, targets: [0, 6, 7] } // Disable ordering on checkboxes, reason & action buttons columns
        ]
    });

}

// Render approved requests
function renderApprovedRequests() {
    const table = $('#approved-table');
    const tableBody = document.getElementById('approved-requests-body');
    const emptyState = document.getElementById('approved-empty');

        // If DataTable already initialized, destroy it before re-rendering rows
        if ($.fn.DataTable.isDataTable(table)) {
            table.DataTable().clear().destroy();
        }

    // Clear existing rows
    tableBody.innerHTML = '';

    if (approvedRequests.length === 0) {
        // Show empty state
        emptyState.style.display = 'flex';
        return;
    }

    // Hide empty state
    emptyState.style.display = 'none';

    // Add rows for each approved request
    approvedRequests.forEach(request => {
        const row = document.createElement('tr');

        // Format date
        const approvedDate = new Date(request.updated_date || Date.now()).toLocaleString();

        // Get validation process details if available
        let validationDetails = '';
        if (request.validation_report) {
            const report = request.validation_report;
            validationDetails = `
                <div class="validation-tooltip tooltip-bottom">
                    <strong>Validation Details</strong>
                    <div class="validation-details">
                        <div data-label="Input:">${report.InputDataCount || 0}</div>
                        <div data-label="New:">${report.NewDataCount || 0}</div>
                        <div data-label="Exists:">${report.ExistsDataCount || 0}</div>
                        <div data-label="Error:">${report.ErrorDataCount || 0}</div>
                    </div>
                </div>
            `;
        }

        // Get file name with title attribute for hover
        const fileName = request.input_file_name || 'N/A';
        const fileNameCell = `<td title="${fileName}">${fileName}</td>`;

        row.innerHTML = `
            <td><strong>${request.request_id}</strong></td>
            ${fileNameCell}
            <td>${request.request_type || 'N/A'}</td>
            <td>${createStatusBadge(request.status)}</td>
            <td>
                ${validationDetails ? `
                <span class="reason-badge reason-approved" title="Validation details">
                    <i class="fas fa-check-circle"></i> Validation Details
                    ${validationDetails}
                </span>` : ''}
            </td>
            <td>${approvedDate}</td>
            <td class="action-buttons">
                <button class="view-btn" onclick="viewRequest(${request.request_id})">
                    <i class="fas fa-eye"></i> View
                </button>
            </td>
        `;

        tableBody.appendChild(row);
    });
    // Now initialize DataTable
    table.DataTable({
        destroy: true,
        paging: true,
        searching: true,
        ordering: true,
        info: true,
        lengthChange: false,
        columnDefs: [
            { orderable: false, targets: [0, 4, 6] }
        ]
    });
}

// Render rejected requests
function renderRejectedRequests() {
    const table = $('#rejected-table');
    const tableBody = document.getElementById('rejected-requests-body');
    const emptyState = document.getElementById('rejected-empty');

    // If DataTable already initialized, destroy it before re-rendering rows
    if ($.fn.DataTable.isDataTable(table)) {
        table.DataTable().clear().destroy();
    }

    // Clear existing rows
    tableBody.innerHTML = '';

    if (rejectedRequests.length === 0) {
        // Show empty state
        emptyState.style.display = 'flex';
        return;
    }

    // Hide empty state
    emptyState.style.display = 'none';

    // Add rows for each rejected request
    rejectedRequests.forEach(request => {
        const row = document.createElement('tr');

        // Format date
        const rejectedDate = new Date(request.updated_date || Date.now()).toLocaleString();

        // Get validation process details if available
        let validationDetails = '';
        if (request.validation_report) {
            const report = request.validation_report;
            validationDetails = `
                <div class="validation-tooltip tooltip-bottom">
                    <strong>Validation Details</strong>
                    <div class="validation-details">
                        <div data-label="Input:">${report.InputDataCount || 0}</div>
                        <div data-label="New:">${report.NewDataCount || 0}</div>
                        <div data-label="Exists:">${report.ExistsDataCount || 0}</div>
                        <div data-label="Error:">${report.ErrorDataCount || 0}</div>
                    </div>
                </div>
            `;
        }

        // Get file name with title attribute for hover
        const fileName = request.input_file_name || 'N/A';
        const fileNameCell = `<td title="${fileName}">${fileName}</td>`;

        row.innerHTML = `
            <td><strong>${request.request_id}</strong></td>
            ${fileNameCell}
            <td>${request.request_type || 'N/A'}</td>
            <td>${createStatusBadge(request.status)}</td>
            <td>
                ${validationDetails ? `
                <span class="reason-badge reason-rejected" title="Validation details">
                    <i class="fas fa-times-circle"></i> Validation Details
                    ${validationDetails}
                </span>` : ''}
            </td>
            <td>${rejectedDate}</td>
            <td class="action-buttons">
                <button class="view-btn" onclick="viewRequest(${request.request_id})">
                    <i class="fas fa-eye"></i> View
                </button>
            </td>
        `;

        tableBody.appendChild(row);
    });
    // Now initialize DataTable
    table.DataTable({
        destroy: true,
        paging: true,
        searching: true,
        ordering: true,
        info: true,
        lengthChange: false,
        columnDefs: [
            { orderable: false, targets: [0, 4, 6] }
        ]
    });
}

// View request details
function viewRequest(requestId) {
    window.location.href = `/request_detail/${requestId}`;
}

// Toggle approval reasons visibility
function toggleApprovalReasons(element, event) {
    // Prevent the click from propagating to parent elements
    event.stopPropagation();

    // Find the container element
    const container = element.nextElementSibling;

    // Get all other approval reason containers
    const allContainers = document.querySelectorAll('.approval-reasons-container');

    // Close all other containers first
    allContainers.forEach(otherContainer => {
        if (otherContainer !== container && otherContainer.classList.contains('show')) {
            // Find the toggle element for this container
            const toggleElement = otherContainer.previousElementSibling;

            // Hide the container
            otherContainer.classList.remove('show');

            // Update the toggle text
            if (toggleElement) {
                const reasonsCount = otherContainer.children.length;
                if (toggleElement.querySelector('.fa-chevron-up')) {
                    toggleElement.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${reasonsCount} approval ${reasonsCount > 1 ? 'reasons' : 'reason'}`;
                }
            }
        }
    });

    // Toggle the 'show' class for the clicked container
    container.classList.toggle('show');

    // Update the toggle text
    if (container.classList.contains('show')) {
        element.innerHTML = `<i class="fas fa-chevron-up"></i> Hide reasons`;

        // Add a click handler to the document to close the container when clicking outside
        setTimeout(() => {
            const closeHandler = function(e) {
                // If the click is outside the container and the toggle element
                if (!container.contains(e.target) && !element.contains(e.target)) {
                    container.classList.remove('show');
                    element.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${container.children.length} approval ${container.children.length > 1 ? 'reasons' : 'reason'}`;
                    document.removeEventListener('click', closeHandler);
                }
            };

            document.addEventListener('click', closeHandler);
        }, 10);
    } else {
        const reasonsCount = container.children.length;
        element.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${reasonsCount} approval ${reasonsCount > 1 ? 'reasons' : 'reason'}`;
    }
}

// Fetch approval reasons for a specific request
async function fetchApprovalReasonsForRequest(requestId, element, event) {
    // Prevent the click from propagating to parent elements
    event.stopPropagation();

    // Show loading indicator
    element.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';

    try {
        // Fetch logs for this request
        const response = await fetch(`/api/get_log_by_id/${requestId}`);
        if (!response.ok) {
            throw new Error('Failed to fetch logs');
        }

        const data = await response.json();
        const logs = data.data || [];

        // Filter logs for approval reasons
        const approvalLogs = logs.filter(log =>
            log.LogType === 'INFO' &&
            (log.Logs.includes('Approval reason:') || log.Logs.includes('Approval required for the following reasons:'))
        );

        // Extract reasons from logs
        const reasons = [];
        const categories = {
            supplier: [],
            catalog: [],
            property: [],
            warehouse: [],
            unit: []
        };

        approvalLogs.forEach(log => {
            if (log.Logs.includes('Approval reason:')) {
                const reason = log.Logs.split('Approval reason:')[1].trim();
                if (reason) {
                    reasons.push(reason);

                    // Categorize the reason
                    if (reason.includes('New supplier') || reason.includes('supplier has no existing data')) {
                        categories.supplier.push(reason);
                    } else if (reason.includes('catalog ID match') || reason.includes('match percentage')) {
                        categories.catalog.push(reason);
                    } else if (reason.includes('Unmapped property') || reason.includes('unmapped columns')) {
                        categories.property.push(reason);
                    } else if (reason.includes('Invalid warehouse')) {
                        categories.warehouse.push(reason);
                    } else if (reason.includes('Invalid unit') || reason.includes('invalid units')) {
                        categories.unit.push(reason);
                    }
                }
            } else if (log.Logs.includes('Approval required for the following reasons:')) {
                const reasonsText = log.Logs.split('Approval required for the following reasons:')[1].trim();
                reasonsText.split(';').forEach(reason => {
                    const cleanReason = reason.trim();
                    if (cleanReason) {
                        reasons.push(cleanReason);

                        // Categorize the reason
                        if (cleanReason.includes('New supplier') || cleanReason.includes('supplier has no existing data')) {
                            categories.supplier.push(cleanReason);
                        } else if (cleanReason.includes('catalog ID match') || cleanReason.includes('match percentage')) {
                            categories.catalog.push(cleanReason);
                        } else if (cleanReason.includes('Unmapped property') || cleanReason.includes('unmapped columns')) {
                            categories.property.push(cleanReason);
                        } else if (cleanReason.includes('Invalid warehouse')) {
                            categories.warehouse.push(cleanReason);
                        } else if (cleanReason.includes('Invalid unit') || cleanReason.includes('invalid units')) {
                            categories.unit.push(cleanReason);
                        }
                    }
                });
            }
        });

        // Remove duplicates from each category
        for (const category in categories) {
            categories[category] = [...new Set(categories[category])];
        }

        // Remove duplicates from the main list
        const uniqueReasons = [...new Set(reasons)];

        // Check if this is a multi-scenario case
        const activeCategories = Object.values(categories).filter(cat => cat.length > 0);
        const isMultiScenario = activeCategories.length > 1;

        // Find the container element
        const container = element.nextElementSibling;

        if (uniqueReasons.length > 0) {
            // Update the toggle text
            element.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${uniqueReasons.length} approval ${uniqueReasons.length > 1 ? 'reasons' : 'reason'}`;

            let reasonsHTML = '';

            if (isMultiScenario) {
                // Multi-scenario case
                const categoryLabels = {
                    'supplier': 'Supplier Issues',
                    'catalog': 'Catalog ID Issues',
                    'property': 'Property Mapping Issues',
                    'warehouse': 'Warehouse Issues',
                    'unit': 'Unit Issues'
                };

                const categoryIcons = {
                    'supplier': 'fa-building',
                    'catalog': 'fa-percentage',
                    'property': 'fa-columns',
                    'warehouse': 'fa-warehouse',
                    'unit': 'fa-ruler'
                };

                // Create a section for each category that has reasons
                for (const [category, categoryReasons] of Object.entries(categories)) {
                    if (categoryReasons.length > 0) {
                        const categoryLabel = categoryLabels[category] || 'Other Issues';
                        const categoryIcon = categoryIcons[category] || 'fa-info-circle';

                        reasonsHTML += `
                            <div class="approval-reason-category">
                                <div class="category-header ${category}-category">
                                    <i class="fas ${categoryIcon}"></i> ${categoryLabel} (${categoryReasons.length})
                                </div>
                                <div class="category-reasons">
                        `;

                        // Add each reason in this category
                        categoryReasons.forEach(reason => {
                            reasonsHTML += `
                                <div class="approval-reason ${category}-reason">
                                    <i class="fas ${categoryIcon}"></i> ${reason}
                                </div>
                            `;
                        });

                        reasonsHTML += `
                                </div>
                            </div>
                        `;
                    }
                }
            } else {
                // Single scenario case
                uniqueReasons.forEach(reason => {
                    let reasonClass = 'approval-reason';
                    let icon = 'fa-info-circle';

                    // Determine reason type based on content
                    if (reason.includes('New supplier') || reason.includes('supplier has no existing data')) {
                        reasonClass += ' supplier-reason';
                        icon = 'fa-building';
                    } else if (reason.includes('catalog ID match') || reason.includes('match percentage')) {
                        reasonClass += ' catalog-reason';
                        icon = 'fa-percentage';
                    } else if (reason.includes('Unmapped property') || reason.includes('unmapped columns')) {
                        reasonClass += ' property-reason';
                        icon = 'fa-columns';
                    } else if (reason.includes('Invalid warehouse')) {
                        reasonClass += ' warehouse-reason';
                        icon = 'fa-warehouse';
                    } else if (reason.includes('Invalid unit') || reason.includes('invalid units')) {
                        reasonClass += ' unit-reason';
                        icon = 'fa-ruler';
                    }

                    reasonsHTML += `
                        <div class="${reasonClass}">
                            <i class="fas ${icon}"></i> ${reason}
                        </div>
                    `;
                });
            }

            // Update the container with the reasons
            container.innerHTML = reasonsHTML;

            // Show the container
            container.classList.add('show');

            // Add a click handler to the document to close the container when clicking outside
            setTimeout(() => {
                const closeHandler = function(e) {
                    // If the click is outside the container and the toggle element
                    if (!container.contains(e.target) && !element.contains(e.target)) {
                        container.classList.remove('show');
                        element.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${uniqueReasons.length} approval ${uniqueReasons.length > 1 ? 'reasons' : 'reason'}`;
                        document.removeEventListener('click', closeHandler);
                    }
                };

                document.addEventListener('click', closeHandler);
            }, 10);

            // Store the reasons and categories for use in approval/rejection dialogs
            const requestRow = element.closest('tr');
            if (requestRow) {
                const approveBtn = requestRow.querySelector('.approve-btn');
                const rejectBtn = requestRow.querySelector('.reject-btn');

                if (approveBtn) {
                    approveBtn.onclick = () => approveRequest(requestId, uniqueReasons, isMultiScenario, categories);
                }

                if (rejectBtn) {
                    rejectBtn.onclick = () => rejectRequest(requestId, uniqueReasons, isMultiScenario, categories);
                }
            }
        } else {
            // No reasons found
            element.innerHTML = '<i class="fas fa-info-circle"></i> No approval reasons found';
            container.innerHTML = '<div class="approval-reason">No specific approval reasons found in logs.</div>';
        }
    } catch (error) {
        console.error('Error fetching approval reasons:', error);
        element.innerHTML = '<i class="fas fa-exclamation-circle"></i> Error loading reasons';
        element.nextElementSibling.innerHTML = '<div class="approval-reason">Failed to load approval reasons. Please try again.</div>';
    }
}

// Approve request
function approveRequest(requestId, approvalReasons = [], isMultiScenario = false, approvalCategories = null) {
    // Find the request to get full details
    let request = null;
    for (const req of pendingRequests) {
        if (req.request_id == requestId) {
            request = req;
            break;
        }
    }

    // If we found the request, use its properties
    if (request) {
        approvalReasons = request.approval_reasons || approvalReasons;
        isMultiScenario = request.is_multi_scenario || isMultiScenario;
        approvalCategories = request.approval_categories || approvalCategories;
    }

    // Create reasons list HTML if there are reasons
    let reasonsHtml = '';
    if (approvalReasons && approvalReasons.length > 0) {
        // Check if this is a multi-scenario case and we have categories
        if (isMultiScenario && approvalCategories) {
            const categoryLabels = {
                'supplier': 'Supplier Issues',
                'catalog': 'Catalog ID Issues',
                'property': 'Property Mapping Issues',
                'warehouse': 'Warehouse Issues',
                'unit': 'Unit Issues'
            };

            const categoryIcons = {
                'supplier': 'fa-building',
                'catalog': 'fa-percentage',
                'property': 'fa-columns',
                'warehouse': 'fa-warehouse',
                'unit': 'fa-ruler'
            };

            let categorizedReasons = '';

            // Create a section for each category that has reasons
            for (const [category, reasons] of Object.entries(approvalCategories)) {
                if (reasons && reasons.length > 0) {
                    const categoryLabel = categoryLabels[category] || 'Other Issues';
                    const categoryIcon = categoryIcons[category] || 'fa-info-circle';

                    categorizedReasons += `
                        <li class="category-header">
                            <strong><i class="fas ${categoryIcon}"></i> ${categoryLabel} (${reasons.length})</strong>
                        </li>
                    `;

                    // Add each reason in this category
                    reasons.forEach(reason => {
                        categorizedReasons += `
                            <li><i class="fas ${categoryIcon}"></i> ${reason}</li>
                        `;
                    });
                }
            }

            reasonsHtml = `
                <p>This request was flagged for <strong>multiple types of issues</strong>:</p>
                <ul class="approval-reasons-list categorized">
                    ${categorizedReasons}
                </ul>
            `;
        } else {
            // Single scenario or uncategorized - use the original approach
            const reasonsList = approvalReasons.map(reason => {
                let icon = 'fa-info-circle';

                // Determine icon based on reason content
                if (reason.includes('New supplier') || reason.includes('supplier has no existing data')) {
                    icon = 'fa-building';
                } else if (reason.includes('catalog ID match') || reason.includes('match percentage')) {
                    icon = 'fa-percentage';
                } else if (reason.includes('Unmapped property') || reason.includes('unmapped columns')) {
                    icon = 'fa-columns';
                } else if (reason.includes('Invalid warehouse')) {
                    icon = 'fa-warehouse';
                } else if (reason.includes('Invalid unit') || reason.includes('invalid units')) {
                    icon = 'fa-ruler';
                }

                return `<li><i class="fas ${icon}"></i> ${reason}</li>`;
            }).join('');

            reasonsHtml = `
                <p>This request was flagged for the following reasons:</p>
                <ul class="approval-reasons-list">
                    ${reasonsList}
                </ul>
            `;
        }
    }

    // Use the shared modern confirmation dialog
    showModernConfirmDialog({
        title: 'Confirm Approval',
        message: `
        <p>You are about to <strong>approve</strong> this request.</p>
        ${reasonsHtml}
        <p>This action will allow the data to be processed and cannot be undone.</p>
        `,
        requestId: requestId.toString(),
        confirmText: 'Yes, Approve',
        type: 'success',
        onConfirm: () => processRequestAction(requestId, true)
    });
}

// Reject request
function rejectRequest(requestId, approvalReasons = [], isMultiScenario = false, approvalCategories = null) {
    // Find the request to get full details
    let request = null;
    for (const req of pendingRequests) {
        if (req.request_id == requestId) {
            request = req;
            break;
        }
    }

    // If we found the request, use its properties
    if (request) {
        approvalReasons = request.approval_reasons || approvalReasons;
        isMultiScenario = request.is_multi_scenario || isMultiScenario;
        approvalCategories = request.approval_categories || approvalCategories;
    }

    // Create reasons list HTML if there are reasons
    let reasonsHtml = '';
    if (approvalReasons && approvalReasons.length > 0) {
        // Check if this is a multi-scenario case and we have categories
        if (isMultiScenario && approvalCategories) {
            const categoryLabels = {
                'supplier': 'Supplier Issues',
                'catalog': 'Catalog ID Issues',
                'property': 'Property Mapping Issues',
                'warehouse': 'Warehouse Issues',
                'unit': 'Unit Issues'
            };

            const categoryIcons = {
                'supplier': 'fa-building',
                'catalog': 'fa-percentage',
                'property': 'fa-columns',
                'warehouse': 'fa-warehouse',
                'unit': 'fa-ruler'
            };

            let categorizedReasons = '';

            // Create a section for each category that has reasons
            for (const [category, reasons] of Object.entries(approvalCategories)) {
                if (reasons && reasons.length > 0) {
                    const categoryLabel = categoryLabels[category] || 'Other Issues';
                    const categoryIcon = categoryIcons[category] || 'fa-info-circle';

                    categorizedReasons += `
                        <li class="category-header">
                            <strong><i class="fas ${categoryIcon}"></i> ${categoryLabel} (${reasons.length})</strong>
                        </li>
                    `;

                    // Add each reason in this category
                    reasons.forEach(reason => {
                        categorizedReasons += `
                            <li><i class="fas ${categoryIcon}"></i> ${reason}</li>
                        `;
                    });
                }
            }

            reasonsHtml = `
                <p>This request was flagged for <strong>multiple types of issues</strong>:</p>
                <ul class="approval-reasons-list categorized">
                    ${categorizedReasons}
                </ul>
            `;
        } else {
            // Single scenario or uncategorized - use the original approach
            const reasonsList = approvalReasons.map(reason => {
                let icon = 'fa-info-circle';

                // Determine icon based on reason content
                if (reason.includes('New supplier') || reason.includes('supplier has no existing data')) {
                    icon = 'fa-building';
                } else if (reason.includes('catalog ID match') || reason.includes('match percentage')) {
                    icon = 'fa-percentage';
                } else if (reason.includes('Unmapped property') || reason.includes('unmapped columns')) {
                    icon = 'fa-columns';
                } else if (reason.includes('Invalid warehouse')) {
                    icon = 'fa-warehouse';
                } else if (reason.includes('Invalid unit') || reason.includes('invalid units')) {
                    icon = 'fa-ruler';
                }

                return `<li><i class="fas ${icon}"></i> ${reason}</li>`;
            }).join('');

            reasonsHtml = `
                <p>This request was flagged for the following reasons:</p>
                <ul class="approval-reasons-list">
                    ${reasonsList}
                </ul>
            `;
        }
    }

    // Use the shared modern confirmation dialog
    showModernConfirmDialog({
        title: 'Confirm Rejection',
        message: `
        <p>You are about to <strong>reject</strong> this request.</p>
        ${reasonsHtml}
        <p>This action will prevent the data from being processed and cannot be undone.</p>
        `,
        requestId: requestId.toString(),
        confirmText: 'Yes, Reject',
        type: 'danger',
        onConfirm: () => processRequestAction(requestId, false)
    });
}

// Process the request action (approve or reject)
async function processRequestAction(requestId, isApproved) {
    // Show loader
    showLoader();

    try {
        // Call API to update request status
        const response = await fetch(`/api/requests/${requestId}/status`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                approved: isApproved
            })
        });

        if (!response.ok) {
            throw new Error(`Failed to ${isApproved ? 'approve' : 'reject'} request ${requestId}`);
        }

        await response.json(); // Parse response but we don't need to use it

        // Show success notification
        const message = `Request ${requestId} has been ${isApproved ? 'approved' : 'rejected'} successfully`;
        showNotification(message, 'success');

        // Refresh data
        initializePage();
    } catch (error) {
        console.error(`Error ${isApproved ? 'approving' : 'rejecting'} request ${requestId}:`, error);
        hideLoader();
        // Show error notification
        const errorMessage = `Error ${isApproved ? 'approving' : 'rejecting'} request. Please try again.`;
        showNotification(errorMessage, 'error');
    }
}

// Show notification with enhanced animation
function showNotification(message, type) {
    const notification = document.getElementById('notification');
    const notificationMessage = document.getElementById('notification-message');

    // Set message and type
    notificationMessage.textContent = message;
    notification.className = `notification ${type}`;

    // Clear any existing timeout
    if (notification.hideTimeout) {
        clearTimeout(notification.hideTimeout);
    }

    // Show notification
    notification.classList.add('show');

    // Hide notification after 4 seconds
    notification.hideTimeout = setTimeout(() => {
        notification.classList.remove('show');
    }, 4000);
}

// Show loader with fade effect
function showLoader() {
    const loader = document.querySelector('.loader-overlay');
    // First add the visible class to enable flex centering
    loader.classList.add('visible');
    // Then fade in for smooth appearance
    $(loader).fadeIn(300);
}

// Hide loader with fade effect
function hideLoader() {
    const loader = document.querySelector('.loader-overlay');
    // Fade out first
    $(loader).fadeOut(300, function() {
        // Remove visible class after fade out is complete
        loader.classList.remove('visible');
    });
}

// Initialize tooltips and hover effects for reason badges
function initializeTooltips() {
    // Position tooltips based on their position in the table
    positionTooltips();

    // Instead of click handlers, we'll use hover effects only
    // This prevents UI issues when clicking on the badges
    document.querySelectorAll('.reason-badge').forEach(badge => {
        const tooltip = badge.querySelector('.validation-tooltip');
        if (tooltip) {
            // Set initial state
            tooltip.style.visibility = 'hidden';
            tooltip.style.opacity = '0';

            // Show tooltip on mouseenter
            badge.addEventListener('mouseenter', function() {
                // Hide all other tooltips first
                document.querySelectorAll('.validation-tooltip').forEach(t => {
                    if (t !== tooltip) {
                        t.style.visibility = 'hidden';
                        t.style.opacity = '0';
                    }
                });

                // Show this tooltip
                tooltip.style.visibility = 'visible';
                tooltip.style.opacity = '1';
            });

            // Hide tooltip on mouseleave
            badge.addEventListener('mouseleave', function() {
                tooltip.style.visibility = 'hidden';
                tooltip.style.opacity = '0';
            });
        }
    });
}

// Position tooltips based on their position in the table
function positionTooltips() {
    // Get all reason badges
    const badges = document.querySelectorAll('.reason-badge');

    badges.forEach(badge => {
        const tooltip = badge.querySelector('.validation-tooltip');
        if (!tooltip) return;

        // Get the badge's position relative to the viewport
        const badgeRect = badge.getBoundingClientRect();
        const tableContainer = badge.closest('.approval-container');
        const tableContainerRect = tableContainer.getBoundingClientRect();

        // Calculate distance from badge to bottom of table container
        const distanceToBottom = tableContainerRect.bottom - badgeRect.bottom;

        // If badge is close to the bottom of the table, position tooltip above
        if (distanceToBottom < 150) { // 150px threshold
            tooltip.classList.remove('tooltip-bottom');
            tooltip.classList.add('tooltip-top');
        } else {
            tooltip.classList.remove('tooltip-top');
            tooltip.classList.add('tooltip-bottom');
        }
    });
}

function toggleProcessButton() {
    const hasSelected = $('.row-checkbox:checked').length > 0;
    $('#processSelectedApproveBtn').toggle(hasSelected);
    $('#processSelectedRejectBtn').toggle(hasSelected);
}

function getSelectedRequestIds() {
    const selectedIds = [];
    $('.row-checkbox:checked').each(function () {
        selectedIds.push($(this).attr('data-id'));
    });
    return selectedIds;
}

// Common handler for both buttons
async function handleRequestAction(actionType) {
    const requestIds = getSelectedRequestIds();

    if (requestIds.length === 0) {
        alert('Please select at least one request.');
        return;
    }

    const approved = actionType === 'approve';
    const baseUrl = window.location.origin;

    try {
        const updatePromises = requestIds.map(async requestId => {
            const response = await fetch(`${baseUrl}/api/requests/${requestId}/status`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ approved: approved })  // ✅ Send boolean only
            });

            const result = await response.json();

            if (!response.ok) {
                throw new Error(result?.detail || `Failed to ${approved ? 'approve' : 'reject'} request ${requestId}`);
            }

            return result;
        });

        // Wait for all updates to finish
        await Promise.all(updatePromises);

        // ✅ Show success message
        showPopupMessage(`Requests ${approved ? 'approved' : 'rejected'} successfully.`, 'success');

        // 🔁 Refresh after 5 seconds
        setTimeout(() => {
            window.location.reload();
        }, 5000);

    } catch (error) {
        console.error('Bulk update error:', error);
        showPopupMessage(`Error: ${error.message}`, 'error');

        // 🔁 Optionally refresh after 5 sec on error
        setTimeout(() => {
            window.location.reload();
        }, 5000);
    }
}

function showPopupMessage(message, type) {
    const popup = document.createElement('div');
    popup.textContent = message;
    popup.style.position = 'fixed';
    popup.style.top = '20px';
    popup.style.right = '20px';
    popup.style.zIndex = 1000;
    popup.style.padding = '12px 20px';
    popup.style.borderRadius = '8px';
    popup.style.color = '#fff';
    popup.style.fontWeight = 'bold';
    popup.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.2)';
    popup.style.backgroundColor = type === 'success' ? '#28a745' : '#dc3545';

    document.body.appendChild(popup);

    setTimeout(() => {
        popup.remove();
    }, 4000); // Popup disappears after 4 seconds
}

// Attach handler to both buttons
$('#processSelectedApproveBtn, #processSelectedRejectBtn').on('click', function () {
    const actionType = $(this).data('action'); // 'approve' or 'reject'
    handleRequestAction(actionType);
});

$(document).on('change', '.row-checkbox', function() {
    toggleProcessButton();
});

$('#selectAllCheckbox').on('change', function() {
    const isChecked = $(this).is(':checked');
    $('.row-checkbox').prop('checked', isChecked);
    toggleProcessButton();
});


function getSelectedRequestIds() {
    const selectedIds = [];
    $('.row-checkbox:checked').each(function() {
        selectedIds.push($(this).attr('data-id'));
    });
    return selectedIds;
}

$(document).ready(function() {
    $('#pending-table').DataTable({
        // optional config here, e.g.:
        paging: true,
        searching: true,
        ordering: true,
        info: true,
        lengthChange: false,
        pageLength: 10,
    });

    $('#approved-table').DataTable({
        paging: true,
        searching: true,
        ordering: true,
        info: true,
        lengthChange: false,
        pageLength: 10,
    });

    $('#rejected-table').DataTable({
        paging: true,
        searching: true,
        ordering: true,
        info: true,
        lengthChange: false,
        pageLength: 10,
    });
});
