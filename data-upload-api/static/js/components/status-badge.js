/**
 * Status Badge Component
 * 
 * This component provides a consistent way to display status badges across the application.
 * It includes proper styling and icons for different status types.
 */

/**
 * Creates a status badge HTML element with appropriate styling and icon
 * @param {string} status - The status text (Open, Start, Inprogress, Done, Error, Pending, Approved, Rejected, etc.)
 * @returns {string} HTML string for the status badge
 */
function createStatusBadge(status) {
    // Default values
    let statusClass = 'status-pending';
    let icon = 'fa-info-circle';
    
    // Normalize status to lowercase for comparison
    const statusLower = status.toLowerCase();
    
    // Determine appropriate class and icon based on status
    if (statusLower === 'open') {
        statusClass = 'status-open';
        icon = 'fa-folder-open';
    } else if (statusLower === 'start') {
        statusClass = 'status-start';
        icon = 'fa-play';
    } else if (statusLower === 'inprogress') {
        statusClass = 'status-inprogress';
        icon = 'fa-spinner';
    } else if (statusLower === 'done' || statusLower === 'completed') {
        statusClass = 'status-done';
        icon = 'fa-check';
    } else if (statusLower === 'error' || statusLower === 'failed') {
        statusClass = 'status-error';
        icon = 'fa-exclamation-triangle';
    } else if (statusLower === 'pending') {
        statusClass = 'status-pending';
        icon = 'fa-clock';
    } else if (statusLower === 'approved') {
        statusClass = 'status-approved';
        icon = 'fa-thumbs-up';
    } else if (statusLower === 'rejected') {
        statusClass = 'status-rejected';
        icon = 'fa-thumbs-down';
    }
    
    // Create the HTML for the status badge
    return `<span class="status-badge ${statusClass}"><i class="fas ${icon}"></i> ${status}</span>`;
}

// Make the function available globally
window.createStatusBadge = createStatusBadge;
