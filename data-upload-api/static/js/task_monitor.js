// Task Monitor JavaScript
let autoRefreshInterval;
let currentTab = 'tasks';
let currentFilter = '';
let currentRefreshInterval = 30; // Default 30 seconds

$(document).ready(function() {
    // Initialize the task monitor
    initializeTaskMonitor();
    
    // Set up auto-refresh
    setupAutoRefresh();
    
    // Load initial data
    refreshData();
});

function initializeTaskMonitor() {
    console.log('Task Monitor initialized');

    // Initialize toggle switch state
    updateToggleState(true);

    // Set up auto-refresh checkbox (hidden)
    $('#autoRefresh').change(function() {
        if (this.checked) {
            setupAutoRefresh();
        } else {
            clearInterval(autoRefreshInterval);
        }
    });
}

function toggleAutoRefresh() {
    const checkbox = $('#autoRefresh');
    const isCurrentlyChecked = checkbox.is(':checked');

    // Toggle the checkbox state
    checkbox.prop('checked', !isCurrentlyChecked);

    // Update visual state
    updateToggleState(!isCurrentlyChecked);

    // Handle auto-refresh logic
    if (!isCurrentlyChecked) {
        setupAutoRefresh();
    } else {
        clearInterval(autoRefreshInterval);
    }
}

function updateToggleState(isActive) {
    const container = $('#autoRefreshContainer');
    const toggleSwitch = $('#toggleSwitch');
    const intervalSelector = $('#refreshInterval');

    if (isActive) {
        container.addClass('active');
        toggleSwitch.addClass('active');
        intervalSelector.prop('disabled', false);
    } else {
        container.removeClass('active');
        toggleSwitch.removeClass('active');
        intervalSelector.prop('disabled', true);
    }
}

function updateRefreshInterval() {
    const newInterval = parseInt($('#refreshInterval').val());
    currentRefreshInterval = newInterval;

    // If auto-refresh is currently active, restart it with new interval
    if ($('#autoRefresh').is(':checked')) {
        setupAutoRefresh();
    }

    console.log(`Refresh interval updated to ${currentRefreshInterval} seconds`);
}

function formatIntervalText(seconds) {
    if (seconds < 60) {
        return `${seconds}s`;
    } else {
        const minutes = Math.floor(seconds / 60);
        return `${minutes}m`;
    }
}

function setupAutoRefresh() {
    // Clear existing interval
    if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
    }

    // Get current interval from selector
    currentRefreshInterval = parseInt($('#refreshInterval').val()) || 30;

    // Set up new interval with dynamic timing
    autoRefreshInterval = setInterval(function() {
        if ($('#autoRefresh').is(':checked')) {
            refreshData();
        }
    }, currentRefreshInterval * 1000);

    console.log(`Auto-refresh set to ${currentRefreshInterval} seconds`);
}

function refreshData() {
    console.log('Refreshing task monitor data...');
    showLoader();
    
    // Load statistics
    loadStatistics();
    
    // Load current tab data
    if (currentTab === 'tasks') {
        loadTasks();
    } else if (currentTab === 'workers') {
        loadWorkers();
    }
}

function loadStatistics() {
    $.ajax({
        url: '/api/monitor/stats',
        method: 'GET',
        success: function(data) {
            updateStatistics(data);
        },
        error: function(xhr, status, error) {
            console.error('Error loading statistics:', error);
            showError('Failed to load statistics');
        }
    });
}

function updateStatistics(data) {
    $('#totalTasks').text(data.tasks.total || 0);
    $('#onlineWorkers').text(data.workers.online || 0);
    $('#activeTasks').text(data.tasks.active || 0);
    $('#failedTasks').text(data.tasks.failed || 0);
}

function loadTasks() {
    const state = $('#stateFilter').val();
    const params = state ? `?state=${state}` : '';
    
    $.ajax({
        url: `/api/monitor/tasks${params}`,
        method: 'GET',
        success: function(data) {
            renderTasksTable(data.tasks);
            hideLoader();
        },
        error: function(xhr, status, error) {
            console.error('Error loading tasks:', error);
            showError('Failed to load tasks');
            hideLoader();
        }
    });
}

function renderTasksTable(tasks) {
    const container = $('#tasksTableContainer');

    if (!tasks || tasks.length === 0) {
        container.html(`
            <div class="empty-state">
                <i class="fas fa-inbox"></i>
                <div class="empty-state-title">No tasks found</div>
                <div class="empty-state-text">There are currently no tasks matching your criteria. Tasks will appear here when they are created.</div>
            </div>
        `);
        return;
    }

    let tableHtml = `
        <table class="table">
            <thead>
                <tr>
                    <th>Task ID</th>
                    <th>Name</th>
                    <th>State</th>
                    <th>Worker</th>
                    <th>Started</th>
                    <th>Runtime</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
    `;

    tasks.forEach(task => {
        const startedTime = task.started ? formatTimestamp(task.started) : '-';
        const runtime = task.runtime ? formatDuration(task.runtime) : '-';
        const taskName = task.name || 'Unknown';
        const worker = task.worker || '-';

        // Get status icon
        const statusIcon = getStatusIcon(task.state);

        tableHtml += `
            <tr>
                <td><code style="background: var(--gray-100); padding: 4px 8px; border-radius: 4px; font-size: 0.85rem;">${task.uuid.substring(0, 8)}...</code></td>
                <td><strong>${taskName}</strong></td>
                <td><span class="status-badge status-${task.state.toLowerCase()}">
                    <i class="${statusIcon}"></i>
                    ${task.state}
                </span></td>
                <td>${worker}</td>
                <td>${startedTime}</td>
                <td>${runtime}</td>
                <td>
                    <div class="action-buttons">
                        <button class="action-btn details" onclick="showTaskDetails('${task.uuid}')">
                            <i class="fas fa-info-circle"></i> Details
                        </button>
                        ${task.state === 'STARTED' || task.state === 'PENDING' ?
                            `<button class="action-btn revoke" onclick="revokeTask('${task.uuid}')">
                                <i class="fas fa-stop-circle"></i> Revoke
                            </button>` : ''
                        }
                    </div>
                </td>
            </tr>
        `;
    });

    tableHtml += `
            </tbody>
        </table>
    `;

    container.html(tableHtml);
}

function getStatusIcon(state) {
    const icons = {
        'PENDING': 'fas fa-clock',
        'STARTED': 'fas fa-play-circle',
        'SUCCESS': 'fas fa-check-circle',
        'FAILURE': 'fas fa-times-circle',
        'REVOKED': 'fas fa-ban'
    };
    return icons[state] || 'fas fa-question-circle';
}

function loadWorkers() {
    $.ajax({
        url: '/api/monitor/workers',
        method: 'GET',
        success: function(data) {
            renderWorkersTable(data.workers);
            hideLoader();
        },
        error: function(xhr, status, error) {
            console.error('Error loading workers:', error);
            showError('Failed to load workers');
            hideLoader();
        }
    });
}

function renderWorkersTable(workers) {
    const container = $('#workersTableContainer');

    if (!workers || workers.length === 0) {
        container.html(`
            <div class="empty-state">
                <i class="fas fa-server"></i>
                <div class="empty-state-title">No workers found</div>
                <div class="empty-state-text">No Celery workers are currently online. Please check your worker processes.</div>
            </div>
        `);
        return;
    }

    let tableHtml = `
        <table class="table">
            <thead>
                <tr>
                    <th>Hostname</th>
                    <th>PID</th>
                    <th>Active Tasks</th>
                    <th>Processed</th>
                    <th>Load Average</th>
                    <th>Software</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
    `;

    workers.forEach(worker => {
        const loadavg = worker.loadavg ? worker.loadavg.join(', ') : '-';
        const software = worker.sw_ident || '-';
        const isHealthy = worker.active < 10; // Consider healthy if less than 10 active tasks

        tableHtml += `
            <tr>
                <td>
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <i class="fas fa-server" style="color: var(--success);"></i>
                        <strong>${worker.hostname}</strong>
                    </div>
                </td>
                <td><code style="background: var(--gray-100); padding: 4px 8px; border-radius: 4px; font-size: 0.85rem;">${worker.pid || '-'}</code></td>
                <td>
                    <span class="status-badge ${worker.active > 0 ? 'status-started' : 'status-success'}">
                        <i class="fas fa-tasks"></i>
                        ${worker.active}
                    </span>
                </td>
                <td>
                    <span class="status-badge status-success">
                        <i class="fas fa-check-circle"></i>
                        ${worker.processed}
                    </span>
                </td>
                <td>${loadavg}</td>
                <td>${software}</td>
                <td>
                    <span class="status-badge ${isHealthy ? 'status-success' : 'status-pending'}">
                        <i class="fas fa-${isHealthy ? 'check-circle' : 'exclamation-triangle'}"></i>
                        ${isHealthy ? 'Healthy' : 'Busy'}
                    </span>
                </td>
            </tr>
        `;
    });

    tableHtml += `
            </tbody>
        </table>
    `;

    container.html(tableHtml);
}

function switchTab(tab) {
    // Update tab buttons with smooth transition
    $('.tab-btn').removeClass('active');

    // Find the correct tab button by checking the onclick attribute or data
    $('.tab-btn').each(function() {
        const onclick = $(this).attr('onclick');
        if (onclick && onclick.includes(`'${tab}'`)) {
            $(this).addClass('active');
        }
    });

    // Fade out current content
    $('.tab-content.active').fadeOut(200, function() {
        $('.tab-content').removeClass('active');
        $(`#${tab}Tab`).addClass('active').fadeIn(200);
    });

    currentTab = tab;

    // Load data for the new tab with a slight delay for smooth transition
    setTimeout(() => {
        if (tab === 'tasks') {
            loadTasks();
        } else if (tab === 'workers') {
            loadWorkers();
        }
    }, 100);
}

function filterTasks() {
    currentFilter = $('#stateFilter').val();
    loadTasks();
}

function showTaskDetails(taskId) {
    showLoader();
    
    $.ajax({
        url: `/api/monitor/tasks/${taskId}`,
        method: 'GET',
        success: function(data) {
            displayTaskDetailsModal(data.task);
            hideLoader();
        },
        error: function(xhr, status, error) {
            console.error('Error loading task details:', error);
            showError('Failed to load task details');
            hideLoader();
        }
    });
}

function displayTaskDetailsModal(task) {
    const statusIcon = getStatusIcon(task.state);
    const modalHtml = `
        <div class="modal-overlay" onclick="closeModal()">
            <div class="modal-content" onclick="event.stopPropagation()">
                <div class="modal-header">
                    <h3>
                        <i class="fas fa-info-circle"></i>
                        Task Details
                    </h3>
                    <button class="modal-close" onclick="closeModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="detail-grid">
                        <div class="detail-item">
                            <strong>Task ID:</strong>
                            <div>
                                <code style="background: var(--gray-100); padding: 8px 12px; border-radius: 6px; font-size: 0.9rem; display: inline-block;">${task.uuid}</code>
                            </div>
                        </div>
                        <div class="detail-item">
                            <strong>Task Name:</strong>
                            <div style="font-weight: 600; color: var(--gray-800);">${task.name || 'Unknown Task'}</div>
                        </div>
                        <div class="detail-item">
                            <strong>Current State:</strong>
                            <span class="status-badge status-${task.state.toLowerCase()}">
                                <i class="${statusIcon}"></i>
                                ${task.state}
                            </span>
                        </div>
                        <div class="detail-item">
                            <strong>Worker:</strong>
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <i class="fas fa-server" style="color: var(--success);"></i>
                                <span>${task.worker || 'Not assigned'}</span>
                            </div>
                        </div>
                        <div class="detail-item">
                            <strong>Started At:</strong>
                            <div>${task.started ? formatTimestamp(task.started) : 'Not started yet'}</div>
                        </div>
                        <div class="detail-item">
                            <strong>Runtime:</strong>
                            <div style="font-weight: 600; color: var(--primary);">${task.runtime ? formatDuration(task.runtime) : 'Not available'}</div>
                        </div>
                        ${task.args && task.args.length > 0 ? `
                        <div class="detail-item">
                            <strong>Arguments:</strong>
                            <pre>${JSON.stringify(task.args, null, 2)}</pre>
                        </div>
                        ` : ''}
                        ${task.kwargs && Object.keys(task.kwargs).length > 0 ? `
                        <div class="detail-item">
                            <strong>Keyword Args:</strong>
                            <pre>${JSON.stringify(task.kwargs, null, 2)}</pre>
                        </div>
                        ` : ''}
                        ${task.result ? `
                        <div class="detail-item">
                            <strong>Result:</strong>
                            <pre>${JSON.stringify(task.result, null, 2)}</pre>
                        </div>
                        ` : ''}
                        ${task.exception ? `
                        <div class="detail-item" style="border-left-color: var(--danger);">
                            <strong>Exception:</strong>
                            <pre class="error-text">${task.exception}</pre>
                        </div>
                        ` : ''}
                        ${task.traceback ? `
                        <div class="detail-item" style="border-left-color: var(--danger);">
                            <strong>Traceback:</strong>
                            <pre class="error-text">${task.traceback}</pre>
                        </div>
                        ` : ''}
                    </div>
                </div>
            </div>
        </div>
    `;

    $('body').append(modalHtml);
}

function revokeTask(taskId) {
    if (!confirm('Are you sure you want to revoke this task?')) {
        return;
    }
    
    showLoader();
    
    $.ajax({
        url: `/api/monitor/tasks/${taskId}/revoke`,
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            action: 'revoke',
            terminate: false
        }),
        success: function(data) {
            showSuccess('Task revoked successfully');
            loadTasks(); // Refresh the tasks list
            hideLoader();
        },
        error: function(xhr, status, error) {
            console.error('Error revoking task:', error);
            showError('Failed to revoke task');
            hideLoader();
        }
    });
}

function closeModal() {
    $('.modal-overlay').remove();
}

// Utility functions
function formatTimestamp(timestamp) {
    const date = new Date(timestamp * 1000);
    return date.toLocaleString();
}

function formatDuration(seconds) {
    if (seconds < 60) {
        return `${seconds.toFixed(1)}s`;
    } else if (seconds < 3600) {
        return `${(seconds / 60).toFixed(1)}m`;
    } else {
        return `${(seconds / 3600).toFixed(1)}h`;
    }
}

function showError(message) {
    // You can implement a toast notification system here
    console.error(message);
    alert(message);
}

function showSuccess(message) {
    // You can implement a toast notification system here
    console.log(message);
    alert(message);
}

function showLoader() {
    // Show loading state - you can customize this
    console.log('Loading...');
}

function hideLoader() {
    // Hide loading state - you can customize this
    console.log('Loading complete');
}
