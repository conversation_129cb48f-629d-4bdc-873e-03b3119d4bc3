/* Modern UI Design System */
:root {
    /* Color Palette */
    --primary: #4361ee;
    --primary-light: #4895ef;
    --primary-dark: #3f37c9;
    --secondary: #4cc9f0;
    --accent: #f72585;
    --success: #4caf50;
    --warning: #ff9800;
    --danger: #f44336;
    --light: #f8f9fa;
    --dark: #212529;
    --gray-100: #f8f9fa;
    --gray-200: #e9ecef;
    --gray-300: #dee2e6;
    --gray-400: #ced4da;
    --gray-500: #adb5bd;
    --gray-600: #6c757d;
    --gray-700: #495057;
    --gray-800: #343a40;
    --gray-900: #212529;

    /* Typography */
    --font-family: 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --font-size-base: 12px;
    --font-weight-normal: 400;
    --font-weight-bold: 600;
    --font-weight-bolder: 700;

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;

    /* Borders */
    --border-radius-sm: 0.25rem;
    --border-radius: 0.375rem;
    --border-radius-lg: 0.5rem;
    --border-radius-xl: 1rem;

    /* Shadows */
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 6px 12px rgba(0, 0, 0, 0.08);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);

    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* Base Styles */
body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    display: flex;
    margin: 0;
    background-color: var(--gray-100);
    color: var(--gray-800);
}

/* Modern Sidebar Styles */
.sidebar {
    width: 220px;
    background: linear-gradient(135deg, var(--gray-900), var(--gray-800) 70%);
    color: white;
    padding: var(--spacing-lg) var(--spacing-md);
    height: 100vh;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    transition: all var(--transition);
    position: fixed;
    z-index: 100;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    overflow-y: auto;
    border-right: 1px solid rgba(255, 255, 255, 0.05);
}

.sidebar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at top right, rgba(67, 97, 238, 0.1), transparent 70%);
    pointer-events: none;
}

.sidebar h2 {
    text-align: left;
    margin-bottom: var(--spacing-xl);
    font-weight: var(--font-weight-bold);
    color: white;
    font-size: 1.4rem;
    letter-spacing: 0.5px;
    padding: var(--spacing-sm) var(--spacing-md);
    display: flex;
    align-items: center;
    position: relative;
}

.sidebar h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.1), transparent);
}

.sidebar ul {
    list-style-type: none;
    padding: 0;
    margin: var(--spacing-lg) 0;
}

.sidebar ul.menu {
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    padding-bottom: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.sidebar ul li {
    margin-bottom: var(--spacing-xs);
    border-radius: var(--border-radius-lg);
    transition: all var(--transition-fast);
    position: relative;
}

.sidebar ul li a {
    color: var(--gray-300);
    text-decoration: none;
    display: flex;
    align-items: center;
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    transition: all var(--transition-fast);
    font-weight: var(--font-weight-normal);
    position: relative;
    z-index: 1;
}

.sidebar ul li a::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background: linear-gradient(90deg, var(--primary), transparent);
    opacity: 0;
    transition: all var(--transition);
    border-radius: var(--border-radius-lg);
    z-index: -1;
}

.sidebar ul li a:hover {
    color: white;
    transform: translateX(3px);
}

.sidebar ul li a:hover::before {
    width: 100%;
    opacity: 0.1;
}

.sidebar ul li a.active {
    background: linear-gradient(90deg, var(--primary), rgba(67, 97, 238, 0.7));
    color: white;
    box-shadow: 0 4px 10px rgba(67, 97, 238, 0.3);
    font-weight: var(--font-weight-bold);
}

.sidebar ul li a.active .nav-icon {
    color: white;
}

.sidebar ul li a .nav-icon {
    font-size: 1.1rem;
    margin-right: var(--spacing-md);
    width: 20px;
    text-align: center;
    transition: all var(--transition);
    color: var(--gray-400);
}

.sidebar ul li a:hover .nav-icon {
    color: white;
    transform: scale(1.1);
}

.logout {
    margin-top: auto;
    padding-top: var(--spacing-md);
}

.logout li {
    margin-bottom: 0;
}

.logout a {
    color: var(--gray-400) !important;
    border: 1px solid rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius-lg);
    margin: var(--spacing-md) var(--spacing-xs);
    transition: all var(--transition);
}

.logout a:hover {
    color: white !important;
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.1);
}

/* Main Content */
.main-content {
    flex: 1;
    padding: var(--spacing-xl);
    margin-left: 270px; /* Updated to match new sidebar width + padding */
    min-height: 100%;
    transition: margin-left var(--transition);
}

/* Form Elements */
label {
    display: block;
    margin: var(--spacing-md) 0 var(--spacing-xs);
    font-weight: var(--font-weight-bold);
    color: var(--gray-700);
}

input, select, textarea {
    padding: var(--spacing-md);
    width: 100%;
    margin-bottom: var(--spacing-md);
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
}

input:focus, select:focus, textarea:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
}

/* Buttons */
button {
    background: var(--primary);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    padding: var(--spacing-md) var(--spacing-lg);
    font-weight: var(--font-weight-bold);
    cursor: pointer;
    transition: background var(--transition-fast), transform var(--transition-fast);
    box-shadow: var(--shadow-sm);
}

button:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

button:active {
    transform: translateY(0);
}

button:disabled {
    background: var(--gray-400);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Text Styles */
p {
    line-height: 1.6;
    margin-bottom: var(--spacing-md);
}

h1, h2, h3, h4, h5, h6 {
    color: var(--gray-900);
    margin-bottom: var(--spacing-md);
    font-weight: var(--font-weight-bold);
}

h2 {
    font-size: 1.75rem;
    margin-bottom: var(--spacing-lg);
}

/* Card Styles */
.card {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
    transition: transform var(--transition), box-shadow var(--transition);
}

.card:hover {
    box-shadow: var(--shadow-lg);
}

/* Table Styles */
table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin-bottom: var(--spacing-xl);
}

th, td {
    padding: var(--spacing-md);
    text-align: left;
    border-bottom: 1px solid var(--gray-200);
}

th {
    background-color: var(--gray-100);
    font-weight: var(--font-weight-bold);
    color: var(--gray-700);
    position: sticky;
    top: 0;
    z-index: 10;
}

tbody tr {
    transition: background var(--transition-fast);
}

tbody tr:hover {
    background-color: var(--gray-100);
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        width: 220px;
    }

    .main-content {
        margin-left: 220px;
        padding: var(--spacing-md);
    }

    .sidebar h2 {
        font-size: 1.2rem;
    }
}

@media (max-width: 576px) {
    .sidebar {
        width: 70px;
        padding: var(--spacing-md) var(--spacing-xs);
    }

    .sidebar h2, .sidebar ul li a span {
        display: none;
    }

    .sidebar h2::after {
        display: none;
    }

    .sidebar ul.menu {
        border-bottom: none;
    }

    .sidebar ul li a {
        padding: var(--spacing-md);
        justify-content: center;
    }

    .sidebar ul li a .nav-icon {
        margin-right: 0;
        font-size: 1.3rem;
    }

    .logout a {
        justify-content: center;
        padding: var(--spacing-md);
    }

    .main-content {
        margin-left: 70px;
        padding: var(--spacing-sm);
    }
}

/* Status Badges */
.badge {
    display: inline-block;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius);
    font-size: 0.75rem;
    font-weight: var(--font-weight-bold);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-primary { background-color: var(--primary); color: white; }
.badge-success { background-color: var(--success); color: white; }
.badge-warning { background-color: var(--warning); color: var(--gray-900); }
.badge-danger { background-color: var(--danger); color: white; }
.badge-info { background-color: var(--secondary); color: var(--gray-900); }

/* Utility Classes */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }

.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }
.mt-5 { margin-top: var(--spacing-xl); }

.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.mb-5 { margin-bottom: var(--spacing-xl); }

.hidden { display: none; }

/* Task Monitor Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    backdrop-filter: blur(3px);
}

.modal-content {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--gray-200);
    background: var(--gray-100);
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

.modal-header h3 {
    margin: 0;
    color: var(--gray-900);
    font-size: 1.2rem;
    font-weight: var(--font-weight-bold);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.modal-header h3 i {
    color: var(--primary);
    font-size: 1rem;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--gray-600);
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all var(--transition-fast);
}

.modal-close:hover {
    background: var(--gray-200);
    color: var(--gray-900);
}

.modal-body {
    padding: var(--spacing-lg);
}

.detail-grid {
    display: grid;
    gap: var(--spacing-md);
}

.detail-item {
    display: grid;
    grid-template-columns: 120px 1fr;
    gap: var(--spacing-sm);
    align-items: start;
}

.detail-item strong {
    color: var(--gray-700);
    font-weight: var(--font-weight-bold);
}

.detail-item pre {
    background: var(--gray-100);
    padding: var(--spacing-sm);
    border-radius: var(--border-radius);
    font-size: 0.85rem;
    overflow-x: auto;
    margin: 0;
}

.detail-item .error-text {
    background: var(--danger-light);
    color: var(--danger);
    border-left: 3px solid var(--danger);
}

/* Request_detail.html page css */
:root {
    --primary-color: #4361ee;
    --primary-light: rgba(67, 97, 238, 0.1);
    --primary-dark: #3a56d4;
    --secondary-color: #6c5ce7;
    --secondary-light: rgba(108, 92, 231, 0.1);
    --success-color: #2ecc71;
    --success-light: rgba(46, 204, 113, 0.1);
    --warning-color: #f39c12;
    --warning-light: rgba(243, 156, 18, 0.1);
    --danger-color: #e74c3c;
    --danger-light: rgba(231, 76, 60, 0.1);
    --info-color: #3498db;
    --info-light: rgba(52, 152, 219, 0.1);
    --light-blue: #f0f7ff;
    --light-blue-2: #e6f0ff;
    --gray-100: #f8f9fa;
    --gray-200: #e9ecef;
    --gray-300: #dee2e6;
    --gray-400: #ced4da;
    --gray-500: #adb5bd;
    --gray-600: #6c757d;
    --gray-700: #495057;
    --gray-800: #343a40;
    --gray-900: #212529;
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 6px 12px rgba(0, 0, 0, 0.08);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --border-radius-sm: 4px;
    --border-radius: 8px;
    --border-radius-lg: 12px;
    --border-radius-xl: 16px;
    --border-radius-pill: 50px;
    --transition-fast: 0.2s ease;
    --transition: 0.3s ease;
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-bold: 600;
    --font-weight-bolder: 700;
}

/* Page Header with Log Icon */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--gray-200);
}

.page-header h2 {
    margin: 0;
    color: var(--gray-900);
    font-size: 1.75rem;
    display: flex;
    align-items: center;
    font-weight: var(--font-weight-bold);
}

.page-header h2 i {
    margin-right: var(--spacing-sm);
    color: var(--primary-color);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.header-actions .btn-tab {
    padding: 8px 16px;
    font-size: 0.9rem;
}

/* Log Icon Button */
.log-icon-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--primary-light);
    color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-fast);
    border: none;
    font-size: 1.1rem;
}

.log-icon-btn:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* Sliding Logs Panel */
.logs-panel {
    position: fixed;
    top: 0;
    right: -500px; /* Start off-screen */
    width: 500px;
    height: 100vh;
    background-color: white;
    box-shadow: var(--shadow-lg);
    z-index: 1000;
    transition: right var(--transition);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.logs-panel.active {
    right: 0; /* Slide in */
}

.logs-panel-header {
    background-color: #6c5ce7; /* Purple header like in reference */
    color: white;
    padding: var(--spacing-md) var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logs-panel-header h3 {
    margin: 0;
    font-size: 1.25rem;
}

.logs-panel-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.25rem;
    cursor: pointer;
    transition: transform var(--transition-fast);
}

.logs-panel-close:hover {
    transform: scale(1.1);
}

.logs-panel-content {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-md);
}

.logs-panel-filter {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.logs-panel-filter select {
    flex: 1;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    background-color: white;
}

/* Request Detail Container */
.request-detail-container {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
    transition: box-shadow var(--transition);
    position: relative;
}

.request-detail-container:hover {
    box-shadow: var(--shadow-md);
}

/* Detail Container */
.detail-container {
    margin-bottom: var(--spacing-lg);
    transition: box-shadow var(--transition);
}

/* Detail Items */
.row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.detail-item {
    display: grid;
    grid-template-columns: 180px 1fr;
    align-items: center;
    margin-bottom: var(--spacing-md);
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--gray-200);
}

/* Special styling for the input file detail item */
.file-detail-item {
    align-items: flex-start !important;
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-item h3 {
    font-size: 0.95rem;
    color: var(--gray-600);
    margin: 0;
    font-weight: var(--font-weight-medium);
}

.detail-item p {
    margin: 0;
    font-size: 1rem;
    color: var(--gray-800);
    font-weight: var(--font-weight-medium);
    word-wrap: break-word;
    overflow-wrap: break-word;
    word-break: break-word;
    max-width: 100%;
    overflow-x: auto;
}

/* File name truncation */
.detail-item p#fileName {
    max-width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: help;
}

/* Formatted Date Styling */
.formatted-date {
    display: flex;
    align-items: center;
    border-radius: 15px;
}

.date-wrapper {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 10px;
}

.date-part {
    font-weight: var(--font-weight-medium);
    color: var(--primary-color);
    background-color: rgba(100, 116, 255, 0.1);
    padding: 5px 6px;
    border-radius: var(--border-radius-pill);
    align-items: center;
    font-family: 'Inter', 'Segoe UI', 'Arial', sans-serif;
    letter-spacing: 0.5px;
    box-shadow: var(--shadow-sm);
    gap: 8px;
}

.date-part i {
    color: var(--primary-dark);
    font-size: 0.9rem;
}

.time-part {
    color: var(--gray-700);
    font-family: 'Inter', 'Segoe UI', 'Arial', sans-serif;
    font-size: 0.9rem;
    background-color: white;
    padding: 5px 12px;
    border-radius: var(--border-radius-pill);
    display: inline-flex;
    align-items: center;
    letter-spacing: 0.5px;
    gap: 8px;
    border: 1px solid var(--gray-200);
}

.time-part i {
    color: var(--gray-600);
    font-size: 0.9rem;
}

/* Status Badge */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--border-radius-pill);
    font-size: 0.75rem;
    font-weight: var(--font-weight-bold);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
    position: relative;
    overflow: hidden;
}

.status-badge i {
    margin-right: var(--spacing-xs);
}

.status-badge::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 4px;
}

.status-open {
    background-color: var(--info-light);
    color: var(--info-color);
}

.status-open::before {
    background-color: var(--info-color);
}

.status-start {
    background-color: var(--info-light);
    color: var(--info-color);
}

.status-start::before {
    background-color: var(--info-color);
}

.status-inprogress {
    background-color: var(--primary-light);
    color: var(--primary-color);
}

.status-inprogress::before {
    background-color: var(--primary-color);
}

.status-done {
    background-color: var(--success-light);
    color: var(--success-color);
}

.status-done::before {
    background-color: var(--success-color);
}

.status-error {
    background-color: var(--danger-light);
    color: var(--danger-color);
}

.status-error::before {
    background-color: var(--danger-color);
}

.status-pending {
    background-color: var(--warning-light);
    color: var(--warning-color);
}

.status-pending::before {
    background-color: var(--warning-color);
}

.status-approved, .status-completed {
    background-color: var(--success-light);
    color: var(--success-color);
}

.status-approved::before, .status-completed::before {
    background-color: var(--success-color);
}

.status-rejected, .status-failed {
    background-color: var(--danger-light);
    color: var(--danger-color);
}

.status-rejected::before, .status-failed::before {
    background-color: var(--danger-color);
}

/* Modern Tabs */
.tab-container {
    display: flex;
    margin-bottom: var(--spacing-lg);
    border-bottom: 1px solid var(--gray-200);
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.tab {
    padding: var(--spacing-md) var(--spacing-lg);
    cursor: pointer;
    font-weight: var(--font-weight-bold);
    color: var(--gray-600);
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    position: relative;
    margin-bottom: -1px;
    background-color: transparent;
}

.tab i {
    font-size: 1rem;
}

.tab.active {
    color: var(--primary);
    border-bottom: 3px solid var(--primary);
    background-color: rgba(67, 97, 238, 0.05);
}

.tab:hover:not(.active) {
    color: var(--primary-dark);
    background-color: var(--gray-100);
}

/* Count Details */
.count-details {
    display: none;
    background: white;
    overflow: hidden;
    transition: all var(--transition);
    margin-bottom: var(--spacing-lg);
    border-radius: var(--border-radius-lg)
}

.count-details.active {
    display: block;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.count-details h4 {
    margin: 0;
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: 1.2rem;
    color: var(--gray-800);
    background-color: var(--light-blue);
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--gray-200);
}

/* Time Duration Styling */
.time-duration {
    display: inline-flex;
    align-items: center;
    background-color: rgba(67, 97, 238, 0.08);
    padding: 6px 12px;
    border-radius: var(--border-radius-pill);
    font-size: 0.9rem;
    color: var(--primary-color);
    font-weight: var(--font-weight-medium);
    box-shadow: var(--shadow-sm);
    gap: 8px;
    border: 1px solid rgba(67, 97, 238, 0.15);
}

.time-duration i {
    color: var(--primary-dark);
    font-size: 0.9rem;
}

.time-duration-label {
    color: var(--gray-600);
    font-weight: var(--font-weight-normal);
    margin-right: 4px;
}

.time-duration-value {
    color: var(--primary-color);
    font-weight: var(--font-weight-bold);
}

#processRequestTime {
    color: var(--gray-700);
    font-size: 0.9rem;
    font-weight: var(--font-weight-normal);
}

.count-section {
    padding: var(--spacing-lg);
}

/* Count Grid Layout */
.count-grid {
    display: grid;
    grid-template-columns: 1fr 80px;
    align-items: center;
    margin-bottom: var(--spacing-xs);
    padding: var(--spacing-xs) 0;
}

.count-grid:not(:last-child) {
    border-bottom: 1px solid var(--gray-200);
}

.count-label {
    font-size: 0.95rem;
    color: var(--gray-700);
}

.count-value {
    font-weight: var(--font-weight-bold);
    background-color: var(--light-blue-2);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    text-align: center;
    color: var(--primary-color);
}

/* Count colors based on type */
.count-new .count-value {
    background-color: var(--success-light);
    color: var(--success-color);
}

.count-error .count-value {
    background-color: var(--danger-light);
    color: var(--danger-color);
}

.count-exists .count-value {
    background-color: var(--info-light);
    color: var(--info-color);
}

.count-update .count-value {
    background-color: var(--warning-light);
    color: var(--warning-color);
}

/* Output Files Section */
.output-file-card {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    /* margin-top: var(--spacing-lg); */
    overflow: hidden;
}

.output-file-card h4 {
    margin: 0;
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: 1.2rem;
    color: var(--primary-color);
    background-color: var(--primary-light);
}

.file-list {
    list-style: none;
    padding: var(--spacing-lg);
    margin: 0;
}

.file-item {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-md);
    padding: 0;
}

#fileDownloadList {
    padding: 0;
    margin: 0;
}

/* Handle long file names */
.file-item span {
    max-width: 300px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: inline-block;
    cursor: help;
}

.file-download {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    background-color: var(--primary-color);
    color: white;
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--border-radius);
    text-decoration: none;
    transition: all var(--transition-fast);
    box-shadow: var(--shadow-sm);
    cursor: pointer;
}

.file-download:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

/* Action Buttons */
.btn-tab {
    padding: var(--spacing-sm) var(--spacing-lg);
    background: var(--primary-color);
    border: none;
    cursor: pointer;
    border-radius: var(--border-radius);
    font-weight: var(--font-weight-medium);
    color: white;
    transition: all var(--transition-fast);
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    box-shadow: var(--shadow-sm);
}

.btn-tab i {
    font-size: 0.9rem;
}

.btn-tab:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.btn-class {
    display: flex;
    gap: var(--spacing-md);
    margin-top: var(--spacing-lg);
}

/* Modern Approval Action Buttons */
.approval-actions-container {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    padding: var(--spacing-md) var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    border-left: 4px solid var(--warning-color);
    position: relative;
    overflow: hidden;
    animation: fadeInDown 0.5s ease-out forwards;
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.approval-actions-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(255, 152, 0, 0.05) 0%, rgba(255, 255, 255, 0) 70%);
    z-index: 0;
}

.approval-status {
    display: flex;
    flex-direction: column;
    z-index: 1;
}

.approval-status-title {
    font-size: 1.1rem;
    font-weight: var(--font-weight-bold);
    color: var(--gray-800);
    margin-bottom: var(--spacing-xs);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.approval-status-title i {
    color: var(--warning-color);
}

.approval-status-message {
    font-size: 0.9rem;
    color: var(--gray-600);
    margin-bottom: var(--spacing-md);
}

.approval-buttons {
    display: flex;
    gap: var(--spacing-md);
    z-index: 1;
}

.approve-button {
    padding: var(--spacing-md) var(--spacing-xl);
    background: var(--success-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-weight: var(--font-weight-bold);
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    box-shadow: var(--shadow-sm);
    min-width: 140px;
    justify-content: center;
    position: relative;
    overflow: hidden;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
    }
}

.approve-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0) 100%);
    transition: all 0.8s ease;
}

.approve-button:hover {
    background: #218838;
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

.approve-button:hover::before {
    left: 100%;
}

.reject-button {
    padding: var(--spacing-md) var(--spacing-xl);
    background: var(--danger-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-weight: var(--font-weight-bold);
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    box-shadow: var(--shadow-sm);
    min-width: 140px;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.reject-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0) 100%);
    transition: all 0.8s ease;
}

.reject-button:hover {
    background: #c82333;
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

.reject-button:hover::before {
    left: 100%;
}

@media (max-width: 768px) {
    .approval-actions-container {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-md);
    }

    .approval-buttons {
        width: 100%;
    }

    .approve-button, .reject-button {
        flex: 1;
    }
}

/* Log Styling */
.log-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-md);
    border-left: 4px solid;
    transition: transform var(--transition-fast), box-shadow var(--transition-fast);
}

.log-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.log-header {
    font-weight: var(--font-weight-bold);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 0.9rem;
    margin-bottom: var(--spacing-xs);
}

.log-body {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 0.8rem;
    color: var(--gray-600);
    margin-bottom: var(--spacing-xs);
}

.log-message {
    font-size: 0.9rem;
    color: var(--gray-800);
    margin-top: var(--spacing-xs);
}

.log-message pre {
    background-color: var(--gray-100);
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    overflow-x: auto;
    font-size: 0.8rem;
    margin-top: var(--spacing-xs);
}

/* Log Colors */
.log-error { border-left-color: var(--danger-color); color: var(--danger-color); }
.log-info { border-left-color: var(--info-color); color: var(--info-color); }
.log-result { border-left-color: var(--success-color); color: var(--success-color); }
.log-warning { border-left-color: var(--warning-color); color: var(--warning-color); }
.log-general { border-left-color: var(--gray-500); color: var(--gray-500); }

/* Status Badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius);
    font-size: 0.75rem;
    font-weight: var(--font-weight-bold);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-approved, .status-completed {
    background-color: rgba(40, 167, 69, 0.2);
    color: #155724;
}

.status-rejected, .status-failed {
    background-color: rgba(220, 53, 69, 0.2);
    color: #721c24;
}

/* Overlay for when logs panel is open */
.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: opacity var(--transition), visibility var(--transition);
}

.overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Loader Styles */
.loader-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.7);
    z-index: 998;
    display: none;
    justify-content: center;
    align-items: center;
    border-radius: var(--border-radius-lg);
}

.loader-overlay.visible {
    display: flex;
}

.loader {
    border: 4px solid var(--gray-200);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Styles */
@media (max-width: 992px) {
    .logs-panel {
        width: 350px;
    }
}

@media (max-width: 768px) {
    .row {
        grid-template-columns: 1fr;
    }

    .logs-panel {
        width: 300px;
    }

    .tab-container {
        flex-direction: column;
        align-items: stretch;
    }

    .tab {
        width: 100%;
    }

    .btn-class {
        flex-direction: column;
    }

    .btn-tab {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 576px) {
    .logs-panel {
        width: 100%;
        right: -100%;
    }

    .detail-item {
        flex-direction: column;
        align-items: flex-start;
    }

    .detail-item h3 {
        min-width: auto;
        margin-bottom: var(--spacing-xs);
    }

    .detail-item p::before {
        display: none;
    }
}

/* Approval Reasons Section */
.approval-reasons-section {
    margin-top: var(--spacing-md);
    border-top: 1px solid var(--gray-200);
    padding-top: var(--spacing-md);
}

.approval-reasons-loading {
    display: flex;
    align-items: center;
    color: var(--gray-600);
    font-style: italic;
    padding: var(--spacing-sm) 0;
}

.approval-reasons-loading i {
    margin-right: var(--spacing-sm);
    color: var(--primary-color);
}

.approval-reasons-title {
    font-weight: var(--font-weight-bold);
    color: var(--gray-800);
    margin-bottom: var(--spacing-sm);
    display: flex;
    align-items: center;
    font-size: 1rem;
}

.approval-reasons-title i {
    margin-right: var(--spacing-sm);
    color: var(--warning-color);
}

.approval-reason-item {
    display: flex;
    align-items: flex-start;
    padding: var(--spacing-sm) var(--spacing-md);
    margin-bottom: var(--spacing-xs);
    border-radius: var(--border-radius);
    background-color: var(--gray-100);
    border-left: 3px solid var(--gray-400);
}

.approval-reason-item i {
    margin-right: var(--spacing-sm);
    color: var(--gray-600);
    margin-top: 2px;
}

/* Category-specific styling */
.supplier-reason {
    border-left-color: #3498db;
    background-color: rgba(52, 152, 219, 0.05);
}

.supplier-reason i {
    color: #3498db;
}

.catalog-reason {
    border-left-color: #9b59b6;
    background-color: rgba(155, 89, 182, 0.05);
}

.catalog-reason i {
    color: #9b59b6;
}

.property-reason {
    border-left-color: #2ecc71;
    background-color: rgba(46, 204, 113, 0.05);
}

.property-reason i {
    color: #2ecc71;
}

.warehouse-reason {
    border-left-color: #f39c12;
    background-color: rgba(243, 156, 18, 0.05);
}

.warehouse-reason i {
    color: #f39c12;
}

.unit-reason {
    border-left-color: #e74c3c;
    background-color: rgba(231, 76, 60, 0.05);
}

.unit-reason i {
    color: #e74c3c;
}

/* Multi-scenario categories */
.approval-reason-category {
    margin-bottom: var(--spacing-md);
}

.category-header {
    font-weight: var(--font-weight-bold);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    display: flex;
    align-items: center;
}

.category-header i {
    margin-right: var(--spacing-sm);
}

.supplier-category {
    background-color: rgba(52, 152, 219, 0.1);
    color: #3498db;
}

.catalog-category {
    background-color: rgba(155, 89, 182, 0.1);
    color: #9b59b6;
}

.property-category {
    background-color: rgba(46, 204, 113, 0.1);
    color: #2ecc71;
}

.warehouse-category {
    background-color: rgba(243, 156, 18, 0.1);
    color: #f39c12;
}

.unit-category {
    background-color: rgba(231, 76, 60, 0.1);
    color: #e74c3c;
}

.category-reasons {
    border: 1px solid var(--gray-200);
    border-top: none;
    border-radius: 0 0 var(--border-radius) var(--border-radius);
    padding: var(--spacing-sm);
    background-color: white;
}

/* Confirmation dialog approval reasons */
.approval-reasons-list {
    margin: var(--spacing-md) 0;
    padding-left: var(--spacing-lg);
}

.approval-reasons-list li {
    margin-bottom: var(--spacing-xs);
    display: flex;
    align-items: flex-start;
}

.approval-reasons-list li i {
    margin-right: var(--spacing-sm);
    min-width: 16px;
}

.approval-reasons-list.categorized {
    border-left: 2px solid var(--gray-300);
}

.approval-reasons-list .category-header {
    margin-top: var(--spacing-sm);
    padding: var(--spacing-xs) 0;
    background-color: transparent;
}