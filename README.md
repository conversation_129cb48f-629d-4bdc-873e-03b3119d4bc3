# New Echem Data Upload

## ⚙️ Project Overview

The **New Echem Data Upload** project is designed to handle the uploading and processing of chemical data. It includes several components responsible for handling different data types and queues, such as price data, product information, and stock details.

This repository is structured to facilitate efficient data processing through the following core components:
- **Data Upload Worker**: The `PriceProcessQueue`, `ProductProcessQueue`, `StockProcessQueue`, and `UploadProcessQueue` are dedicated to handling specific data types.
- **API**: A central API that interacts with different processes.
- **Process Manager**: Manages the data processing tasks and ensures efficient task handling.

---

## 📌 Clone the Repository

You can clone the entire repository or just a specific folder (optional).

### Option 1: Clone the Entire Repository

To clone the entire repository, simply run:

```sh
git clone https://github.com/echemteam-developers/new-echem-data-upload.git
```

If you prefer **SSH** authentication, use:

```sh
<NAME_EMAIL>:echemteam-developers/new-echem-data-upload.git
```

### Option 2: Clone Only a Specific Folder (Sparse Checkout)

If you only want to download a specific folder (for example, `PriceProcessQueue`), follow the steps below after cloning the repository **without checking out the files**.

#### Step 1: Clone the Repository without Checkout

To clone without checking out files (if using HTTPS):

```sh
git clone --no-checkout https://github.com/echemteam-developers/new-echem-data-upload.git
```

Or if using **GitHub Personal Access Token (PAT)**:

```sh
git clone --no-checkout https://<TOKEN>@github.com/echemteam-developers/new-echem-data-upload.git
```

Replace `<TOKEN>` with your actual token.

If using **SSH**:

```sh
git clone --no-checkout **************:echemteam-developers/new-echem-data-upload.git
```

#### Step 2: Initialize Sparse Checkout

After cloning, run the following commands to initialize **sparse checkout** and download the specific folder you need:

```sh
cd new-echem-data-upload
git sparse-checkout init --cone
git sparse-checkout set data-upload-worker/PriceProcessQueue
git checkout
```

---

## ⚙️ How to Set Up the Project

To set up this project locally, follow these steps:

### Prerequisites

Before proceeding with the setup, make sure you have the following installed:
- **Python** (preferably version 3.8 or higher)
- **Git** for version control
- **Docker** (optional, for containerized environments)
- **MongoDB** 
- **Python Dependencies** (listed in the requirements file)

### Step 1: Clone the Repository

Follow the instructions above to clone the entire repository or just the folder you're working with.

### Step 2: Install Dependencies

Once the repository is cloned, navigate to the relevant directory (`data-upload-worker/PriceProcessQueue`, or any other part you're working with) and install the dependencies:

```sh
cd data-upload-worker/PriceProcessQueue
pip install -r requirements.txt
```

---

## 🛠️ Usage

The project contains several queues that can be used for data processing tasks. For example, to start processing price data, navigate to the appropriate queue directory and run the application:

```sh
cd data-upload-worker/PriceProcessQueue
python app.py
```

### Available Queues:
- **Price Process Queue**: Handles price-related data.
- **Product Process Queue**: Manages product data uploads.
- **Stock Process Queue**: Manages stock data.
- **Upload Process Queue**: Handles general upload operations.

---

## 🧰 Configuration

There are configuration files located in various directories, including `config.cfg` and `config.cfg.template`. Update these files to suit your specific needs, such as API keys, database connections, or other project-specific settings.

---

## 🚀 Running in a Dockerized Environment

This project also supports Docker for containerization. If you prefer to run the entire project in a containerized environment, follow these steps:

### Step 1: Set Up Docker

Make sure **Docker** is installed on your machine. Then, build the Docker images for the relevant services:

```sh
docker-compose -f docker-compose.yml build
```

### Step 2: Start Services

Start the services using Docker Compose:

```sh
docker-compose up
```

This will bring up the necessary containers, including those for API processing, data management, and other tasks.

---

## 📜 Additional Information

For more detailed instructions on authentication, GitHub setup, or configuration management, please refer to the following resources:
- [GitHub Docs: Personal Access Tokens](https://docs.github.com/en/github/authenticating-to-github/creating-a-personal-access-token)
- [GitHub Docs: SSH Key Setup](https://docs.github.com/en/github/authenticating-to-github/connecting-to-github-with-ssh)
- [Python Documentation](https://docs.python.org/)
- [Docker Documentation](https://docs.docker.com/)

---

## ⚠️ Notes

✅ Make sure you have the necessary **permissions** to access the repository.  
✅ If using **Token-based authentication**, ensure you use a **GitHub PAT** with appropriate **repository access**.  
✅ If using **SSH**, ensure your **SSH key** is correctly configured in GitHub.  

---
