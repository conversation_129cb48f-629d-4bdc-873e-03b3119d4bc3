import warnings
from aiohttp import web
import configparser
import os
import logging
import math
import wget
import sys
import time
from datetime import datetime
import pandas as pd
import csv
import requests
import json
import subprocess
import re
from datetime import date
from base64 import b64encode, b64decode
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad
import venv
import platform
import shutil
import time
import glob
import sys
import pika
import pyodbc

warnings.filterwarnings('ignore')

app = web.Application()  # Initialising webApplication

# Get the current folder and the configuration file path
thisfolder = os.path.dirname(os.path.abspath(__file__))

def find_config_file(filename="config.cfg", max_levels=2):
    """Finds the config file by searching only up to two parent directories."""
    current_folder = os.path.dirname(os.path.abspath(__file__))

    for _ in range(max_levels + 1):  # Search up to 'max_levels' parents
        config_path = os.path.join(current_folder, filename)
        if os.path.isfile(config_path):
            return config_path  # Return the first match found
        current_folder = os.path.dirname(current_folder)  # Move one level up

    raise FileNotFoundError(f"{filename} not found within {max_levels} parent directories.")

# Get the config file path
cfgfile = find_config_file()

# Read the configuration file
config = configparser.ConfigParser()
config.read(cfgfile)

Main_folders = [config.get("PATH", key) for key in ["MainTempFolder", "LogFilesFolder"]]

# Define the list of temp folders using a list comprehension
Temp_folders = [config.get("PATH", key) for key in
                ["QueueLogFilesFolder", "RequestLogFilesFolder"]]

# Creating all required temp folder according stages
for folder in Main_folders + Temp_folders:
    try:
        os.makedirs(os.path.join(thisfolder, folder), exist_ok=True)
    except Exception as e:
        print(f"Error creating directory {folder}: {str(e)}")

# Create a logger
logger = logging.getLogger('my_logger')
logger.setLevel(logging.INFO)

# Create a StreamHandler to output log messages to stdout
stream_handler = logging.StreamHandler()
stream_handler.setLevel(logging.INFO)

# Make a path for logfiles
log_file = os.path.join(thisfolder, 'Log/QueueLog', f'{datetime.today().strftime("%d-%m-%Y")}.txt')

# Create a FileHandler to store log messages in a file
file_handler = logging.FileHandler(log_file)
file_handler.setLevel(logging.INFO)

# Create a formatter and add it to the handlers
formatter = logging.Formatter(
    "[%(asctime)s]:[PID %(process)d]:[%(levelname)s]:%(name)s::%(filename)s|%(lineno)d|%(message)s",
    datefmt='%d-%m-%Y %H:%M:%S')
stream_handler.setFormatter(formatter)
file_handler.setFormatter(formatter)

# Add the handlers to the logger
logger.addHandler(stream_handler)
logger.addHandler(file_handler)

try:
    PORT = config.getint("PORTS", "UPLOAD_WORKER_PORT")
except (Exception,):
    PORT = int(os.environ.get("PORT", 1011))

try:
    ISPRODUCTION = config.getboolean("Data", "ISPRODUCTION")
except (Exception,):
    ISPRODUCTION = str(os.environ.get("PRODUCTION", "False")).lower() == "true"

print(ISPRODUCTION)

MiddlewareBaseApiUrl = config.get("ProductionApi", "MiddlewareBaseUrl") if ISPRODUCTION else config.get("TestingAPI", "MiddlewareBaseUrl")

from client.com.controller.RequestController import StockSheetProcessor
processor = StockSheetProcessor()
processor.start()
# import client.com.handler
