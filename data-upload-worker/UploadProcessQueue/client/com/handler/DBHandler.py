from client import config, pd
from pymongo import MongoClient
from client.com.handler.LoggerHandler import LoggerHandler

log = LoggerHandler()
URL = config.get('MongoDB', 'URL')
DATABASE_NAME = config.get('MongoDB', 'DATABASE_NAME')


def get_collection_names():
    log.request_logger.info("Getting Collection")
    client = MongoClient(URL)
    db = client[DATABASE_NAME]
    collection_names = db.list_collection_names()

    client.close()
    return collection_names


def read_data_from_mongo(coll_name):
    log.request_logger.info(f"Read Data from Mongodb {coll_name}")
    client = MongoClient(URL)
    db = client[DATABASE_NAME]
    collection = db[coll_name]  # Access the specified collection
    data = list(collection.find())
    client.close()
    product_df = pd.DataFrame(data)
    if "_id" in product_df.columns:
        product_df["_id"] = product_df["_id"].astype(str)  # Convert "_id" to string if needed
    return product_df


def delete_collection(collection_name):
    client = MongoClient(URL)
    db = client[DATABASE_NAME]
    db.drop_collection(collection_name)  # Deletes the collection from MongoDB
    log.request_logger.info(f"Collection {collection_name} deleted successfully.")



"""
data-upload-worker/
├── shared/                          # Shared components
│   ├── __init__.py
│   ├── core/                        # Core abstractions
│   │   ├── __init__.py
│   │   ├── base_worker.py          # Abstract base worker
│   │   ├── base_processor.py       # Abstract processor
│   │   └── interfaces.py           # Protocol definitions
│   ├── infrastructure/              # Infrastructure concerns
│   │   ├── __init__.py
│   │   ├── queue_manager.py        # RabbitMQ abstraction
│   │   ├── api_client.py           # HTTP client
│   │   ├── file_manager.py         # File operations
│   │   └── database.py             # DB connections
│   ├── config/                      # Configuration management
│   │   ├── __init__.py
│   │   ├── settings.py             # Settings classes
│   │   └── validator.py            # Config validation
│   ├── logging/                     # Centralized logging
│   │   ├── __init__.py
│   │   ├── logger_factory.py       # Logger creation
│   │   └── formatters.py           # Log formatters
│   └── utils/                       # Shared utilities
│       ├── __init__.py
│       ├── exceptions.py           # Custom exceptions
│       └── decorators.py           # Common decorators
├── workers/                         # Specific worker implementations
│   ├── __init__.py
│   ├── price_worker/               # Price-specific logic only
│   │   ├── __init__.py
│   │   ├── processor.py
│   │   └── handlers.py
│   ├── product_worker/             # Product-specific logic only
│   │   ├── __init__.py
│   │   ├── processor.py
│   │   └── handlers.py
│   ├── stock_worker/               # Stock-specific logic only
│   │   ├── __init__.py
│   │   ├── processor.py
│   │   └── handlers.py
│   └── upload_worker/              # Upload-specific logic only
│       ├── __init__.py
│       ├── processor.py
│       └── handlers.py
├── config/                          # Global configuration
│   ├── settings.py
│   ├── config.cfg
│   └── docker-compose.yml
├── tests/                           # Comprehensive test suite
│   ├── __init__.py
│   ├── unit/
│   ├── integration/
│   └── fixtures/
└── scripts/                         # Deployment scripts
    ├── deploy.sh
    └── health_check.py

"""