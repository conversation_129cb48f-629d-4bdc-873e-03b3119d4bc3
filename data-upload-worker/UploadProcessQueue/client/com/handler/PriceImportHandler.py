from client import pyodbc, config, platform, pd
from sqlalchemy import create_engine
from client.com.handler.LoggerHandler import <PERSON><PERSON><PERSON>and<PERSON>
from client.com.handler.RequestHandler import RequestHandler

log = LoggerHandler()
server = config.get("CREDENTIAL", "SERVER")
database = config.get("CREDENTIAL", "DATABASE")
username = config.get("CREDENTIAL", "USERNAME")
password = config.get("CREDENTIAL", "PASSWORD")


class PriceImportHandler(RequestHandler):

    def __init__(self):
        super().__init__()
        self.server = server
        self.database = database
        self.username = username
        self.password = password
        self.conn = None

    def connect_to_database(self):
        try:
            conn_string = f"Driver={{ODBC Driver 17 for SQL Server}};Server={self.server};Database={self.database};uid={self.username};pwd={self.password};"
            print(conn_string)
            self.conn = pyodbc.connect(conn_string)
        except (Exception,):
            conn_string = (
                f"DRIVER={{ODBC Driver 18 for SQL Server}};"
                f"SERVER={self.server};"
                f"DATABASE={self.database};"
                f"UID={self.username};"
                f"PWD={self.password};"
                "Encrypt=yes;TrustServerCertificate=yes;"
            )
            self.conn = pyodbc.connect(conn_string)

    def update_price(self, df):
        try:
            created_date_str = self.CreatedDate.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
            log.request_logger.info(f"Updating data into the product table.")
            self.connect_to_database()

            # Replace NaN with 'Null'
            new_df = df.where(pd.notna(df), 'Null')

            batch_size = 1000
            total_rows = len(new_df)

            cursor = self.conn.cursor()

            for start in range(0, total_rows, batch_size):
                end = min(start + batch_size, total_rows)
                batch_df = new_df.iloc[start:end]

                # Generate CASE statements for each column
                case_statements1 = [
                    f"""WHEN SupplierProductPriceId = {row['SupplierProductPriceId']} THEN {row['Price']}""" if
                    row['Price'] != 'Null' else f"WHEN SupplierProductPriceId = {row['SupplierProductPriceId']} THEN NULL"
                    for _, row in batch_df.iterrows()
                ]
                case_statements2 = [
                    f"""WHEN SupplierProductPriceId = {row['SupplierProductPriceId']} THEN {row['IsDisable']}""" if
                    row['IsDisable'] != 'Null' else f"WHEN SupplierProductPriceId = {row['SupplierProductPriceId']} THEN NULL"
                    for _, row in batch_df.iterrows()
                ]

                # Join the individual CASE statements into full query strings
                price_query = "\n".join(case_statements1)
                disable_query = "\n".join(case_statements2)

                # Create the bulk update query
                bulk_update_query = f'''
                        UPDATE SupplierProductPrices
                        SET
                            Price = CASE
                                {price_query}
                                ELSE Price
                            END,
                            IsDisable = CASE
                                {disable_query}
                                ELSE IsDisable
                            END,
                            UpdatedAt = '{created_date_str}',
                            LastUpdatedAt = '{created_date_str}',
                            UpdatedRequestId = {self.ImportRequestId}
                        WHERE SupplierProductPriceId IN ({", ".join(map(str, batch_df['SupplierProductPriceId']))});
                        '''

                print(bulk_update_query)
                cursor.execute(bulk_update_query)
            self.conn.commit()
            cursor.close()
            self.conn.close()
            return True, f"Successfully updated data into the Price table."
        except Exception as e:
            log.request_logger.error(f"Error updating data into Price table: {e}")
            return False, f"Error updating data into Price table: {e}"

    def insert_into_price_audit(self, df):
        try:
            log.request_logger.info(f"Inserting data into Price Audit table.")

            # Step 1: Remove duplicates based on SupplierId + SupplierCatalogId
            df = df.drop_duplicates(subset=['SupplierProductPriceId', 'Price'])

            new_df = df[['SupplierProductPriceId', 'Price', 'CreatedAt']]
            system = platform.system()
            if not new_df.empty:
                if system == 'Linux':
                    conn_string = (
                        f"DRIVER={{ODBC Driver 18 for SQL Server}};"
                        f"SERVER={self.server};"
                        f"DATABASE={self.database};"
                        f"UID={self.username};"
                        f"PWD={self.password};"
                        "Encrypt=yes;TrustServerCertificate=yes;"
                    )
                    engine = create_engine(f"mssql+pyodbc:///?odbc_connect={conn_string}")
                elif system == 'Windows':
                    conn_string = f"Driver={{ODBC Driver 17 for SQL Server}};Server={self.server};Database={self.database};uid={self.username};pwd={self.password};"
                    engine = create_engine(f"mssql+pyodbc:///?odbc_connect={conn_string}")
                batch_size = 1000
                new_df.to_sql('SupplierProductPriceAudits', con=engine, index=False, if_exists='append', schema='dbo', chunksize=batch_size)
            return True, f"Successfully inserted data into Price Audit table."
        except Exception as e:
            log.request_logger.error(f"Error inserting Price Audit data: {e}")
            return False, f"Error inserting Price Audit data: {e}"

    def insert_into_price(self, df):
        try:
            df['CreatedRequestId'] = self.ImportRequestId
            df['CreatedAt'] = self.CreatedDate
            df['LastUpdatedAt'] = self.CreatedDate
            log.request_logger.info(f"Inserting data into the Price table.")

            # Step 1: Remove duplicates based on SupplierId + SupplierCatalogId
            df = df.drop_duplicates(subset=['SupplierProductId', 'SizeUnitId', 'Price', 'Size'])

            new_df = df[['SupplierProductId', 'SizeUnitId', 'Price', 'Size', 'CreatedAt', 'LastUpdatedAt', 'IsDisable', 'CreatedRequestId']]
            system = platform.system()
            if not new_df.empty:
                if system == 'Linux':
                    conn_string = (
                        f"DRIVER={{ODBC Driver 18 for SQL Server}};"
                        f"SERVER={self.server};"
                        f"DATABASE={self.database};"
                        f"UID={self.username};"
                        f"PWD={self.password};"
                        "Encrypt=yes;TrustServerCertificate=yes;"
                    )
                    engine = create_engine(f"mssql+pyodbc:///?odbc_connect={conn_string}")
                elif system == 'Windows':
                    conn_string = f"Driver={{ODBC Driver 17 for SQL Server}};Server={self.server};Database={self.database};uid={self.username};pwd={self.password};"
                    engine = create_engine(f"mssql+pyodbc:///?odbc_connect={conn_string}")
                batch_size = 1000
                new_df.to_sql('SupplierProductPrices', con=engine, index=False, if_exists='append', schema='dbo', chunksize=batch_size)
            return True, f"Successfully inserted data into the Price table."
        except Exception as e:
            log.request_logger.error(f"Error inserting data into Price table: {e}")
            return False, f"Error inserting data into Price table: {e}"
