from client import pyodbc, config, platform, pd
from sqlalchemy import create_engine
from client.com.handler.LoggerHandler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from client.com.handler.RequestHandler import RequestHandler

log = LoggerHandler()
server = config.get("CREDENTIAL", "SERVER")
database = config.get("CREDENTIAL", "DATABASE")
username = config.get("CREDENTIAL", "USERNAME")
password = config.get("CREDENTIAL", "PASSWORD")


class MainProductImportHandler(RequestHandler):

    def __init__(self):
        super().__init__()
        self.server = server
        self.database = database
        self.username = username
        self.password = password
        self.conn = None

    def connect_to_database(self):
        try:
            conn_string = f"Driver={{ODBC Driver 17 for SQL Server}};Server={self.server};Database={self.database};uid={self.username};pwd={self.password};"
            print(conn_string)
            self.conn = pyodbc.connect(conn_string)
        except (Exception,):
            conn_string = (
                f"DRIVER={{ODBC Driver 18 for SQL Server}};"
                f"SERVER={self.server};"
                f"DATABASE={self.database};"
                f"UID={self.username};"
                f"PWD={self.password};"
                "Encrypt=yes;TrustServerCertificate=yes;"
            )
            self.conn = pyodbc.connect(conn_string)

    def insert_into_mp(self, df):
        try:
            df.drop_duplicates(subset='CI_ChemProductId', keep='first', inplace=True)
            df['CreatedAt'] = self.CreatedDate
            log.request_logger.info(f"Inserting data into the main product table.")
            if 'CI_ChemProductId' not in df.columns:
                df['CI_ChemProductId'] = 0
            new_df = df[['CI_ChemProductId', 'CreatedAt']]

            system = platform.system()
            if not new_df.empty:
                if system == 'Linux':
                    conn_string = (
                        f"DRIVER={{ODBC Driver 18 for SQL Server}};"
                        f"SERVER={self.server};"
                        f"DATABASE={self.database};"
                        f"UID={self.username};"
                        f"PWD={self.password};"
                        "Encrypt=yes;TrustServerCertificate=yes;"
                    )
                    engine = create_engine(f"mssql+pyodbc:///?odbc_connect={conn_string}")
                elif system == 'Windows':
                    conn_string = f"Driver={{ODBC Driver 17 for SQL Server}};Server={self.server};Database={self.database};uid={self.username};pwd={self.password};"
                    print(conn_string)
                    engine = create_engine(f"mssql+pyodbc:///?odbc_connect={conn_string}")
                batch_size = 1000
                new_df.to_sql('MainProducts', con=engine, index=False, if_exists='append', schema='dbo', chunksize=batch_size)
            return True, f"Successfully inserted data into the main product table."
        except Exception as e:
            log.request_logger.error(f"Error inserting data into main product table: {e}")
            return False, f"Error inserting data into main product table: {e}"
