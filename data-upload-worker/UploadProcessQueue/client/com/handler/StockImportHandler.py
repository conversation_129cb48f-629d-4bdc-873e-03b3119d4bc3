from client import pyodbc, config, platform, pd
from sqlalchemy import create_engine
from client.com.handler.LoggerHandler import <PERSON><PERSON><PERSON>and<PERSON>
from client.com.handler.RequestHandler import RequestHandler

log = LoggerHandler()
server = config.get("CREDENTIAL", "SERVER")
database = config.get("CREDENTIAL", "DATABASE")
username = config.get("CREDENTIAL", "USERNAME")
password = config.get("CREDENTIAL", "PASSWORD")


class StockImportHandler(RequestHandler):

    def __init__(self):
        super().__init__()
        self.server = server
        self.database = database
        self.username = username
        self.password = password
        self.conn = None

    def connect_to_database(self):
        try:
            conn_string = f"Driver={{ODBC Driver 17 for SQL Server}};Server={self.server};Database={self.database};uid={self.username};pwd={self.password};"
            print(conn_string)
            self.conn = pyodbc.connect(conn_string)
        except (Exception,):
            conn_string = (
                f"DRIVER={{ODBC Driver 18 for SQL Server}};"
                f"SERVER={self.server};"
                f"DATABASE={self.database};"
                f"UID={self.username};"
                f"PWD={self.password};"
                "Encrypt=yes;TrustServerCertificate=yes;"
            )
            self.conn = pyodbc.connect(conn_string)

    def update_stock(self, df):
        try:
            created_date_str = self.CreatedDate.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
            log.request_logger.info(f"Updating data into the product table.")
            self.connect_to_database()

            # Replace NaN with 'Null'
            new_df = df.where(pd.notna(df), 'Null')

            batch_size = 1000
            total_rows = len(new_df)

            cursor = self.conn.cursor()

            for start in range(0, total_rows, batch_size):

                end = min(start + batch_size, total_rows)
                batch_df = new_df.iloc[start:end]

                # Generate CASE statements for each column
                case_statements1 = [
                    f"""WHEN SupplierProductStockId = {row['SupplierProductStockId']} THEN {row['StockAmount']}""" if
                    row['StockAmount'] != 'Null' else f"WHEN SupplierProductStockId = {row['SupplierProductStockId']} THEN NULL"
                    for _, row in batch_df.iterrows()
                ]
                case_statements2 = [
                    f"""WHEN SupplierProductStockId = {row['SupplierProductStockId']} THEN {row['IsDisable']}""" if
                    row['IsDisable'] != 'Null' else f"WHEN SupplierProductStockId = {row['SupplierProductStockId']} THEN NULL"
                    for _, row in batch_df.iterrows()
                ]
                case_statements3 = [
                    f"""WHEN SupplierProductStockId = {row['SupplierProductStockId']} THEN {row['SizeUnitId']}""" if
                    row['SizeUnitId'] != 'Null' else f"WHEN SupplierProductStockId = {row['SupplierProductStockId']} THEN NULL"
                    for _, row in batch_df.iterrows()
                ]

                # Join the individual CASE statements into full query strings
                quantity_query = "\n".join(case_statements1)
                disable_query = "\n".join(case_statements2)
                size_query = "\n".join(case_statements3)

                # Create the bulk update query
                bulk_update_query = f'''
                        UPDATE SupplierProductStocks
                        SET
                            StockAmount = CASE
                                {quantity_query}
                                ELSE StockAmount
                            END,
                            SizeUnitId = CASE
                                {size_query}
                                ELSE SizeUnitId
                            END,
                            IsDisable = CASE
                                {disable_query}
                                ELSE IsDisable
                            END,
                            UpdatedAt = '{created_date_str}',
                            LastUpdatedAt = '{created_date_str}',
                            UpdatedRequestId = {self.ImportRequestId}
                        WHERE SupplierProductStockId IN ({", ".join(map(str, batch_df['SupplierProductStockId']))});
                        '''

                print(bulk_update_query)
                cursor.execute(bulk_update_query)
            self.conn.commit()
            cursor.close()
            self.conn.close()
            return True, f"Successfully updated data into the product table."
        except Exception as e:
            log.request_logger.error(f"Error updating data into product table: {e}")
            return False, f"Error updating data into product table: {e}"

    def insert_into_stock_audit(self, df):
        try:
            log.request_logger.info(f"Inserting data into Stock Audit table.")

            # Step 1: Remove duplicates based on SupplierId + SupplierCatalogId
            df = df.drop_duplicates(
                subset=['SupplierProductStockId', 'StockAmount'])

            new_df = df[['SupplierProductStockId', 'StockAmount', 'CreatedAt']]
            system = platform.system()
            if not new_df.empty:
                if system == 'Linux':
                    conn_string = (
                        f"DRIVER={{ODBC Driver 18 for SQL Server}};"
                        f"SERVER={self.server};"
                        f"DATABASE={self.database};"
                        f"UID={self.username};"
                        f"PWD={self.password};"
                        "Encrypt=yes;TrustServerCertificate=yes;"
                    )
                    engine = create_engine(f"mssql+pyodbc:///?odbc_connect={conn_string}")
                elif system == 'Windows':
                    conn_string = f"Driver={{ODBC Driver 17 for SQL Server}};Server={self.server};Database={self.database};uid={self.username};pwd={self.password};"
                    engine = create_engine(f"mssql+pyodbc:///?odbc_connect={conn_string}")
                batch_size = 1000
                new_df.to_sql('SupplierProductStockAudits', con=engine, index=False, if_exists='append', schema='dbo', chunksize=batch_size)
            return True, f"Successfully inserted data into Stock Audit table."
        except Exception as e:
            log.request_logger.error(f"Error inserting Stock Audit data: {e}")
            return False, f"Error inserting Stock Audit data: {e}"

    def insert_into_stock(self, df):
        try:
            df['CreatedRequestId'] = self.ImportRequestId
            df['CreatedAt'] = self.CreatedDate
            df['LastUpdatedAt'] = self.CreatedDate
            log.request_logger.info(f"Inserting data into the Stock table.")

            # Step 1: Remove duplicates based on SupplierId + SupplierCatalogId
            df = df.drop_duplicates(subset=['SupplierProductId', 'SizeUnitId', 'StockAmount', 'WarehouseId', 'WarehouseName'])

            new_df = df[['SupplierProductId', 'SizeUnitId', 'StockAmount', 'WarehouseId', 'WarehouseName', 'CreatedAt', 'LastUpdatedAt', 'IsDisable', 'CreatedRequestId']]
            new_df = new_df.rename(columns={"WarehouseId": "OmsEchemPortalWarehouseID",
                                            "WarehouseName": "OmsEchemPortalWarehouseName"})
            system = platform.system()
            if not new_df.empty:
                if system == 'Linux':
                    conn_string = (
                        f"DRIVER={{ODBC Driver 18 for SQL Server}};"
                        f"SERVER={self.server};"
                        f"DATABASE={self.database};"
                        f"UID={self.username};"
                        f"PWD={self.password};"
                        "Encrypt=yes;TrustServerCertificate=yes;"
                    )
                    engine = create_engine(f"mssql+pyodbc:///?odbc_connect={conn_string}")
                elif system == 'Windows':
                    conn_string = f"Driver={{ODBC Driver 17 for SQL Server}};Server={self.server};Database={self.database};uid={self.username};pwd={self.password};"
                    engine = create_engine(f"mssql+pyodbc:///?odbc_connect={conn_string}")
                batch_size = 1000
                new_df.to_sql('SupplierProductStocks', con=engine, index=False, if_exists='append', schema='dbo', chunksize=batch_size)
            return True, f"Successfully inserted data into the Stock table."
        except Exception as e:
            log.request_logger.error(f"Error inserting data into Stock table: {e}")
            return False, f"Error inserting data into Stock table: {e}"

    def update_spd_availability(self, df):
        try:
            log.request_logger.info("Updating data into the product table AvailabilityId.")
            self.connect_to_database()
            cursor = self.conn.cursor()
            batch_size = 1000
            total_rows = len(df)

            for start in range(0, total_rows, batch_size):
                end = min(start + batch_size, total_rows)
                batch_df = df.iloc[start:end]
                supplier_ids = batch_df['SupplierProductId'].unique().tolist()

                if not supplier_ids:
                    continue

                if 'IsDisable' not in batch_df.columns:
                    # No IsDisable column — default all to AvailabilityId = 1
                    product_ids_str = ", ".join(str(_id) for _id in supplier_ids)
                    update_query = f'''
                        UPDATE [dbo].[SupplierProducts]
                        SET AvailabilityId = 1
                        WHERE SupplierProductId IN ({product_ids_str})
                    '''
                    cursor.execute(update_query)
                else:
                    # Get all SupplierProductStocks rows for this batch in one query
                    placeholders = ", ".join("?" for _ in supplier_ids)
                    select_query = f'''
                        SELECT SupplierProductId, IsDisable FROM [dbo].[SupplierProductStocks]
                        WHERE SupplierProductId IN ({placeholders})
                    '''
                    cursor.execute(select_query, supplier_ids)
                    rows = cursor.fetchall()

                    # Group by SupplierProductId
                    from collections import defaultdict
                    spd_status_map = defaultdict(list)
                    for spid, isdisable in rows:
                        spd_status_map[spid].append(isdisable)

                    # Now split into two groups
                    avail_1_ids = []
                    avail_3_ids = []

                    for spid, values in spd_status_map.items():
                        if any(v in (0, None) for v in values):
                            avail_1_ids.append(spid)
                        elif all(v == 1 for v in values):
                            avail_3_ids.append(spid)

                    # Update both groups
                    if avail_1_ids:
                        ids_str = ", ".join(str(i) for i in avail_1_ids)
                        update_query = f'''
                            UPDATE [dbo].[SupplierProducts]
                            SET AvailabilityId = 1
                            WHERE SupplierProductId IN ({ids_str})
                        '''
                        cursor.execute(update_query)

                    if avail_3_ids:
                        ids_str = ", ".join(str(i) for i in avail_3_ids)
                        update_query = f'''
                            UPDATE [dbo].[SupplierProducts]
                            SET AvailabilityId = 3
                            WHERE SupplierProductId IN ({ids_str})
                        '''
                        cursor.execute(update_query)

            self.conn.commit()
            cursor.close()
            self.conn.close()
            return True, "Successfully updated AvailabilityId based on SupplierProductStocks."
        except Exception as e:
            log.request_logger.error(f"Error updating AvailabilityId: {e}")
            return False, f"Error updating AvailabilityId: {e}"
