from client import thisfolder, os, config, pd
from client.com.handler.ApiHandler import ApiHandler


class RequestHandler(ApiHandler):

    def __init__(self):
        super().__init__()
        self.initialize_request()  # Initialize variables at the start
        self.request_logger = None
        self.ImportRequestId = None
        self.SupplierID = None
        self.SupplierName = None
        self.CollectionName = ""
        self.Dbname = ""
        self.RequestTypeId = None
        self.Error = ""
        self.InputFileName = ""
        self.Status = "Initiate"
        self.SendModuleType = "UploadProcess"
        self.TimeTaken = ""
        self.CreatedDate = ""
        self.RequestOutputFiles = []
        self.Script = None

    def initialize_request(self):
        """Reset all attributes to their default values before a new request."""
        self.ImportRequestId = None
        self.SupplierID = None
        self.SupplierName = None
        self.CollectionName = ""
        self.Dbname = ""
        self.RequestTypeId = None
        self.Error = ""
        self.InputFileName = ""
        self.Status = "Initiate"
        self.SendModuleType = "UploadProcess"
        self.TimeTaken = ""
        self.CreatedDate = ""
        self.RequestOutputFiles = []
        self.Script = None

    def update_request_status(self, stage_id: int, sub_stage: int = None):
        try:
            if self.Status == 'Inprogress':
                status_id = 3
            elif self.Status == 'Finished':
                status_id = 4
            else:
                status_id = 5
            update_res = self.update_request(import_id=self.ImportRequestId, status_id=status_id, stage_id=stage_id,
                                             sub_stage_id=sub_stage)

            if not self.validate_response(update_res):
                self.request_logger.error(f"There is error during UpdateRequest_API {update_res.__dict__}")
                self.Status = "Canceled"
                self.Error = f"There is error during UpdateRequest_API {update_res.__dict__}"
        except Exception as e:
            self.request_logger.error(e, exc_info=True)

    def validate_response(self, res):
        if res is not None:
            if res.status_code == 200:
                return True
            else:
                self.request_logger.error(res.__dict__)
                return False
        else:
            return False

    def error_log(self, logs: str):
        try:
            self.add_log(import_id=self.ImportRequestId, com_name="UploadingProcess",
                         log_type="ERROR", logs=logs)
        except Exception as e:
            self.request_logger.error(e, exc_info=True)

    def info(self, logs: str):
        try:
            self.add_log(import_id=self.ImportRequestId, com_name="UploadingProcess",
                         log_type="INFO", logs=logs)
        except Exception as e:
            self.request_logger.error(e, exc_info=True)

    def result(self, logs: str):
        try:
            self.add_log(import_id=self.ImportRequestId, com_name="UploadingProcess",
                         log_type="RESULT", logs=logs)
        except Exception as e:
            self.request_logger.error(e, exc_info=True)
