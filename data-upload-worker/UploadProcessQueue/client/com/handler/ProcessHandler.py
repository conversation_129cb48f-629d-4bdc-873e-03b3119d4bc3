from client.com.handler.UploadHandler import Upload<PERSON>andler
from client.com.handler.ApiHandler import <PERSON><PERSON><PERSON>andler
from client.com.handler.LoggerHandler import get_logger
from client import datetime

api_handler = ApiHandler()


class ProcessHandler(UploadHandler):

    def __init__(self):
        super().__init__()

    def start_process(self, data):
        try:
            self.initialize_request()  # Initialize variables at the start
            print(f"{data = }")
            self.SupplierName = data["SupplierName"]
            self.SupplierID = data["SupplierID"]
            self.ImportRequestId = data["ImportRequestId"]
            self.RequestTypeId = data["RequestTypeId"]
            created_date = data.get("CreatedDate")
            self.CreatedDate = datetime.fromisoformat(created_date)
            self.request_logger = get_logger(self.ImportRequestId)
            self.request_logger.info(f"Starting Uploading Process")
            self.info(logs=f"Starting Uploading Process")
            response_data = self.start_importing_process()
            return response_data
        except Exception as e:
            self.request_logger.error(e, exc_info=True)
            return
