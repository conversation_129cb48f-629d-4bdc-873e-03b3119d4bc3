from client import pika, config, logger
from client.com.handler.ProcessHandler import ProcessHandler
import json
import time

rabbit_mq = config.get('Data', 'RABBITMQ')


class StockSheetProcessor(ProcessHandler):

    def __init__(self):
        super().__init__()
        self.connection = None
        self.channel = None

    def establish_connection(self, max_retries=None):
        """Establish a new connection."""
        retry_count = 0
        while True:
            try:
                logger.info(f"Connecting to RabbitMQ server at {rabbit_mq}...")

                connection_params = pika.URLParameters(rabbit_mq)

                # Additional connection settings
                connection_params.heartbeat = 30000
                connection_params.blocked_connection_timeout = 30000
                connection_params.retry_delay = 5
                connection_params.connection_attempts = 5
                
                # Establish the connection with the specified parameters
                self.connection = pika.BlockingConnection(connection_params)

                logger.info("Connected to RabbitMQ server.")
                self.channel = self.connection.channel()
                return
            except pika.exceptions.AMQPConnectionError as e:
                retry_count += 1
                if max_retries is not None and retry_count > max_retries:
                    raise Exception("Maximum retry limit reached. Unable to connect to RabbitMQ.") from e
                logger.info(f"Connection error: {e}. Retrying in 5 seconds... (Attempt {retry_count})")
                time.sleep(5)

    def process_task(self, task):
        """Simulate processing of a task."""
        logger.info(f"Processing Upload Task: {task}")
        self.info(logs=f"Processing Upload Task: {task}")
        response_data = self.start_process(task)
        return response_data

    def on_request(self, ch, method, properties, body):
        """Callback for incoming tasks."""
        try:

            task = json.loads(body)
            logger.info(f"Received task: {task}")
            response = self.process_task(task)

            # Publish the response to the response queue
            self.channel.basic_publish(
                exchange='',
                routing_key='response_queue',
                body=json.dumps(response)
            )
            logger.info(f"Response sent: {response}")

            # Acknowledge the request message
            ch.basic_ack(delivery_tag=method.delivery_tag)
        except Exception as e:
            logger.error(f"Error in on_request: {e}")
            ch.basic_ack(delivery_tag=method.delivery_tag)  # Negative acknowledgment for requeueing the message

    def start(self):
        """Start listening for tasks."""
        while True:
            try:
                self.establish_connection()

                # Declare the request queue
                logger.info(f"Declaring queue upload_queue...")
                self.channel.queue_declare(queue='upload_queue', durable=True)

                logger.info(f"Waiting for tasks from queue upload_queue...")
                self.channel.basic_consume(queue='upload_queue', on_message_callback=self.on_request, auto_ack=False)
                logger.info("UploadProcessor running...")

                # Start consuming messages
                self.channel.start_consuming()
            except pika.exceptions.AMQPConnectionError as e:
                logger.info(f"Connection lost: {e}. Reconnecting...")
            except KeyboardInterrupt:
                logger.error("Consumer interrupted by user. Closing connection...")
                break
            except Exception as e:
                logger.error(f"Unexpected error: {e}")
            finally:
                if self.connection and not self.connection.is_closed:
                    logger.info("Closing RabbitMQ connection...")
                    self.connection.close()
