# Use the official small size Python 3.10-slim base image as runtime
FROM python:3.10-slim AS runtime

# Set the working directory to /app within the container
WORKDIR /app

# Copy only the necessary files for installing Python dependencies
COPY requirements.txt .

# Install Python dependencies from requirements.txt, skipping cache
RUN pip install --no-cache-dir -r requirements.txt

# Install dependencies in a single layer and remove unnecessary files
RUN apt-get update && \
    apt-get install -y --no-install-recommends curl ca-certificates gnupg && \
    curl -sSL https://packages.microsoft.com/keys/microsoft.asc | apt-key add - && \
    curl -sSL https://packages.microsoft.com/config/debian/10/prod.list -o /etc/apt/sources.list.d/mssql-release.list && \
    apt-get update && \
    ACCEPT_EULA=Y apt-get install -y --no-install-recommends msodbcsql18 && \
    apt-get purge -y curl gnupg && \
    apt-get autoremove -y && \
    rm -rf /var/lib/apt/lists/* /etc/apt

# Copy the entire application code into the container
COPY . .

# Define the command to run when the container starts
CMD ["python", "app.py"]