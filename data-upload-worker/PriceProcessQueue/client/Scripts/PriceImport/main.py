import sys
import re
import math
import pandas as pd
import pyodbc
import warnings
from loggerfile import logger
from config_manager import ConfigManager
from mongodb import read_data_from_mongo, insert_data_into_mongodb


class DataProcessor:
    def __init__(self, server, database, username, password):
        warnings.filterwarnings("ignore")
        self.initialize_request()
        self.dbname = sys.argv[1]
        self.server = server
        self.database = database
        self.username = username
        self.password = password
        self.conn = None
        self.Req_id = sys.argv[5]
        self.SelectedDate = sys.argv[6]
        self.Type = int(sys.argv[4])
        self.price_df = pd.DataFrame()
        self.spp_df = pd.DataFrame()
        self.match_price_df = pd.DataFrame()
        self.cat_match_in_spd = pd.DataFrame()
        self.not_match_cat_in_spd = pd.DataFrame()
        self.product_id_match_in_spp = pd.DataFrame()
        self.product_id_not_match_in_spp = pd.DataFrame()
        self.size_df = pd.DataFrame()

    def initialize_request(self):
        self.Req_id = sys.argv[5]
        self.SelectedDate = sys.argv[6]
        self.Type = int(sys.argv[4])
        self.price_df = pd.DataFrame()
        self.spp_df = pd.DataFrame()
        self.match_price_df = pd.DataFrame()
        self.cat_match_in_spd = pd.DataFrame()
        self.not_match_cat_in_spd = pd.DataFrame()
        self.product_id_match_in_spp = pd.DataFrame()
        self.product_id_not_match_in_spp = pd.DataFrame()
        self.size_df = pd.DataFrame()

    def connect_to_database(self):
        try:
            conn_string = f"Driver={{SQL Server}};Server={self.server};Database={self.database};uid={self.username};pwd={self.password};"
            self.conn = pyodbc.connect(conn_string)
        except (Exception,):
            conn_string = (
                f"DRIVER={{ODBC Driver 18 for SQL Server}};"
                f"SERVER={self.server};"
                f"DATABASE={self.database};"
                f"UID={self.username};"
                f"PWD={self.password};"
                "Encrypt=yes;TrustServerCertificate=yes;"
            )
            self.conn = pyodbc.connect(conn_string)

    def read_data_from_supplier_product(self, total_cat_price_df):
        logger.info("read_data_from_supplier_product")
        self.connect_to_database()

        # Extract catalog IDs from product_df
        catalog_ids = total_cat_price_df['SupplierCatalogId'].tolist()
        supplier_index = total_cat_price_df['SupplierId'].first_valid_index()
        supplier_id = total_cat_price_df.at[supplier_index, 'SupplierId'] if supplier_index is not None else None

        batch_size = 1000
        total_ids = len(catalog_ids)
        total_batches = math.ceil(total_ids / batch_size)

        result_df = pd.DataFrame()

        for i in range(total_batches):
            batch_ids = catalog_ids[i * batch_size: (i + 1) * batch_size]
            batch_ids_str = ", ".join(f"'{str(id)}'" for id in batch_ids)
            query = f"""SELECT * FROM [dbo].[SupplierProducts]
                            WHERE SupplierCatalogId IN ({batch_ids_str})
                            and OmsEchemPortalSupplierId = {supplier_id}"""

            batch_df = pd.read_sql(query, self.conn)
            result_df = pd.concat([result_df, batch_df], ignore_index=True)
            logger.info(f"Processed batch {i + 1}/{total_batches} - {len(batch_df)} records fetched.")

        self.cat_match_in_spd = result_df

        # Merge the two dataframes to find matching rows and include SupplierProductID from cat_match_in_spd
        exists_spd_df = total_cat_price_df.merge(self.cat_match_in_spd[['SupplierCatalogId', 'SupplierProductId']],
                                                 on='SupplierCatalogId', how='inner')
        self.not_match_cat_in_spd = total_cat_price_df[
            ~total_cat_price_df['SupplierCatalogId'].isin(self.cat_match_in_spd['SupplierCatalogId'])]

        self.conn.close()
        return exists_spd_df

    def read_data_from_supplier_product_price(self, exists_price):
        logger.info("read_data_from_supplier_product_price")
        self.connect_to_database()
        batch_size = 1000
        catalog_ids = exists_price['SupplierProductId'].tolist()
        total_ids = len(catalog_ids)
        total_batches = math.ceil(total_ids / batch_size)
        result_df = pd.DataFrame()

        for i in range(total_batches):
            batch_ids = catalog_ids[i * batch_size: (i + 1) * batch_size]
            batch_ids_str = ", ".join(f"'{str(id)}'" for id in batch_ids)

            query = f"""SELECT spp.*, su.SizeUnit
                                FROM [dbo].[SupplierProductPrices] spp
                                INNER JOIN [dbo].[SizeUnits] su ON spp.SizeUnitId = su.SizeUnitId
                                WHERE SupplierProductId IN ({batch_ids_str})"""
            batch_df = pd.read_sql(query, self.conn)
            result_df = pd.concat([result_df, batch_df], ignore_index=True)
            logger.info(f"Processed batch {i + 1}/{total_batches}")
        self.conn.close()
        self.spp_df = result_df

    def read_data_from_size(self):
        logger.info("read_data_from_size")
        self.connect_to_database()
        query = 'select * from SizeUnits'
        self.size_df = pd.read_sql(query, self.conn)
        self.conn.close()

    def map_product_id_in_spp(self, exists_price):
        logger.info("map_product_id_in_spp")

        self.product_id_match_in_spp = exists_price.merge(
            self.spp_df[['SupplierProductId', 'LastUpdatedAt']],
            on='SupplierProductId',
            how='inner'
        )
        self.product_id_match_in_spp = self.product_id_match_in_spp.sort_values("LastUpdatedAt", ascending=False)
        self.product_id_match_in_spp = self.product_id_match_in_spp.drop_duplicates(
            subset=['SupplierProductId', 'Price', 'Size', 'Unit'],
            keep='first'
        )
        # self.product_id_match_in_spp = exists_price[
        #     exists_price['SupplierProductId'].isin(self.spp_df['SupplierProductId'])]
        self.product_id_not_match_in_spp = exists_price[
            ~exists_price['SupplierProductId'].isin(self.spp_df['SupplierProductId'])]

    def check_price_changes(self, audit_only=False):
        logger.info("check_price_changes")

        # Convert columns to appropriate types
        self.product_id_match_in_spp['Size'] = pd.to_numeric(self.product_id_match_in_spp['Size'], errors='coerce')
        self.spp_df['Size'] = pd.to_numeric(self.spp_df['Size'], errors='coerce')
        self.product_id_match_in_spp['Price'] = self.product_id_match_in_spp['Price'].astype(float)
        self.spp_df['Price'] = self.spp_df['Price'].astype(float)
        self.product_id_match_in_spp['Unit'] = self.product_id_match_in_spp['Unit'].astype(str)
        self.spp_df['SizeUnit'] = self.spp_df['SizeUnit'].astype(str)

        # Debug: Check cleaned inputs
        print("Sorted product_id_match_in_spp:")
        print(self.product_id_match_in_spp[['SupplierProductId', 'Size', 'Unit', 'Price']])
        print("Sorted spp_df:")
        print(self.spp_df[['SupplierProductId', 'Size', 'SizeUnit', 'Price']])

        # Merge on SupplierProductId + Size + Unit <=> SizeUnit
        merged_df = pd.merge(
            self.product_id_match_in_spp,
            self.spp_df,
            left_on=['SupplierProductId', 'Unit', 'Size'],
            right_on=['SupplierProductId', 'SizeUnit', 'Size'],
            how='left',
            suffixes=('_product', '_spp')
        )

        # Map SizeUnitId using size_df
        size_df = self.size_df[['SizeUnit', 'SizeUnitId']].set_index('SizeUnit')
        merged_df['SizeUnitId'] = merged_df['Unit'].map(size_df['SizeUnitId'])

        # Detect price changes
        price_changed_mask = (
                                     round(merged_df['Price_product'], 2) != round(merged_df['Price_spp'], 2)
                             ) & (~merged_df['Price_spp'].isna())  # Exclude completely new records

        # Detect new price-size entries (not matched in spp_df)
        new_price_size_mask = merged_df['Price_spp'].isna()

        # Price Change DF
        price_change_df = merged_df[price_changed_mask].copy()
        price_change_df = price_change_df[
            ['SupplierProductPriceId', 'SupplierProductId', 'SupplierId', 'SizeUnitId', 'Price_product', 'Size',
             'SupplierCatalogId']
        ]
        price_change_df.columns = ['SupplierProductPriceId', 'SupplierProductId', 'SupplierId', 'SizeUnitId', 'Price',
                                   'Size', 'SupplierCatalogId']
        price_change_df['IsDisable'] = 0

        # Audit Price DF
        if audit_only:
            # Only matched rows based on SupplierProductId + Unit <=> SizeUnit + Size
            matched_audit_df = pd.merge(
                self.product_id_match_in_spp,
                self.spp_df,
                left_on=['SupplierProductId', 'Unit', 'Size'],
                right_on=['SupplierProductId', 'SizeUnit', 'Size'],
                how='inner'
            )

            audit_price_df = matched_audit_df[[
                'SupplierProductId', 'Price_x', 'SupplierCatalogId',  # 'Price_x' from product_id_match_in_spp
            ]].copy()
            audit_price_df.rename(columns={'Price_x': 'Price'}, inplace=True)
            audit_price_df['CreatedAt'] = pd.to_datetime(self.SelectedDate)
            audit_price_df['SupplierProductPriceId'] = matched_audit_df['SupplierProductPriceId']
        else:
            audit_price_df = merged_df[price_changed_mask].copy()
            audit_price_df['Price'] = audit_price_df['Price_spp']
            audit_price_df['CreatedAt'] = audit_price_df['LastUpdatedAt_spp']
            audit_price_df = audit_price_df[
                ['SupplierProductPriceId', 'SupplierProductId', 'Price', 'SupplierCatalogId', 'CreatedAt']
            ]

        # New Price-Size DF
        if audit_only:
            new_price_size_df = merged_df[new_price_size_mask].copy()
            new_price_size_df['IsDisable'] = 1
            new_price_size_df = new_price_size_df[
                ['SupplierCatalogId', 'SupplierProductId', 'SupplierId', 'SizeUnitId', 'Price_product', 'Size', 'IsDisable']
            ]
            new_price_size_df.columns = ['SupplierCatalogId', 'SupplierProductId', 'SupplierId', 'SizeUnitId', 'Price',
                                         'Size', 'IsDisable']
        else:
            new_price_size_df = merged_df[new_price_size_mask].copy()
            new_price_size_df['IsDisable'] = 0
            new_price_size_df = new_price_size_df[
                ['SupplierCatalogId', 'SupplierProductId', 'SupplierId', 'SizeUnitId', 'Price_product', 'Size',
                 'IsDisable']
            ]
            new_price_size_df.columns = ['SupplierCatalogId', 'SupplierProductId', 'SupplierId', 'SizeUnitId', 'Price',
                                         'Size', 'IsDisable']

        # Disable Price DF: In spp_df but not in product_id_match_in_spp
        disable_price_df = self.spp_df[~self.spp_df.set_index(['SupplierProductId', 'Size', 'SizeUnit']).index.isin(
            self.product_id_match_in_spp.set_index(['SupplierProductId', 'Size', 'Unit']).index
        )].copy()
        disable_price_df['IsDisable'] = 1
        disable_price_df.drop(columns=['CreatedAt', 'UpdatedAt', 'UpdatedBy', 'SizeUnit'], inplace=True)

        # Re-enable previously disabled prices
        re_enable_price_df = self.spp_df[
            (self.spp_df['IsDisable'] == 1) &
            self.spp_df.set_index(['SupplierProductId', 'Size', 'SizeUnit']).index.isin(
                self.product_id_match_in_spp.set_index(['SupplierProductId', 'Size', 'Unit']).index
            )
            ].copy()
        re_enable_price_df['IsDisable'] = 0
        re_enable_price_df.drop(columns=['CreatedAt', 'UpdatedAt', 'UpdatedBy', 'SizeUnit'], inplace=True)

        return price_change_df, new_price_size_df, audit_price_df, disable_price_df, re_enable_price_df

    def price_detection(self, check_df):
        logger.info("Detecting Incorrect Prices")

        def convert_size(val):
            if not isinstance(val, str) or not val:
                return None
            alpha_numeric = re.findall(r'[A-Za-z]+|[-\d.]+\d*', val)
            if len(alpha_numeric) < 2:
                return None
            try:
                numeric = float(alpha_numeric[0])
                unit = alpha_numeric[1].upper()
                if unit == 'MG':
                    return f"{numeric}MG"
                elif unit == 'G':
                    return f"{numeric * 1000}MG"
                elif unit == 'KG':
                    return f"{numeric * 1000000}MG"
                return val
            except (ValueError, IndexError):
                return None

        # Combine size and unit then convert
        check_df['TempSize'] = check_df['Size'].astype(str) + check_df['Unit'].astype(str)
        check_df['TempSize'] = check_df['TempSize'].apply(convert_size)
        check_df.dropna(subset=['TempSize'], inplace=True)

        check_df['TempUnit'] = check_df['TempSize'].str.extract(r'([A-Za-z]+)')
        check_df['TempSize'] = check_df['TempSize'].str.replace(r'[A-Za-z]+', '', regex=True).astype(float)

        valid_prices_df = check_df[check_df['Price'] > 0].copy()
        incorrect_catalogs = set()

        # 1. Duplicate sizes in same catalog
        duplicated = valid_prices_df.duplicated(subset=["SupplierCatalogId", "TempSize"], keep=False)
        dup_df = valid_prices_df[duplicated]
        if not dup_df.empty:
            # logger.info("Duplicate sizes found:")
            incorrect_catalogs.update(dup_df['SupplierCatalogId'].unique())

        # 2. Price must increase or stay the same with size (no decrease)
        for catalog_id, catalog_df in valid_prices_df.groupby('SupplierCatalogId'):
            catalog_df = catalog_df.sort_values('TempSize')
            for i in range(1, len(catalog_df)):
                if catalog_df.iloc[i]['Price'] < catalog_df.iloc[i - 1]['Price']:
                    # print(f"Price inconsistency in catalog {catalog_id}")
                    incorrect_catalogs.add(catalog_id)
                    break

        # 3. Different sizes with SAME price within catalog → mark error
        for catalog_id, catalog_df in valid_prices_df.groupby('SupplierCatalogId'):
            # Group by price and count unique sizes for each price
            price_size_counts = catalog_df.groupby('Price')['TempSize'].nunique()
            same_price_multiple_sizes = price_size_counts[price_size_counts > 1]
            if not same_price_multiple_sizes.empty:
                # print(f"Same price for multiple different sizes in catalog {catalog_id}:")
                # print(catalog_df[catalog_df['Price'].isin(same_price_multiple_sizes.index)])
                incorrect_catalogs.add(catalog_id)

        # Collect all rows from incorrect catalogs
        incorrect_df = valid_prices_df[valid_prices_df['SupplierCatalogId'].isin(incorrect_catalogs)].copy()

        # Drop temp columns
        check_df.drop(['TempSize', 'TempUnit'], axis=1, inplace=True)
        incorrect_df.drop(['TempSize', 'TempUnit'], axis=1, inplace=True)

        return check_df.reset_index(drop=True), incorrect_df.reset_index(drop=True)

    def get_conversion_rules(self):
        self.connect_to_database()
        cursor = self.conn.cursor()
        cursor.execute("""
            SELECT 
                suc.FromSizeUnitId,
                suc.ToSizeUnitId,
                suc.Threshold,
                suc.ConversionFactor,
                suc.IsReverse,
                suc.SizeUnitCategoryId,
                fsu.SizeUnit AS FromUnit,
                tsu.SizeUnit AS ToUnit
            FROM 
                SizeUnitConversions suc
            JOIN 
                SizeUnits fsu ON suc.FromSizeUnitId = fsu.SizeUnitId
            JOIN 
                SizeUnits tsu ON suc.ToSizeUnitId = tsu.SizeUnitId
            WHERE 
                suc.IsActive = 1
        """)
        rules = {(row[6], row[7]): {'threshold': row[2], 'factor': row[3], 'reverse': bool(row[4])}
                 for row in cursor.fetchall()}
        self.conn.close()
        return rules

    def read_unit_map_from_unit_mappings(self):
        self.connect_to_database()
        if not self.conn:
            raise pyodbc.DatabaseError("Database connection failed")
        cursor = self.conn.cursor()
        cursor.execute("SELECT Variation, Standard FROM SizeUnitNormalization WHERE IsActive = 1")
        rows = cursor.fetchall()
        if self.conn:
            self.conn.close()
        return {variation.strip().lower(): standard.strip().upper() for variation, standard in rows}

    def normalize_unit(self, unit, unit_map):
        if not isinstance(unit, str):
            return unit

        return unit_map.get(unit.strip().lower(), unit)

    def convert_size_units(self, df):
        conversion_rules = self.get_conversion_rules()
        print(conversion_rules)
        unit_map = self.read_unit_map_from_unit_mappings()

        def convert_row(qty, unit):
            normalized_unit = self.normalize_unit(unit, unit_map)

            for (from_unit, to_unit), rule in conversion_rules.items():
                if normalized_unit == from_unit:
                    threshold, factor, reverse = rule.values()

                    if (not reverse and qty >= threshold) or (reverse and qty < threshold):
                        return qty * factor, to_unit

            return qty, normalized_unit

        df[['Size', 'Unit']] = pd.DataFrame(
            [convert_row(qty, unit) for qty, unit in zip(df['Size'], df['Unit'])],
            index=df.index
        )
        return df

    def process(self):
        self.initialize_request()
        self.price_df = read_data_from_mongo(self.dbname, sys.argv[2])
        input_price_df = self.price_df.copy().drop_duplicates(subset=['SupplierCatalogId', 'Size', 'Unit', 'Price'], keep='last')

        logger.info(f"Input_Data_Count: {len(input_price_df)}")
        input_price_df.to_csv(f"{sys.argv[3]}/{self.Req_id}_Input_Data_Count.csv", index=False)

        if not self.price_df.empty:
            convert_price_df = self.convert_size_units(self.price_df)
            convert_price_df['Size'] = convert_price_df['Size'].astype(float).round(2)
            correct_df, error_price_df = self.price_detection(convert_price_df.copy())
            self.price_df = correct_df[~correct_df['SupplierCatalogId'].isin(error_price_df['SupplierCatalogId'])]

            if not error_price_df.empty:
                error_price_df['Reason'] = 'Error in Price'
                error_price_df = error_price_df[error_price_df['Price'].notna() | (error_price_df['Price'] != '')]
                insert_data_into_mongodb(self.dbname, error_price_df, f'{self.Req_id}_ErrorPriceExcel')

            self.read_data_from_size()
            if self.Type == 3:
                exists_spd_df = self.read_data_from_supplier_product(convert_price_df)
                if not self.not_match_cat_in_spd.empty:
                    not_match_cat_in_spd = self.not_match_cat_in_spd[self.not_match_cat_in_spd['Price'].notna() | (self.not_match_cat_in_spd['Price'] != '')]
                    not_match_cat_in_spd_2 = not_match_cat_in_spd[~not_match_cat_in_spd['SupplierCatalogId'].isin(error_price_df['SupplierCatalogId'])]
                    logger.info('This Catalog is not in Our SPD table')
                    not_match_cat_in_spd_2['Reason'] = 'This Catalog is not in Our SPD table'
                    insert_data_into_mongodb(self.dbname, not_match_cat_in_spd_2, f'{self.Req_id}_PriceProductDoesNotExists')
            else:
                exists_spd_df = read_data_from_mongo(self.dbname, f'{self.Req_id}_ExistsSupplierProduct')

            if not exists_spd_df.empty:
                exists_spd_df_unique = exists_spd_df[['SupplierCatalogId', 'SupplierProductId']].drop_duplicates()
                exists_price = convert_price_df.merge(exists_spd_df_unique, on='SupplierCatalogId', how='inner')
                exists_price.drop_duplicates(subset=['SupplierProductId', 'Size', 'Unit', 'Price'], keep='first', inplace=True)

                if not exists_price.empty:
                    self.read_data_from_supplier_product_price(exists_price)
                    # print(self.spp_df[['SupplierProductPriceId', 'LastUpdatedAt']])
                    self.map_product_id_in_spp(exists_price)
                    # print(self.product_id_match_in_spp[['SupplierProductId', 'Price', 'Size', 'Unit', 'LastUpdatedAt']])

                    selected_date_dt = pd.to_datetime(self.SelectedDate)
                    # Ensure LastUpdatedAt is datetime
                    self.product_id_match_in_spp['LastUpdatedAt'] = pd.to_datetime(self.product_id_match_in_spp['LastUpdatedAt'])

                    # Add a flag column: True if update should happen
                    self.product_id_match_in_spp['ShouldUpdate'] = selected_date_dt > self.product_id_match_in_spp['LastUpdatedAt']

                    # Separate into rows that should be updated vs only audited
                    to_update_df = self.product_id_match_in_spp[self.product_id_match_in_spp['ShouldUpdate'] == True]
                    only_audit_df = self.product_id_match_in_spp[self.product_id_match_in_spp['ShouldUpdate'] == False]

                    # print(f"{selected_date_dt = }")
                    # print(to_update_df[['SupplierProductId', 'Price', 'Size', 'Unit', 'LastUpdatedAt']])
                    # print(only_audit_df[['SupplierProductId', 'Price', 'Size', 'Unit', 'LastUpdatedAt']])

                    logger.info("Exists price check and insert")
                    if not self.product_id_match_in_spp.empty:
                        temp_exists_price = self.product_id_match_in_spp.copy()[self.product_id_match_in_spp.copy()['Price'].notna()]
                        temp_exists_price.to_csv(f'{sys.argv[3]}/{self.Req_id}_Exists_Data_Count.csv', index=False)

                        # Process updates (real price changes + audit)
                        if not to_update_df.empty:
                            self.product_id_match_in_spp = to_update_df.copy()
                            price_change_df, new_price_size_df, audit_price_df, disable_price_df, re_enable_price_df = self.check_price_changes()
                            only_audit_ids = only_audit_df['SupplierProductId'].unique()
                            price_change_df = price_change_df[~price_change_df['SupplierProductId'].isin(only_audit_ids)]
                            new_price_size_df = new_price_size_df[~new_price_size_df['SupplierProductId'].isin(only_audit_ids)]
                            audit_price_df = audit_price_df[~audit_price_df['SupplierProductId'].isin(only_audit_ids)]
                            disable_price_df = disable_price_df[~disable_price_df['SupplierProductId'].isin(only_audit_ids)]
                            re_enable_price_df = re_enable_price_df[~re_enable_price_df['SupplierProductId'].isin(only_audit_ids)]

                            if not price_change_df.empty:
                                price_change_df = price_change_df[~price_change_df['SupplierCatalogId'].isin(error_price_df['SupplierCatalogId'])]
                                insert_data_into_mongodb(self.dbname, price_change_df, f'{self.Req_id}_SupplierProductPricesUpdateTemp')

                            if not disable_price_df.empty:
                                insert_data_into_mongodb(self.dbname, disable_price_df, f'{self.Req_id}_SupplierProductPricesUpdateTemp')

                            if not re_enable_price_df.empty:
                                insert_data_into_mongodb(self.dbname, re_enable_price_df, f'{self.Req_id}_SupplierProductPricesUpdateTemp')

                            if not new_price_size_df.empty:
                                new_price_size_df = new_price_size_df[new_price_size_df['Price'].notna()]
                                new_price_size_df = new_price_size_df[~new_price_size_df['SupplierCatalogId'].isin(error_price_df['SupplierCatalogId'])]
                                insert_data_into_mongodb(self.dbname, new_price_size_df, f'{self.Req_id}_SupplierProductPricesInsertTemp')

                            if not audit_price_df.empty:
                                audit_price_df = audit_price_df[~audit_price_df['SupplierCatalogId'].isin(error_price_df['SupplierCatalogId'])]
                                insert_data_into_mongodb(self.dbname, audit_price_df, f'{self.Req_id}_AuditPriceInsertTemp')

                        # Process audit-only (ShouldUpdate == False)
                        if not only_audit_df.empty:
                            self.product_id_match_in_spp = only_audit_df.copy()
                            _, new_price_size_df, audit_price_df, _, _ = self.check_price_changes(audit_only=True)
                            to_update_ids = to_update_df['SupplierProductId'].unique()
                            new_price_size_df = new_price_size_df[~new_price_size_df['SupplierProductId'].isin(to_update_ids)]
                            audit_price_df = audit_price_df[~audit_price_df['SupplierProductId'].isin(to_update_ids)]

                            if not new_price_size_df.empty:
                                new_price_size_df = new_price_size_df[new_price_size_df['Price'].notna()]
                                new_price_size_df = new_price_size_df[~new_price_size_df['SupplierCatalogId'].isin(error_price_df['SupplierCatalogId'])]
                                insert_data_into_mongodb(self.dbname, new_price_size_df, f'{self.Req_id}_SupplierProductPricesInsertTemp')

                            if not audit_price_df.empty:
                                audit_price_df = audit_price_df[~audit_price_df['SupplierCatalogId'].isin(error_price_df['SupplierCatalogId'])]
                                insert_data_into_mongodb(self.dbname, audit_price_df, f'{self.Req_id}_AuditPriceInsertTemp')

                    logger.info("New price check and insert")
                    self.product_id_not_match_in_spp = self.product_id_not_match_in_spp[
                        self.product_id_not_match_in_spp['Price'].notna() | self.product_id_not_match_in_spp['Size'].notna() |
                        self.product_id_not_match_in_spp['Unit'].notna()]
                    if not self.product_id_not_match_in_spp.empty:
                        self.product_id_not_match_in_spp = self.product_id_not_match_in_spp.merge(self.size_df,
                                                                                                  left_on='Unit',
                                                                                                  right_on='SizeUnit',
                                                                                                  how='left')
                        self.product_id_not_match_in_spp['SizeUnitId'] = self.product_id_not_match_in_spp['SizeUnitId']
                        self.product_id_not_match_in_spp.drop(columns=['SizeUnit'], inplace=True)
                        new_df = self.product_id_not_match_in_spp[
                            ['SupplierCatalogId', 'SupplierId', 'Price', 'Size', 'SizeUnitId', 'SupplierProductId']]
                        final_df = new_df[~new_df['SupplierCatalogId'].isin(error_price_df['SupplierCatalogId'])]
                        final_df['IsDisable'] = 0
                        insert_data_into_mongodb(self.dbname, final_df, f'{self.Req_id}_SupplierProductPricesInsertTemp')

            if self.Type == 1:
                collection_names = [
                    f'{self.Req_id}_SupplierProductDetailsInsertTemp',
                    f'{self.Req_id}_SupplierProductDetailsMappingInsertTemp'
                ]
                new_cat_in_spd_1 = read_data_from_mongo(self.dbname, collection_names[0])
                new_cat_in_spd_2 = read_data_from_mongo(self.dbname, collection_names[1])
                new_cat_in_spd = pd.concat([new_cat_in_spd_1, new_cat_in_spd_2], ignore_index=True)

                if not new_cat_in_spd.empty:
                    self.price_df = self.price_df.drop('_id', axis=1)
                    new_price = self.price_df.merge(new_cat_in_spd[['SupplierCatalogId', '_id']], on='SupplierCatalogId', how='inner')

                    if not new_price.empty:

                        logger.info("New price check and insert")
                        new_price.rename(columns={'_id': 'TempID'}, inplace=True)
                        new_price = new_price.merge(self.size_df, left_on='Unit', right_on='SizeUnit', how='left')
                        new_price['SizeUnitId'] = new_price['SizeUnitId']
                        new_price.drop(columns=['SizeUnit'], inplace=True)
                        new_df = new_price[['SupplierCatalogId', 'SupplierId', 'Price', 'Size', 'SizeUnitId', 'TempID']]
                        final_df = new_df[~new_df['SupplierCatalogId'].isin(error_price_df['SupplierCatalogId'])]
                        final_df = final_df[final_df['Price'].notna()]
                        final_df['IsDisable'] = 0
                        insert_data_into_mongodb(self.dbname, final_df, f'{self.Req_id}_SupplierProductPricesInsertTemp')

                # '''Check Catalog is exists in spd or not'''
                if not exists_spd_df.empty and not new_cat_in_spd.empty:
                    # Merge only when both DataFrames have data
                    merged_df = pd.merge(exists_spd_df, new_cat_in_spd, on='SupplierCatalogId', how='outer')
                    not_price_cat_in_spd = self.price_df[~self.price_df['SupplierCatalogId'].isin(merged_df['SupplierCatalogId'])]
                    not_price_cat_in_spd = not_price_cat_in_spd[not_price_cat_in_spd['Price'].notna() | (not_price_cat_in_spd['Price'] != '')]
                    if not not_price_cat_in_spd.empty:
                        logger.info('This Catalog is not in Our SPD table')
                        not_price_cat_in_spd['Reason'] = 'This Catalog is not in Our SPD table'
                        insert_data_into_mongodb(self.dbname, not_price_cat_in_spd,  f'{self.Req_id}_PriceProductDoesNotExists')

                # If one DataFrame is empty, use the one that is not empty
                elif not exists_spd_df.empty:
                    not_price_cat_in_spd = self.price_df[~self.price_df['SupplierCatalogId'].isin(exists_spd_df['SupplierCatalogId'])]
                    not_price_cat_in_spd = not_price_cat_in_spd[not_price_cat_in_spd['Price'].notna() | (not_price_cat_in_spd['Price'] != '')]
                    if not not_price_cat_in_spd.empty:
                        logger.info('This Catalog is not in Our SPD table')
                        not_price_cat_in_spd['Reason'] = 'This Catalog is not in Our SPD table'
                        insert_data_into_mongodb(self.dbname, not_price_cat_in_spd, f'{self.Req_id}_PriceProductDoesNotExists')

                elif not new_cat_in_spd.empty:
                    not_price_cat_in_spd = self.price_df[~self.price_df['SupplierCatalogId'].isin(new_cat_in_spd['SupplierCatalogId'])]
                    not_price_cat_in_spd = not_price_cat_in_spd[not_price_cat_in_spd['Price'].notna() | (not_price_cat_in_spd['Price'] != '')]
                    if not not_price_cat_in_spd.empty:
                        logger.info('This Catalog is not in Our SPD table')
                        not_price_cat_in_spd['Reason'] = 'This Catalog is not in Our SPD table'
                        insert_data_into_mongodb(self.dbname, not_price_cat_in_spd, f'{self.Req_id}_PriceProductDoesNotExists')

        '''Read Data From SupplierProductPricesInsertTemp or SupplierProductPricesUpdateTemp or PriceProductDoesNotExists'''
        update_product_df = read_data_from_mongo(self.dbname, f'{self.Req_id}_SupplierProductPricesUpdateTemp')
        new_product_df = read_data_from_mongo(self.dbname, f'{self.Req_id}_SupplierProductPricesInsertTemp')
        error_product_count = read_data_from_mongo(self.dbname, f'{self.Req_id}_PriceProductDoesNotExists')
        error_in_price = read_data_from_mongo(self.dbname, f'{self.Req_id}_ErrorPriceExcel')

        '''Concat the product does not exists and error price dataframe in one df'''
        error_price = pd.concat([error_in_price, error_product_count])

        if not update_product_df.empty:
            update_product_df.to_csv(f'{sys.argv[3]}/{self.Req_id}_Update_Data_Count.csv', index=False)
        if not new_product_df.empty:
            new_product_df.to_csv(f'{sys.argv[3]}/{self.Req_id}_New_Data_Count.csv', index=False)
        if not error_price.empty:
            error_price.to_csv(f'{sys.argv[3]}/{self.Req_id}_Error_Data_Count.csv', index=False)


if __name__ == "__main__":
    config = ConfigManager()
    data = {'Server': config.get_value("CREDENTIAL", "SERVER"),
            'Database': config.get_value("CREDENTIAL", "DATABASE"),
            'Username': config.get_value("CREDENTIAL", "USERNAME"),
            'Password': config.get_value("CREDENTIAL", "PASSWORD")}

    print(data)
    data_processor = DataProcessor(server=data['Server'], database=data['Database'], username=data['Username'],
                                   password=data['Password'])
    data_processor.process()
