
import logging
import os
from datetime import datetime


class LoggerHandler:
    def __init__(self, request_id="default"):
        self.request_logger = logging.getLogger('my_logger')
        self.request_logger.setLevel(logging.INFO)

        # Remove previous handlers to avoid duplicate logs
        if self.request_logger.hasHandlers():
            self.request_logger.handlers.clear()

        # Create a StreamHandler to output log messages to stdout
        stream_handler = logging.StreamHandler()
        stream_handler.setLevel(logging.INFO)

        this_folder = os.path.dirname(os.path.abspath(__file__))
        scripts_folder = os.path.dirname(this_folder)
        sub_scripts_folder = os.path.dirname(scripts_folder)

        # Generate a dynamic log file name
        log_folder = os.path.join(sub_scripts_folder, 'Log/RequestLog')
        os.makedirs(log_folder, exist_ok=True)  # Ensure the folder exists

        log_file = os.path.join(log_folder, f'{request_id}_{datetime.today().strftime("%d-%m-%Y")}.txt')

        # Create a FileHandler to store log messages in a file
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(logging.INFO)

        # Create a formatter and add it to the handlers
        formatter = logging.Formatter(
            "[%(asctime)s]:[PID %(process)d]:[%(levelname)s]:%(name)s::%(filename)s|%(lineno)d|%(message)s",
            datefmt='%d-%m-%Y %H:%M:%S')
        stream_handler.setFormatter(formatter)
        file_handler.setFormatter(formatter)

        # Add the handlers to the logger
        self.request_logger.addHandler(stream_handler)
        self.request_logger.addHandler(file_handler)


def get_logger(request_id):
    return LoggerHandler(request_id).request_logger
