from client import config, ISPRODUCTION, warnings, requests, date, wget
import time
from client.com.handler.LoggerHandler import <PERSON><PERSON><PERSON>andler

log = LoggerHandler()
warnings.filterwarnings('ignore')


class FileManagerApi:
    def __init__(self):
        self.__filemanager_base_url = config.get("ProductionApi", "FileManagerBaseUrl") if ISPRODUCTION else config.get(
            "TestingAPI", "FileManagerBaseUrl")
        self.__existfile_endpoint = self.__filemanager_base_url + 'File/FileExists'
        self.__getfile_endpoint = self.__filemanager_base_url + 'File/GetFile'
        self.__uploadfile_endpoint = self.__filemanager_base_url + 'api/external/file/uploadFile/'

    def post_file(self, endpoint: str, payload: dict, files: dict):
        try:
            res = requests.post(endpoint, params=payload, files=files, verify=False)
            return res
        except Exception as e:
            log.request_logger.error(e, exc_info=True)
            return

    def upload_file(self, filename: str, file: object, file_path: str):
        bucket_uuid = config.get("Data", "BUCKET_UUID")
        attempt = 0
        max_attempts = 3
        while attempt < max_attempts:
            try:
                files = {"file": (filename, file, "text/csv")}
                payload = {"bucket_uuid": bucket_uuid, "file_path": file_path}
                res = self.post_file(self.__uploadfile_endpoint, payload=payload, files=files)

                if res and res.status_code == 200:
                    return res
            except Exception as e:
                log.request_logger.error(e, exc_info=True)

            attempt += 1
            time.sleep(5)  # Wait for 5 seconds before retrying

        log.request_logger.error(f"Failed to upload file after {max_attempts} attempts.")
        return None


class ApiHandler(FileManagerApi):
    def __init__(self):
        super().__init__()
        self.__middleware_base_url = config.get("ProductionApi", "MiddlewareBaseUrl") if ISPRODUCTION else config.get(
            "TestingAPI", "MiddlewareBaseUrl")
        self.__update_request_endpoint = self.__middleware_base_url + "api/updateRequestStatus"
        self.__addmiddleware_log_endpoint = self.__middleware_base_url + "api/addLogs"
        self.__add_request_report_endpoint = self.__middleware_base_url + 'api/addRequestReport'
        self.__add_output_file_endpoint = self.__middleware_base_url + 'api/addOutputFile'

    def post(self, endpoint: str, payload: dict):

        try:
            res = requests.put(endpoint, json=payload, verify=False)
            return res
        except Exception as e:
            log.request_logger.error(e, exc_info=True)
            return

    def log_post(self, endpoint: str, payload: dict):

        try:
            res = requests.post(endpoint, json=payload, verify=False)
            return res
        except Exception as e:
            log.request_logger.error(e, exc_info=True)
            return

    def get(self, endpoint: str):
        try:
            res = requests.get(endpoint, verify=False)
            return res
        except Exception as e:
            log.request_logger.error(e, exc_info=True)
            return

    def update_request(self, status_id: int, stage_id: int, import_id: int, sub_stage_id: int):
        try:
            payload = {"StatusId": status_id, "StageId": stage_id, "ImportRequestID": import_id}
            if sub_stage_id is not None:
                payload["SubStageId"] = sub_stage_id
            resp = self.post(self.__update_request_endpoint, payload=payload)
            print(resp.json())
            return resp
        except Exception as e:
            log.request_logger.error(e, exc_info=True)
            return

    def add_output_file(self, endpoint: str, import_id: int, output_file: str, file_uuid_name: str, import_type_id: int, output_file_type_id: int) -> None:
        try:
            data = {"ImportRequestID": import_id, "FileName": output_file, "OutputFileTypeId": output_file_type_id, "ImportTypeId": import_type_id,
                    "FileUUID": file_uuid_name}
            print("OutputProductImport", data)
            res = self.log_post(endpoint, payload=data)
            return res
        except Exception as e:
            log.request_logger.error(e, exc_info=True)
            return

    def add_import_filename(self, output_file: str, file_uuid: str, import_id: int, import_type_id: int, output_file_type_id: int):
        try:
            return self.add_output_file(
                endpoint=self.__add_output_file_endpoint, output_file=output_file, file_uuid_name=file_uuid,
                import_id=import_id, import_type_id=import_type_id,
                output_file_type_id=output_file_type_id)
        except Exception as e:
            log.request_logger.error(e, exc_info=True)
            return

    def add_log(self, import_id: int, com_name: str, logs: str, log_type: str):
        try:
            data = {"ImportRequestID": import_id, "ComponentName": com_name, "LogType": log_type, "Logs": logs}
            res = self.log_post(endpoint=self.__addmiddleware_log_endpoint, payload=data)
            return
        except Exception as e:
            log.request_logger.error(e, exc_info=True)
            return

    def add_report(self, import_id: int, import_type_id: int, input_count: int, new_count: int, exists_count: int, error_count: int, update_count: int,
                   duplicate_count: int):
        try:
            data = {"ImportRequestID": import_id, "ImportTypeId": import_type_id, "InputDataCount": input_count, "NewDataCount": new_count,
                    "ExistsDataCount": exists_count, "ErrorDataCount": error_count, "UpdateDataCount": update_count, "DuplicateDataCount": duplicate_count}
            res = self.log_post(endpoint=self.__add_request_report_endpoint, payload=data)
            return res
        except Exception as e:
            log.request_logger.error(e, exc_info=True)
            return
