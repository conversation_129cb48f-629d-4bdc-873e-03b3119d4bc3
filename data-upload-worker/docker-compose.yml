services:

  # Price Process Queue
  price-queue-prod:
    image: price-queue-prod:latest
    build: 
      context: ./PriceProcessQueue
      dockerfile: Dockerfile
    ports:
      - "1011:1011"
    restart: unless-stopped
    environment:
      - PORT=1011
      - PRODUCTION=True
    hostname: price-queue-prod
    container_name: price-queue-prod
    volumes:
      - ./config.cfg:/app/config.cfg  # Mount the config file inside the container

  # Product Process Queue
  product-process-queue:
    image: product-process-queue:latest
    build: 
      context: ./ProductProcessQueue
      dockerfile: Dockerfile
    ports:
      - "1012:1012"
    restart: unless-stopped
    environment:
      - PORT=1012
      - PRODUCTION=True
    hostname: product-queue-prod
    container_name: product-queue-prod
    volumes:
      - ./config.cfg:/app/config.cfg  # Mount the config file inside the container

  # Stock Process Queue
  stock-queue-prod:
    image: stock-queue-prod:latest
    build: 
      context: ./StockProcessQueue
      dockerfile: Dockerfile
    ports:
      - "1013:1013"
    restart: unless-stopped
    environment:
      - PORT=1013
      - PRODUCTION=True
    hostname: stock-queue-prod
    container_name: stock-queue-prod
    volumes:
      - ./config.cfg:/app/config.cfg  # Mount the config file inside the container

  # Upload Process Queue
  upload-queue-prod:
    image: upload-queue-prod:latest
    build: 
      context: ./UploadProcessQueue
      dockerfile: Dockerfile
    ports:
      - "1014:1014"
    restart: unless-stopped
    environment:
      - PORT=1014
      - PRODUCTION=True
    hostname: upload-queue-prod
    container_name: upload-queue-prod
    volumes:
      - ./config.cfg:/app/config.cfg  # Mount the config file inside the container
    