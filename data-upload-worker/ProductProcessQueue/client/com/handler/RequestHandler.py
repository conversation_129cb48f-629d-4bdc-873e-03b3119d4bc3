from client import logger, thisfolder, os, config, pd
from client.com.handler.ApiHandler import ApiHandler


class RequestHandler(ApiHandler):

    def __init__(self):
        super().__init__()
        self.initialize_request()  # Initialize variables at the start
        self.request_logger = None
        self.ImportRequestId = None
        self.SupplierID = None
        self.SupplierName = None
        self.CollectionName = ""
        self.Dbname = ""
        self.ImportTypeId = None
        self.RequestTypeId = None
        self.Error = ""
        self.InputFileName = ""
        self.Status = "Initiate"
        self.SendModuleType = "ProductProcess"
        self.TimeTaken = ""
        self.CreatedDate = ""
        self.RequestOutputFiles = []
        self.ScriptPath = os.path.join(thisfolder, config.get("PATH", "ScriptsFolder"))
        self.Script = None
        self.InputDataCount = 0
        self.NewDataCount = 0
        self.ExistsDataCount = 0
        self.ErrorDataCount = 0
        self.UpdateDataCount = 0
        self.ChemInputDataCount = 0
        self.ChemNewDataCount = 0
        self.ChemErrorDataCount = 0
        self.ChemExistsDataCount = 0
        self.ChemAssociateDataCount = 0
        self.ChemIndexObj = {}

    def initialize_request(self):
        """Reset all attributes to their default values before a new request."""
        self.ImportRequestId = None
        self.SupplierID = None
        self.SupplierName = None
        self.CollectionName = ""
        self.Dbname = ""
        self.ImportTypeId = None
        self.RequestTypeId = None
        self.Error = ""  # Reset error
        self.InputFileName = ""
        self.Status = "Initiate"
        self.TimeTaken = ""
        self.CreatedDate = ""
        self.RequestOutputFiles = []
        self.InputDataCount = 0
        self.NewDataCount = 0
        self.ExistsDataCount = 0
        self.ErrorDataCount = 0
        self.UpdateDataCount = 0
        self.ChemInputDataCount = 0
        self.ChemNewDataCount = 0
        self.ChemErrorDataCount = 0
        self.ChemExistsDataCount = 0
        self.ChemAssociateDataCount = 0
        self.ChemIndexObj = {}

    def middleware_upload_file(self, file_name, file_type_id, act_file_name):
        try:
            filename = os.path.basename(file_name)
            file_path = config.get("Data", "UPLOAD_FILE_PATH") + str(self.ImportRequestId) + "/" + "Product" + "/"
            file_stream = open(file_name, 'rb')
            self.request_logger.warning(
                f"[[ImportRequestId:{self.ImportRequestId}]] => The File: {file_name} and FileType: {file_type_id} is being uploaded on Cloud Server")
            self.info(
                logs=f"The File {filename} and FileType: {file_type_id} is being uploaded on Cloud Server")

            file_upload_res = self.upload_file(filename=act_file_name, file=file_stream, file_path=file_path)
            respone_file_data = {}
            if self.validate_response(file_upload_res):
                file_upload_res_data = file_upload_res.json()
                output_file_name = file_upload_res_data["data"].get("filename", "")
                file_name_uuid = file_upload_res_data["data"].get("uuid", "")

                print("output_file_name", output_file_name)
                self.result(
                    logs=f"The File {filename} and FileType:{file_type_id} has been uploaded on CloudServer in module")
                self.info(logs="Adding the OutPutFileName")
                add_filename_resp = self.add_import_filename(
                    import_id=self.ImportRequestId,
                    output_file=output_file_name,
                    file_uuid=file_name_uuid,
                    import_type_id=self.ImportTypeId,
                    output_file_type_id=file_type_id)
                if self.validate_response(add_filename_resp):
                    self.result(logs="Output File Successfully Added")
                    respone_file_data['FileName'] = str(output_file_name)
                    respone_file_data['FileType'] = str(file_type_id)
                    respone_file_data['FileUUID'] = str(file_name_uuid)
                else:
                    self.error_log(logs="There is error in Adding FileName")
                    self.request_logger.error(f"There is error in Adding File {file_name}")
                    self.Status = "Canceled"
                    self.Error = f"There is error in Adding FileName {filename}"
            else:
                self.error_log(logs=f"There is error in uploading middleware file {filename} ::{self.SendModuleType} :: {file_type_id}")
                self.request_logger.error(f"There is error in uploading middleware file {file_name}")
                self.Status = "Canceled"
                self.Error = f"There is error in uploading Product file {filename}"
            return respone_file_data
        except Exception as e:
            self.Error = str(e)
            self.error_log(logs=str(e))
            self.Status = "Canceled"
            self.request_logger.error(e, exc_info=True)
            return

    def middleware_is_file_empty(self, middleware_file_path: str):
        try:
            if os.path.exists(middleware_file_path):
                if middleware_file_path.endswith(".csv"):
                    middleware_file_df = pd.read_csv(middleware_file_path, dtype=str, encoding='Latin-1')
                    return middleware_file_df.empty
                elif middleware_file_path.endswith(".xlsx"):
                    middleware_file_df = pd.read_excel(middleware_file_path, dtype=str)
                    return middleware_file_df.empty
                else:
                    middleware_file_size = os.stat(middleware_file_path).st_size / 1000
                    if middleware_file_size > 1:
                        return False
                    else:
                        return True
            else:
                return True
        except Exception as e:
            self.error_log(logs=str(e))
            self.request_logger.error(e, exc_info=True)
            return True

    def get_count_of_lines_for_txt(self, filename):
        with open(filename, 'r') as f:
            for line_number, line in enumerate(f, start=1):
                if line.strip():
                    yield line_number

    def get_count_of_data(self, file_name: str):
        try:
            if file_name.endswith(".csv"):
                df = pd.read_csv(file_name, dtype=str)
                return len(df)
            elif file_name.endswith(".xlsx"):
                df = pd.read_excel(file_name, dtype=str)
                return len(df)
            elif file_name.endswith(".txt"):
                return sum(1 for _ in self.get_count_of_lines_for_txt(file_name))
        except Exception as e:
            self.request_logger.error(f"[[ImportRequestId:{self.ImportRequestId}]] => {e}",
                         exc_info=True)
            self.error_log(logs=str(e))

    def add_request_report(self, import_type_id: int):
        try:
            add_res = self.add_report(import_id=self.ImportRequestId, import_type_id=import_type_id,
                                      input_count=self.InputDataCount, new_count=self.NewDataCount,
                                      exists_count=self.ExistsDataCount,
                                      error_count=self.ErrorDataCount, update_count=self.UpdateDataCount, duplicate_count=0, chem_index_obj=self.ChemIndexObj)

            if not self.validate_response(add_res):
                self.request_logger.error(f"There is error during AddReportRequest_API {add_res.__dict__}")
                self.Status = "Canceled"
                self.Error = f"There is error during AddReportRequest_API {add_res.__dict__}"
        except Exception as e:
            self.request_logger.error(e, exc_info=True)

    def update_request_status(self, stage_id: int, sub_stage: int = None):
        try:
            if self.Status == 'Inprogress':
                status_id = 3
            elif self.Status == 'Finished':
                status_id = 4
            else:
                status_id = 5
            update_res = self.update_request(import_id=self.ImportRequestId, status_id=status_id, stage_id=stage_id,
                                             sub_stage_id=sub_stage)

            if not self.validate_response(update_res):
                self.request_logger.error(f"There is error during UpdateRequest_API {update_res.__dict__}")
                self.Status = "Canceled"
                self.Error = f"There is error during UpdateRequest_API {update_res.__dict__}"
        except Exception as e:
            self.request_logger.error(e, exc_info=True)

    def validate_response(self, res):
        if res is not None:
            if res.status_code == 200:
                return True
            else:
                self.request_logger.error(res.__dict__)
                return False
        else:
            return False

    def error_log(self, logs: str):
        try:
            self.add_log(import_id=self.ImportRequestId, com_name="ProductImportingProcess",
                         log_type="ERROR", logs=logs)
        except Exception as e:
            self.request_logger.error(e, exc_info=True)

    def info(self, logs: str):
        try:
            self.add_log(import_id=self.ImportRequestId, com_name="ProductImportingProcess",
                         log_type="INFO", logs=logs)
        except Exception as e:
            self.request_logger.error(e, exc_info=True)

    def result(self, logs: str):
        try:
            self.add_log(import_id=self.ImportRequestId, com_name="ProductImportingProcess",
                         log_type="RESULT", logs=logs)
        except Exception as e:
            self.request_logger.error(e, exc_info=True)
