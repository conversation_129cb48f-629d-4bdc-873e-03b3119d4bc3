from client import thisfolder, os, subprocess, platform, config, time, sys, re
from client.com.handler.RequestHandler import RequestHandler
import json


class ProductImportHandler(RequestHandler):

    def response(self):
        try:
            return {
                "ImportRequestId": self.ImportRequestId,
                "RequestTypeId": self.RequestTypeId,
                "Status": self.Status,
                "Error": self.Error,
                "ProductTimeTaken": self.TimeTaken,
                "QueueName": "product_queue",
                "OutputFiles": self.RequestOutputFiles,
            }
        except Exception as e:
            self.request_logger.error(e, exc_info=True)

    def start_importing_process(self):
        try:

            temp_output_folder_name = os.path.join(thisfolder, 'Temp/ProductImport' + "/" + str(self.ImportRequestId))
            try:
                os.mkdir(temp_output_folder_name)
            except FileExistsError:
                pass

            self.Status = "Inprogress"
            self.update_request_status(stage_id=3, sub_stage=16)

            if not self.Error:

                self.info(logs=f"{self.Script} Successfully started")
                self.request_logger.info(
                    f"[[ImportRequestID:{self.ImportRequestId}]] => {self.Script} Successfully started")

                start_time = time.time()

                python_executable = sys.executable
                venv_path = os.path.dirname(os.path.dirname(python_executable))
                python_path = os.path.join(venv_path, 'Scripts' if platform.system() == 'Windows' else 'bin', 'python')
                print('Python', python_path)
                proc = subprocess.run([python_path, os.path.join(self.ScriptPath, self.Script, 'main.py'), self.Dbname,
                                       self.CollectionName, temp_output_folder_name, str(self.ImportRequestId), str(self.CreatedDate)],
                                      capture_output=True, text=True)

                return_code = proc.returncode
                self.request_logger.info(proc.stderr)
                self.request_logger.info(
                    f"[[ImportRequestId:{self.ImportRequestId}]] => {self.Script} RETURN CODE:{return_code}")
                end_time = time.time()
                elapsed_time = end_time - start_time
                hours = int(elapsed_time // 3600)
                elapsed_time %= 3600
                minutes = int(elapsed_time // 60)
                seconds = elapsed_time % 60
                self.request_logger.info(
                    f"The ProductImport Process has Time taken: {hours} hours, {minutes} minutes, {seconds:.2f} seconds")
                self.TimeTaken = f"{hours} hours, {minutes} minutes, {seconds:.2f} seconds"

                if return_code == 0:
                    self.RequestOutputFiles.clear()
                    for attr in ["InputDataCount", "NewDataCount", "ExistsDataCount", "ErrorDataCount",
                                 "UpdateDataCount"]:
                        setattr(self, attr, 0)
                    self.Status = "Finished"
                    self.request_logger.warning(
                        f"[[ImportRequestId:{self.ImportRequestId}]] => The {self.Script} Process has been Finished Now Files will be uploading")

                    self.update_request_status(stage_id=3, sub_stage=17)
                    product_output_file_list = os.listdir(temp_output_folder_name)
                    print(f"{product_output_file_list = }")
                    product_allowed_filetype = json.loads(config.get("ProductData", "ProductAllowFileType"))
                    if product_output_file_list:
                        for product_file_name in product_output_file_list:
                            isvalid_file_type = False  # define variable for Validate filetype for listing bug for unallocated
                            for file_type, file_type_data in product_allowed_filetype.items():
                                pattern = rf"\b{self.ImportRequestId}_{file_type}\b"
                                if re.search(pattern, product_file_name, re.IGNORECASE):
                                    print(f"Matched file_type: {file_type} -> {product_file_name}")
                                    print(f"Assigned fileType ID: {file_type_data['fileType']}")
                                    isvalid_file_type = True  # change variable to true
                                    if not self.middleware_is_file_empty(temp_output_folder_name + "/" + product_file_name):
                                        request_file_resp = self.middleware_upload_file(
                                            file_name=temp_output_folder_name + "/" + product_file_name,
                                            file_type_id=product_allowed_filetype[file_type]["fileType"],
                                            act_file_name=product_file_name)
                                        self.RequestOutputFiles.append(request_file_resp)

                                        exec(
                                            f"self.{product_allowed_filetype[file_type]['varName']} ="
                                            f" {self.get_count_of_data(file_name=temp_output_folder_name + '/' + product_file_name)}")

                                    else:
                                        self.request_logger.info(
                                            f"[[ImportRequestID:{self.ImportRequestId}]] => The ProductImport has not Created File or Empty file for Type [[{file_type}]]")
                                        self.info(logs=f"The ProductImport has not Created File or Empty file for Type {file_type}")
                                    break
                                else:
                                    continue

                            if not isvalid_file_type:
                                self.info(logs=f"The ProductImport has created invalid filetype which is not included in allowed filetype {product_file_name}")
                                self.request_logger.error(
                                    f"[[ImportRequestID:{self.ImportRequestId}]] => The ProductImport has created invalid filetype which is not included in allowed filetype [[{product_file_name}]]")
                        self.update_request_status(stage_id=3)
                    else:
                        self.request_logger.error(self.Error)
                        self.Error = "The ProductImport has not Created any Output Files"
                else:
                    self.Status = "Canceled"
                    self.Error = proc.stderr
                    self.request_logger.error(self.Error)
                    self.update_request_status(stage_id=3)
            else:
                self.Status = "Canceled"
            self.ChemIndexObj["ChemIndexInputDataCount"] = self.ChemInputDataCount
            self.ChemIndexObj["ChemIndexNewDataCount"] = self.ChemNewDataCount
            self.ChemIndexObj["ChemIndexErrorDataCount"] = self.ChemErrorDataCount
            self.ChemIndexObj["ChemIndexAssociationDataCount"] = self.ChemAssociateDataCount
            self.ChemIndexObj["ChemIndexExistDataCount"] = self.ChemExistsDataCount
            self.Status = "Canceled" if self.Error else "Finished"
            response_data = self.response()
            print(f"{response_data = }")
            if self.Error:
                self.request_logger.error(
                    f"[[ImportRequestId:{self.ImportRequestId}]] => {self.Error}")
            self.request_logger.warning(
                f"[[ImportRequestId:{self.ImportRequestId}]] => The {self.Script} Whole Process has been finished")
            self.update_request_status(stage_id=3)
            self.add_request_report(import_type_id=self.ImportTypeId)
            return response_data
        except Exception as e:
            self.request_logger.error(f"[[ImportRequestId:{self.ImportRequestId}]] => {e}", exc_info=True)
            self.Status = "Canceled"
            self.Error = f"There is an error occured during process {str(e)}"
            self.update_request_status(stage_id=3)
            response_data = self.response()
            print(f"{response_data = }")
            return response_data
