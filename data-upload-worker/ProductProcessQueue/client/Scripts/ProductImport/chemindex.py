import platform
import sys
from datetime import datetime, timezone
import pandas as pd
from indigo import Indigo
import numpy as np
import pyodbc
import warnings
from rdkit.Chem import inchi
from rdkit import Chem, RDLogger
from sqlalchemy import create_engine
from config_manager import ConfigManager


RDLogger.DisableLog('rdApp.*')
config = ConfigManager()
server = config.get_value('ChemIndex_CREDENTIAL', 'SERVER')
database = config.get_value('ChemIndex_CREDENTIAL', 'DATABASE')
username = config.get_value('ChemIndex_CREDENTIAL', 'USERNAME')
password = config.get_value('ChemIndex_CREDENTIAL', 'PASSWORD')


class ProductImporting:
    warnings.filterwarnings("ignore")

    def __init__(self):
        warnings.filterwarnings("ignore")
        self.indigo_instance = Indigo()
        self.conn = None
        self.chem_product_df = pd.DataFrame()
        self.df2 = pd.DataFrame()
        self.merged_df = pd.DataFrame()
        self.excel_df = pd.DataFrame()
        self.exists_df = pd.DataFrame()
        self.ass_chem_df = pd.DataFrame()
        self.new_associated = pd.DataFrame()
        self.error_chem_products = pd.DataFrame()
        self.error_chem_product = pd.DataFrame()
        self.smile_check = pd.DataFrame()
        self.associate_chem_product_df = pd.DataFrame()

    def connect_to_database(self):
        try:
            conn_string = (f"Driver={{ODBC Driver 17 for SQL Server}};Server={server};"
                           f"Database={database};"
                           f"uid={username};"
                           f"pwd={password};")
            self.conn = pyodbc.connect(conn_string)
        except (Exception,):
            conn_string = (
                f"DRIVER={{ODBC Driver 18 for SQL Server}};"
                f"SERVER={server};"
                f"DATABASE={database};"
                f"UID={username};"
                f"PWD={password};"
                "Encrypt=yes;TrustServerCertificate=yes;"
            )
            self.conn = pyodbc.connect(conn_string)
        print("Connected to the database.")
        return

    def get_engine(self):
        system = platform.system()
        if system == 'Linux':
            conn_string = (
                f"DRIVER={{ODBC Driver 18 for SQL Server}};"
                f"SERVER={server};"
                f"DATABASE={database};"
                f"UID={username};"
                f"PWD={password};"
                "Encrypt=yes;TrustServerCertificate=yes;"
            )
            engine = create_engine(f"mssql+pyodbc:///?odbc_connect={conn_string}")
        elif system == 'Windows':
            conn_string = f"Driver={{ODBC Driver 17 for SQL Server}};Server={server};Database={database};uid={username};pwd={password};"
            engine = create_engine(f"mssql+pyodbc:///?odbc_connect={conn_string}")
        return engine

    def read_data_from_chem_product_database(self):
        self.connect_to_database()
        query = """select ChemProductID as id, MolFile as smile, Can_Smile as can_smiles, InChiKey from [dbo].[ChemProducts]"""
        self.chem_product_df = pd.read_sql(query, self.conn)
        self.conn.close()
        print("Read data from the ChemProduct database.")
        return

    def read_data_from_error_chem_product_database(self):
        self.connect_to_database()
        query = """select MolFile as smile, ErrorID from ErrorChemProducts"""
        self.error_chem_product = pd.read_sql(query, self.conn)
        print("Read data from the ErrorChemProduct database.")
        self.conn.close()
        return

    def read_data_from_associate_chem_product_database(self):
        self.connect_to_database()
        query = """select ChemProductId as id, MolFile as smile, CanSmile as can_smiles from AssociatedChemProduct"""
        self.associate_chem_product_df = pd.read_sql(query, self.conn)
        print("Read data from the AssociateChemProduct database.")
        self.conn.close()
        return

    def generate_canonical_smiles(self, row):
        try:
            mol = self.indigo_instance.loadMolecule(row['smile'])
            mol.aromatize()
            can_smiles = mol.canonicalSmiles()
            if can_smiles == '':
                return None
            return can_smiles
        except (Exception,):
            return None

    def generate_inchi(self, row):
        try:
            mol = Chem.MolFromSmiles(row['smile'])
            if mol is not None:
                inchi_str = inchi.MolToInchi(mol)
                return inchi_str
            else:
                return None
        except (Exception,):
            return None

    def generate_inchi_key(self, row):
        try:
            if row['Inchi'] is not None:
                inchi_key = inchi.InchiToInchiKey(row['Inchi'])
                return inchi_key
            else:
                return None
        except (Exception,):
            return None

    # def generate_inchi_key(self, row):
    #     try:
    #         smiles = row['smile']
    #         if smiles:
    #             molecule = Chem.MolFromSmiles(smiles)
    #             if molecule:
    #                 inchi_key = Chem.inchi.MolToInchiKey(molecule)
    #                 return inchi_key
    #             else:
    #                 return np.nan
    #         else:
    #             return np.nan
    #     except (Exception,):
    #         return np.nan

    def insert_error_data(self, df):
        self.connect_to_database()
        query = """select MolFile from [dbo].[ErrorChemProducts]"""
        error_chem_df = pd.read_sql(query, self.conn)
        new_error_chem = df[~df['smile'].isin(error_chem_df['MolFile'])]
        error_df = new_error_chem[['smile', 'Reason']]
        column_name_mapping = {
            'smile': 'MolFile',
            'Reason': 'Reason'
        }
        error_df = error_df.rename(columns=column_name_mapping)
        if not error_df.empty:
            engine = self.get_engine()
            error_df.to_sql('ErrorChemProducts', con=engine, index=False, if_exists='append', schema='dbo')
            print("Data Inserted Successfully in Error Table")
        else:
            print('ErrorDF is Empty')
        self.conn.close()
        return

    def insert_chem_product_data(self, df):
        self.connect_to_database()
        new_insert_df = df[
            ['smile', 'canonical_smiles', 'InchiKey', 'Created_Date', 'CratedBy']]
        column_name_mapping = {
            'smile': 'MolFile',
            'canonical_smiles': 'Can_Smile',
            'InchiKey': 'InChiKey',
            'Created_Date': 'CreatedDate',
            'CratedBy': 'CreatedBy'
        }
        new_insert_df = new_insert_df.rename(columns=column_name_mapping)
        if not new_insert_df.empty:
            engine = self.get_engine()
            new_insert_df.to_sql('ChemProducts', con=engine, index=False, if_exists='append', schema='dbo')
            print("Data Inserted Successfully in ChemProduct Table")
        else:
            print("ChemProductDF is Empty")
        self.conn.close()
        return

    def insert_associate_data(self, df):
        self.connect_to_database()
        query = """select ChemProductID as DB_ID from [dbo].[AssociatedChemProduct]"""
        self.ass_chem_df = pd.read_sql(query, self.conn)
        self.new_associated = df[~df['DB_ID'].isin(self.ass_chem_df['DB_ID'])]
        columns_to_map = ['smile', 'DB_ID', 'Association', 'canonical_smiles', 'ProductName', 'CASNumber', 'MDLNumber']
        for col in columns_to_map:
            if col not in self.new_associated.columns:
                self.new_associated[col] = np.nan

        associates_df = self.new_associated[
            ['smile', 'DB_ID', 'Association', 'canonical_smiles']]
        column_name_mapping = {
            'smile': 'MolFile',
            'DB_ID': 'ChemProductId',
            'Association': 'AssociationTypeId',
            'canonical_smiles': 'CanSmile',
        }
        associates_df = associates_df.rename(columns=column_name_mapping)
        if not associates_df.empty:
            engine = self.get_engine()
            associates_df.to_sql('AssociatedChemProduct', con=engine, index=False, if_exists='append', schema='dbo')
            print("Data Inserted Successfully in Association Table")
        else:
            print('AssociationDF is Empty')
        self.conn.close()
        return

    def insert_associate_detail_data(self, df):
        self.connect_to_database()
        query = """select ChemProductID as DB_ID from [dbo].[AssociatedChemProductDetail]"""
        self.ass_chem_df = pd.read_sql(query, self.conn)
        self.new_associated = df[~df['DB_ID'].isin(self.ass_chem_df['DB_ID'])]
        columns_to_map = ['DB_ID', 'ProductName', 'CASNumber', 'MDLNumber']
        for col in columns_to_map:
            if col not in self.new_associated.columns:
                self.new_associated[col] = np.nan

        associates_df = self.new_associated[
            ['DB_ID', 'ProductName', 'CASNumber', 'MDLNumber']]
        column_name_mapping = {
            'DB_ID': 'ChemProductId',
            'ProductName': 'ProductName',
            'CASNumber': 'CASNumber',
            'MDLNumber': 'MDLNumber'
        }
        associates_df = associates_df.rename(columns=column_name_mapping)
        if not associates_df.empty:
            engine = self.get_engine()
            associates_df.to_sql('AssociatedChemProductDetail', con=engine, index=False, if_exists='append', schema='dbo')
            print("Data Inserted Successfully in Association Detail Table")
        else:
            print('AssociationDetailDF is Empty')
        self.conn.close()
        return

    def insert_chem_product_detail_data(self, df):
        self.connect_to_database()
        temp_df = df[['id', 'Inchi', 'ProductName', 'CASNumber', 'MDLNumber']]
        column_name_mapping = {
            'id': 'ChemProductID',
            'Inchi': 'inChi',
            'ProductName': 'ProductName',
            'CASNumber': 'CASNumber',
            'MDLNumber': 'MDLNumber',
        }
        new_insert_df = temp_df.rename(columns=column_name_mapping)
        if not new_insert_df.empty:
            engine = self.get_engine()
            new_insert_df.to_sql('ChemProductDetail', con=engine, index=False, if_exists='append', schema='dbo')
            print("Data Inserted Successfully in ChemProduct Detail Table")
        else:
            print("ChemDetailDF is Empty")
        self.conn.close()
        return

    def chemimdex_process(self, excel_df, req_id):
        try:
            warnings.filterwarnings("ignore")
            self.read_data_from_chem_product_database()
            self.read_data_from_error_chem_product_database()
            self.read_data_from_associate_chem_product_database()
            new_product_df = excel_df
            new_product_df.to_csv(f"{sys.argv[3]}/{req_id}_Chem_Input_Data_Count.csv", index=False)

            '''Process of mapping Smile with Database CanSmile'''
            temp_match = pd.DataFrame(new_product_df)
            temp_match['DB_ID'] = None
            temp_match['Association'] = None
            temp_match['Reason'] = None
            chem_product_dict = dict(zip(self.chem_product_df['can_smiles'], self.chem_product_df['id']))
            associate_smiles_dict = dict(
                zip(self.associate_chem_product_df['smile'], self.associate_chem_product_df['id']))
            associate_can_smiles_dict = dict(
                zip(self.associate_chem_product_df['can_smiles'], self.associate_chem_product_df['id']))
            for index, row in new_product_df.iterrows():
                smiles = row['smile']
                if smiles in chem_product_dict:
                    temp_match.at[index, 'DB_ID'] = chem_product_dict[smiles]
                elif smiles in associate_smiles_dict:
                    temp_match.at[index, 'DB_ID'] = associate_smiles_dict[smiles]
                    temp_match.at[index, 'Reason'] = 'Excel Smile Match With Associate ChemProduct table Smile'
                elif smiles in associate_can_smiles_dict:
                    temp_match.at[index, 'DB_ID'] = associate_can_smiles_dict[smiles]
                    temp_match.at[index, 'Reason'] = 'Excel Smile Match With Associate ChemProduct table CanSmile'
            associate_smiles_df = temp_match[temp_match['Reason'].notna()]
            temp_data_check = temp_match[
                temp_match['Association'].isnull() & ~temp_match['smile'].isin(associate_smiles_df['smile'])]
            temp_can_matched = temp_match[temp_match['Association'].notnull()]
            print(f"Smile_Match_CanSmile: {len(temp_can_matched)}")

            new_df = pd.DataFrame(temp_data_check)
            new_df['canonical_smiles'] = new_df.apply(self.generate_canonical_smiles, axis=1)
            none_canonical_smile_df = new_df[new_df['canonical_smiles'].isna()]
            none_canonical_smile_df['Reason'] = 'Canonical_Smiles is None'
            new_df = new_df[new_df['canonical_smiles'].notna()]

            '''Process of mapping CanSmile with Databse Molfile'''
            smile_match = pd.DataFrame(new_df)
            chem_product_dict = dict(zip(self.chem_product_df['smile'], self.chem_product_df['id']))
            for index, row in new_df.iterrows():
                can_smiles = row['canonical_smiles']
                if can_smiles in chem_product_dict:
                    smile_match.at[index, 'DB_ID'] = chem_product_dict[can_smiles]
            mol_smile_data_check = smile_match[smile_match['DB_ID'].isnull()]
            mol_smile_can_matched = smile_match[smile_match['DB_ID'].notnull()]
            print(f"CanSmile_Match_MolSmile: {len(mol_smile_can_matched)}")

            '''Process of mapping CanSmile with Databse CanSmile'''
            can_smile_match = pd.DataFrame(mol_smile_data_check)
            chem_product_can_smiles_dict = dict(zip(self.chem_product_df['can_smiles'], self.chem_product_df['id']))
            associate_smiles_dict = dict(
                zip(self.associate_chem_product_df['smile'], self.associate_chem_product_df['id']))
            associate_can_smiles_dict = dict(
                zip(self.associate_chem_product_df['can_smiles'], self.associate_chem_product_df['id']))
            for index, row in mol_smile_data_check.iterrows():
                can_smiles = row['canonical_smiles']
                if can_smiles in chem_product_can_smiles_dict:
                    can_smile_match.at[index, 'DB_ID'] = chem_product_can_smiles_dict[can_smiles]
                    can_smile_match.at[index, 'Association'] = 1
                elif can_smiles in associate_smiles_dict:
                    can_smile_match.at[index, 'DB_ID'] = associate_smiles_dict[can_smiles]
                    can_smile_match.at[
                        index, 'Reason'] = 'Excel CanSmile Match With Associate ChemProduct table Smile'
                elif can_smiles in associate_can_smiles_dict:
                    can_smile_match.at[index, 'DB_ID'] = associate_can_smiles_dict[can_smiles]
                    can_smile_match.at[
                        index, 'Reason'] = 'Excel CanSmile Match With Associate ChemProduct table CanSmile'
            associate_cansmiles_df = can_smile_match[can_smile_match['Reason'].notna()]
            can_smile_data_check = can_smile_match[
                can_smile_match['Association'].isnull() & ~can_smile_match['canonical_smiles'].isin(
                    associate_cansmiles_df['canonical_smiles'])]
            can_smile_can_matched = can_smile_match[can_smile_match['Association'].notnull()]
            print(f"CanSmile_Match_CanSmile: {len(can_smile_can_matched)}")

            exists_df = pd.concat([associate_smiles_df, associate_cansmiles_df, mol_smile_can_matched[['smile', 'canonical_smiles', 'Association', 'DB_ID']]], axis=0, ignore_index=True)
            exists_df['DB_ID'] = exists_df['DB_ID']
            print(f"Exists_Data_Count: {len(exists_df)}")
            exists_df.to_csv(f"{sys.argv[3]}/{req_id}_Chem_Exists_Data_Count.csv", index=False)

            new_product_df = pd.DataFrame(can_smile_data_check)
            new_product_df['Inchi'] = new_product_df.apply(self.generate_inchi, axis=1)
            new_product_df['InchiKey'] = new_product_df.apply(self.generate_inchi_key, axis=1)
            none_in_chi_key_df = new_product_df[new_product_df['InchiKey'].isna()]
            none_in_chi_key_df['Reason'] = 'InChiKey is None'
            new_product_df = new_product_df[new_product_df['InchiKey'].notna()]

            '''Process of mapping InchiKey with Databse InchiKey'''
            in_chi_match = pd.DataFrame(new_product_df)
            chem_product_inchi_dict = dict(zip(self.chem_product_df['InChiKey'], self.chem_product_df['id']))
            chem_product_smiles_dict = dict(zip(self.chem_product_df['can_smiles'], self.chem_product_df['id']))
            for index, row in new_product_df.iterrows():
                in_chi_key = row['InchiKey']
                canonical_smiles = row['canonical_smiles']
                if in_chi_key in chem_product_inchi_dict and canonical_smiles in chem_product_smiles_dict:
                    in_chi_match.at[index, 'Association'] = 3
                    in_chi_match.at[index, 'DB_ID'] = chem_product_inchi_dict[in_chi_key]
                elif in_chi_key in chem_product_inchi_dict:
                    in_chi_match.at[index, 'Association'] = 2
                    in_chi_match.at[index, 'DB_ID'] = chem_product_inchi_dict[in_chi_key]
                else:
                    in_chi_match.at[index, 'Association'] = None
                    in_chi_match.at[index, 'DB_ID'] = None
            final_data_check = in_chi_match[in_chi_match['DB_ID'].isnull()]
            final_df = in_chi_match[in_chi_match['DB_ID'].notnull()]
            print(f"InChi_Match_DBInchi: {len(final_df)}")

            self.error_chem_products = pd.concat([self.smile_check, none_canonical_smile_df, none_in_chi_key_df], axis=0, ignore_index=True)
            print(f"Error_Data_Count: {len(self.error_chem_products)}")
            self.error_chem_products.to_csv(f"{sys.argv[3]}/{req_id}_Chem_Error_Data_Count.csv", index=False)

            if not self.error_chem_products.empty:
                self.insert_error_data(self.error_chem_products)

            duplicates_mask = final_data_check.duplicated(subset=['canonical_smiles', 'InchiKey'])
            original_df = final_data_check[~duplicates_mask]
            duplicates_df = final_data_check[duplicates_mask]
            duplicates_df['Association'] = 3

            duplicates_mask1 = original_df.duplicated(subset=['canonical_smiles'])
            original_df1 = original_df[~duplicates_mask1]
            duplicates_df1 = original_df[duplicates_mask1]
            duplicates_df1['Association'] = 1

            duplicates_mask2 = original_df1.duplicated(subset=['InchiKey'])
            original_df2 = original_df1[~duplicates_mask2]
            duplicates_df2 = original_df1[duplicates_mask2]
            duplicates_df2['Association'] = 2

            duplicate = pd.concat([duplicates_df, duplicates_df1, duplicates_df2], axis=0, ignore_index=True)

            current_utc_time = datetime.now(timezone.utc)
            formatted_datetime = current_utc_time.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
            parsed_datetime = datetime.strptime(formatted_datetime, '%Y-%m-%d %H:%M:%S.%f')

            original_df2['Created_Date'] = parsed_datetime
            original_df2['CratedBy'] = 999
            print(f"New_Data_Count: {len(original_df2)}")
            original_df2.to_csv(f"{sys.argv[3]}/{req_id}_Chem_New_Data_Count.csv", index=False)

            columns_to_map = ['smile', 'canonical_smiles', 'Inchi', 'InchiKey', 'MDLNumber', 'CASNumber', 'ProductName', 'Created_Date', 'CratedBy']
            for col in columns_to_map:
                if col not in original_df2.columns:
                    original_df2[col] = np.nan

            if not original_df2.empty:
                self.insert_chem_product_data(original_df2)

            self.read_data_from_chem_product_database()
            print(f"InsertAfterCount: {len(self.chem_product_df)}")

            '''Inserting ChemProduct Details'''
            new_chem_detail_df = original_df2[['Inchi', 'MDLNumber', 'CASNumber', 'ProductName', 'InchiKey']]
            new_chem_detail_df['InChiKey'] = new_chem_detail_df['InchiKey'].fillna('')
            new_chem_detail = new_chem_detail_df.merge(self.chem_product_df[['id', 'InChiKey']], on='InChiKey', how='inner')
            if not new_chem_detail.empty:
                self.insert_chem_product_detail_data(new_chem_detail)

            association_df = pd.concat([duplicate, temp_can_matched, can_smile_can_matched, final_df], axis=0, ignore_index=True)

            new_ass = pd.DataFrame(association_df)
            new_ass['can_smiles'] = new_ass['canonical_smiles']
            new_ass['InChiKey'] = new_ass['InchiKey']
            new_ass['can_smiles'] = new_ass['can_smiles'].fillna('')
            new_ass['InChiKey'] = new_ass['InChiKey'].fillna('')
            output1 = new_ass.merge(self.chem_product_df[['id', 'can_smiles']], on='can_smiles', how='left', validate=None)
            output2 = output1.merge(self.chem_product_df[['id', 'InChiKey']], on='InChiKey', how='left', validate=None)
            output2['DB_ID'] = output2['id_x'].fillna(output2['id_y'])
            main_associate_df = pd.DataFrame(output2)
            main_associate_df.to_csv(f"{sys.argv[3]}/{req_id}_Chem_Associate_Data_Count.csv", index=False)
            print(f"Association_Data_Count: {len(main_associate_df)}")
            main_associate_df.drop_duplicates(subset='DB_ID', keep='first')
            final_associate_df = main_associate_df[main_associate_df['DB_ID'].notna()]
            print(f"{len(new_ass)} Total Record Found ")
            print(f"{len(new_ass) - len(final_associate_df)} duplicate Removed Successfully")
            print(f"FinalAssociateDf_Data_Count: {len(final_associate_df)}")

            if not final_associate_df.empty:
                self.insert_associate_data(final_associate_df)

            if not final_associate_df.empty:
                self.insert_associate_detail_data(final_associate_df)
            return True, "Successfully ChemIndex Process Done"
        except Exception as e:
            print(f"Error: {e}")
            return False, f"Error in ChemIndex Process : {e}"
