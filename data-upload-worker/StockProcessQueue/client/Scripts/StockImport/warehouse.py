import requests
import json
from base64 import b64encode, b64decode
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad
import logging
import time  # To add delay if needed
from config_manager import ConfigManager


config = ConfigManager()

# AES Key & IV (From API Config)
# AES_KEY = b64decode("oQZXp2YzVH/68TcVhrAVhiqCNzVJRdg4S9t8BFTPkas=")
AES_KEY = b64decode(config.get_value('OMS_AES', 'KEY'))
# AES_IV = b64decode("fKZm6rqve0ZwMdoFIl5Rhw==")
AES_IV = b64decode(config.get_value('OMS_AES', 'IV'))


# Logger setup
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)


class Encdcr:
    @classmethod
    def encryptData(cls, d1):
        """Encrypts the given data using AES CBC mode and base64 encodes the result."""
        try:
            cipher = AES.new(AES_KEY, AES.MODE_CBC, AES_IV)
            encrypted_data = cipher.encrypt(pad(str(d1).encode(), AES.block_size))
            return b64encode(encrypted_data).decode("utf-8")  # Return base64 encoded string
        except Exception as e:
            logger.error(f"Encryption Error: {e}", exc_info=True)
            return None

    @classmethod
    def decryptData(cls, encrypted_text):
        """Decrypts the given base64-encoded AES CBC encrypted text."""
        try:
            encrypted_data = b64decode(encrypted_text)
            cipher = AES.new(AES_KEY, AES.MODE_CBC, AES_IV)
            decrypted_data = unpad(cipher.decrypt(encrypted_data), AES.block_size)
            return decrypted_data.decode()
        except Exception as e:
            logger.error(f"Decryption Error: {e}", exc_info=True)
            return f"Decryption Error: {e}"


def get_supplier_warehouse_details(supplier_id, oms_url):
    """Fetches warehouse details for a given supplier ID."""
    encrypted = Encdcr.encryptData(f"supplierId={supplier_id}")  # Encrypt

    url = f"{oms_url}api/SupplierWareHouse/GetWarehouseDetailsBySupplierId?{encrypted}"

    try:
        response = requests.get(url)
        response.raise_for_status()

        response_json = response.json()
        encrypted_response = response_json.get("responseData")

        if encrypted_response:
            decrypted_response = Encdcr.decryptData(encrypted_response)
            
            try:
                decrypted_json = json.loads(decrypted_response)
                return decrypted_json
            except json.JSONDecodeError:
                return decrypted_response  # If not JSON, return as is
        else:
            return "❌ No responseData found in API response."
        
    except requests.exceptions.RequestException as e:
        return f"❌ Request Error: {e}"
    except json.JSONDecodeError:
        return "❌ Error: Response is not valid JSON."
