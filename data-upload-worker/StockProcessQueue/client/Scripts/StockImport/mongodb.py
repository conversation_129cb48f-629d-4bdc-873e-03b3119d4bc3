import os
import logging
from typing import List, Optional

import pandas as pd
from pymongo import MongoClient
from pymongo.collection import Collection
from pymongo.results import InsertManyResult

from loggerfile import logger
from config_manager import ConfigManager

# Load configuration
config = ConfigManager()
MONGO_URL = config.get_value("MONGO_CREDENTIAL", "URL")


def db_connection(database_name: str) -> tuple[MongoClient, Collection]:
    """
    Establish a connection to MongoDB and return the client and database.

    Args:
        database_name (str): Name of the MongoDB database.

    Returns:
        Tuple[MongoClient, Database]: MongoDB client and the database object.
    """
    client = MongoClient(MONGO_URL)
    database = client[database_name]
    return client, database


def handle_missing_values(dataframe: pd.DataFrame) -> pd.DataFrame:
    """
    Replace missing values (NaN) in the DataFrame with None for MongoDB compatibility.

    Args:
        dataframe (pd.DataFrame): Input DataFrame.

    Returns:
        pd.DataFrame: Cleaned DataFrame.
    """
    return dataframe.where(pd.notna(dataframe), None)


def insert_data_into_mongodb(
    database_name: str,
    dataframe: pd.DataFrame,
    collection_name: str
) -> Optional[List]:
    """
    Insert data from a pandas DataFrame into a MongoDB collection.
    Drops the collection first if it already contains documents.

    Args:
        database_name (str): MongoDB database name.
        dataframe (pd.DataFrame): DataFrame to insert.
        collection_name (str): Target MongoDB collection name.

    Returns:
        Optional[List]: List of inserted document IDs, or None if insertion fails or input is empty.
    """
    logger.info("Inserting data into MongoDB collection: '%s'", collection_name)

    if dataframe.empty:
        logger.warning("Provided DataFrame is empty. No data to insert.")
        return None

    client = None
    try:
        client, database = db_connection(database_name)
        collection: Collection = database[collection_name]

        # Drop existing data in collection
        if collection.find_one() is not None:
            logger.info("Collection '%s' is not empty. Dropping it before inserting new data.", collection_name)
            collection.drop()

        # Clean DataFrame
        cleaned_df = handle_missing_values(dataframe)
        records = cleaned_df.to_dict(orient='records')

        if not records:
            logger.warning("No valid records found after cleaning. Nothing inserted.")
            return None

        # Insert into MongoDB
        result: InsertManyResult = collection.insert_many(records, ordered=False)
        logger.info("Inserted %d documents into collection '%s'.", len(result.inserted_ids), collection_name)

        return result.inserted_ids

    except Exception as error:
        logger.exception("Error inserting data into MongoDB collection '%s': %s", collection_name, str(error))
        return None

    finally:
        if client:
            client.close()
            logger.debug("MongoDB client connection closed.")


def read_data_from_mongo(database_name: str, collection_name: str) -> pd.DataFrame:
    """
    Read all data from a MongoDB collection into a pandas DataFrame.

    Args:
        database_name (str): MongoDB database name.
        collection_name (str): MongoDB collection name.

    Returns:
        pd.DataFrame: Data from MongoDB as a DataFrame.
    """
    logger.info("Reading data from MongoDB collection: '%s'", collection_name)

    client, database = db_connection(database_name)
    collection: Collection = database[collection_name]

    documents = list(collection.find())
    client.close()
    logger.debug("MongoDB client connection closed after reading.")

    if not documents:
        logger.warning("No documents found in collection '%s'. Returning empty DataFrame.", collection_name)
        return pd.DataFrame()

    dataframe = pd.DataFrame(documents)
    if "_id" in dataframe.columns:
        dataframe["_id"] = dataframe["_id"].astype(str)

    return dataframe
