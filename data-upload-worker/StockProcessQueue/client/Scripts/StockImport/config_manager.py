import configparser
import os

class ConfigManager:
    def __init__(self, config_file='config.cfg', max_levels=4):
        self.config_file = self.find_config_file(config_file, max_levels)
        self.config = configparser.ConfigParser()
        self.config.read(self.config_file)

    @staticmethod
    def find_config_file(filename="config.cfg", max_levels=4):
        """Finds the config file by searching up to 'max_levels' parent directories."""
        current_folder = os.path.dirname(os.path.abspath(__file__))

        for _ in range(max_levels + 1):  # Search up to 'max_levels' parents
            config_path = os.path.join(current_folder, filename)
            if os.path.isfile(config_path):
                return config_path  # Return the first match found
            current_folder = os.path.dirname(current_folder)  # Move one level up

        raise FileNotFoundError(f"{filename} not found within {max_levels} parent directories.")

    def get_value(self, section, key):
        """Retrieve a value from the configuration file."""
        try:
            return self.config.get(section, key)
        except (configparser.NoSectionError, configparser.NoOptionError):
            print(f"Error: Missing [{section}] {key} in configuration file.")
            return None
