import logging
import os
from datetime import datetime


# Create a logger
logger = logging.getLogger('my_logger')
logger.setLevel(logging.INFO)

# Create a StreamHandler to output log messages to stdout
stream_handler = logging.StreamHandler()
stream_handler.setLevel(logging.INFO)

this_folder = os.path.dirname(os.path.abspath(__file__))
scripts_folder = os.path.dirname(this_folder)

# Make a path for logfiles
log_file = os.path.join(scripts_folder, 'Log', f'{datetime.today().strftime("%d-%m-%Y")}.txt')
print(log_file)

# Create a FileHandler to store log messages in a file
file_handler = logging.FileHandler(log_file)
file_handler.setLevel(logging.INFO)

# Create a formatter and add it to the handlers
formatter = logging.Formatter(
    "[%(asctime)s]:[PID %(process)d]:[%(levelname)s]:%(name)s::%(filename)s|%(lineno)d|%(message)s",
    datefmt='%d-%m-%Y %H:%M:%S')
stream_handler.setFormatter(formatter)
file_handler.setFormatter(formatter)

# Add the handlers to the logger
logger.addHandler(stream_handler)
logger.addHandler(file_handler)
