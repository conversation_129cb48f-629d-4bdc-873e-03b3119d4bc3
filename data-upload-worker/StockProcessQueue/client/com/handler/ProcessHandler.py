from client.com.handler.StockImportHandler import StockImportHandler
from client.com.handler.ApiHandler import <PERSON><PERSON><PERSON>andler
from client.com.handler.LoggerHandler import get_logger
from client import AllowedScript, datetime

api_handler = ApiHandler()


class ProcessHandler(StockImportHandler):

    def __init__(self):
        super().__init__()

    def start_process(self, data):
        try:
            self.initialize_request()  # Reset all values before starting a new request
            self.SupplierID = data["SupplierID"]
            self.ImportRequestId = data["ImportRequestId"]
            self.RequestTypeId = data["RequestTypeId"]
            self.Dbname = data["Data"]["DatabaseName"]
            self.CreatedDate = data.get("CreatedDate")
            created_date = datetime.fromisoformat(self.CreatedDate)
            self.CollectionName = data["Data"]["CollectionName"]
            self.ImportTypeId = 4
            self.Script = next((script['ScriptName'] for script in AllowedScript if script['TypeId'] == self.ImportTypeId), None)
            self.request_logger = get_logger(self.ImportRequestId)
            self.request_logger.info(f"Starting {self.Script}")
            response_data = self.start_importing_process()
            return response_data
        except Exception as e:
            self.request_logger.error(e, exc_info=True)
            return
